# Guía de Entrenamiento del Modelo de IA para Reconocimiento de Plantas

Este documento explica cómo entrenar el modelo de IA integrado en el proyecto para reconocer plantas y sus enfermedades.

## Requisitos Previos

Antes de comenzar, asegúrate de tener instaladas todas las dependencias necesarias:

```bash
pip install torch torchvision opencv-python numpy matplotlib scikit-learn pillow
```

## Estructura de Datos para Entrenamiento

Para entrenar el modelo, necesitas organizar tus imágenes de plantas en la siguiente estructura:

```
datos_plantas/
├── Yuca Sana/
│   ├── imagen1.jpg
│   ├── imagen2.jpg
│   └── ...
├── Yuca Pudrición Radicular/
│   ├── imagen1.jpg
│   ├── imagen2.jpg
│   └── ...
├── Mezquite Sano/
│   └── ...
└── ...
```

Cada carpeta debe tener el nombre exacto de la clase (planta y condición) y contener imágenes de esa clase.

## Pasos para Entrenar el Modelo

### 1. Preparar las Imágenes

Recopila imágenes de plantas para cada una de las clases que deseas reconocer. Es recomendable tener al menos 30-50 imágenes por clase para obtener buenos resultados.

Las imágenes deben:
- Estar en formato JPG, JPEG o PNG
- Mostrar claramente la planta o la parte afectada por la enfermedad
- Tener buena iluminación y enfoque
- Tener un tamaño razonable (al menos 300x300 píxeles)

### 2. Ejecutar el Script de Entrenamiento

Usa el script `entrenar_modelo.py` para entrenar el modelo:

```bash
python entrenar_modelo.py --data_dir ruta/a/datos_plantas --epochs 20
```

Parámetros disponibles:
- `--data_dir`: (Obligatorio) Directorio que contiene las imágenes organizadas por clase
- `--processed_dir`: Directorio donde se guardarán las imágenes procesadas (predeterminado: 'data/processed_plant_images')
- `--model_path`: Ruta donde se guardará el modelo entrenado (predeterminado: 'models/plant_disease_model.pth')
- `--class_names_path`: Ruta donde se guardarán los nombres de las clases (predeterminado: 'models/class_names.json')
- `--epochs`: Número de épocas para entrenar (predeterminado: 20)
- `--batch_size`: Tamaño del lote para entrenamiento (predeterminado: 32)
- `--skip_data_prep`: Omitir la preparación de datos si ya están procesados

### 3. Monitorear el Entrenamiento

Durante el entrenamiento, verás información sobre:
- Progreso de la preparación de datos
- Estadísticas del conjunto de datos
- Progreso del entrenamiento por época
- Pérdida y precisión en los conjuntos de entrenamiento y validación

El entrenamiento puede tardar desde minutos hasta horas, dependiendo de:
- La cantidad de imágenes
- El número de clases
- La potencia de tu computadora
- Si tienes una GPU disponible

### 4. Resultados del Entrenamiento

Al finalizar el entrenamiento, se generarán:
1. El modelo entrenado (`models/plant_disease_model.pth`)
2. El archivo de nombres de clases (`models/class_names.json`)
3. Una gráfica del historial de entrenamiento (`models/training_history.png`)

## Uso del Modelo Entrenado

Una vez entrenado, el modelo se utilizará automáticamente en la aplicación para:
- Analizar imágenes subidas a través de la función de escáner
- Identificar plantas y detectar enfermedades
- Proporcionar recomendaciones basadas en el diagnóstico

No es necesario realizar ninguna configuración adicional, ya que la aplicación está diseñada para cargar automáticamente el modelo desde la ubicación predeterminada.

## Solución de Problemas

### El entrenamiento falla con error de memoria

Si encuentras errores de memoria durante el entrenamiento:
- Reduce el tamaño del lote (`--batch_size`)
- Usa menos imágenes por clase
- Entrena en una máquina con más RAM o con GPU

### Baja precisión del modelo

Si el modelo entrenado tiene baja precisión:
- Aumenta el número de imágenes de entrenamiento
- Asegúrate de que las imágenes sean representativas y de buena calidad
- Aumenta el número de épocas de entrenamiento
- Considera usar técnicas de aumento de datos más avanzadas

### El modelo no se carga en la aplicación

Si la aplicación no carga el modelo entrenado:
- Verifica que el modelo se haya guardado en la ubicación correcta (`models/plant_disease_model.pth`)
- Asegúrate de que el archivo de nombres de clases también esté presente
- Revisa los registros de la aplicación para ver mensajes de error específicos

## Mejoras Futuras

Para mejorar el modelo en el futuro, considera:
- Recopilar más imágenes para cada clase
- Añadir más clases de plantas y enfermedades
- Experimentar con diferentes arquitecturas de modelos
- Implementar técnicas de aumento de datos más sofisticadas
- Usar técnicas de aprendizaje por transferencia con modelos pre-entrenados más avanzados
