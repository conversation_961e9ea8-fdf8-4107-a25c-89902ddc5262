# Configuración de MySQL para PlantCare

Este documento explica cómo configurar PlantCare para usar MySQL en lugar de SQLite.

## Requisitos previos

1. Tener MySQL instalado y funcionando en tu sistema
2. Tener las credenciales de acceso a MySQL (usuario y contraseña)
3. Tener permisos para crear bases de datos en MySQL

## Pasos para configurar MySQL

### 1. Instalar dependencias

Asegúrate de tener instaladas las dependencias necesarias:

```bash
pip install PyMySQL cryptography
```

### 2. Configurar las variables de entorno

Puedes configurar las variables de entorno para la conexión a MySQL. Hay dos formas de hacerlo:

#### Opción 1: Variables de entorno del sistema

En Windows:
```
set USE_MYSQL=True
set MYSQL_HOST=localhost
set MYSQL_USER=tu_usuario
set MYSQL_PASSWORD=tu_contraseña
set MYSQL_DB=plantcare
```

En Linux/Mac:
```
export USE_MYSQL=True
export MYSQL_HOST=localhost
export MYSQL_USER=tu_usuario
export MYSQL_PASSWORD=tu_contraseña
export MYSQL_DB=plantcare
```

#### Opción 2: Modificar config.py

Abre el archivo `config.py` y modifica las siguientes líneas:

```python
# Cambiar a True para usar MySQL
USE_MYSQL = True

# Configurar tus credenciales de MySQL
MYSQL_HOST = 'localhost'
MYSQL_USER = 'tu_usuario'
MYSQL_PASSWORD = 'tu_contraseña'
MYSQL_DB = 'plantcare'
```

### 3. Crear la base de datos

Antes de ejecutar la aplicación, necesitas crear la base de datos en MySQL:

1. Accede a MySQL:
   ```
   mysql -u tu_usuario -p
   ```

2. Crea la base de datos:
   ```sql
   CREATE DATABASE plantcare CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

3. Salir de MySQL:
   ```sql
   EXIT;
   ```

### 4. Inicializar las tablas

Puedes inicializar las tablas ejecutando la aplicación normalmente:

```bash
python app.py
```

La aplicación creará automáticamente las tablas necesarias en la base de datos MySQL.

## Solución de problemas

### Error de conexión a MySQL

Si recibes un error como "Access denied for user", verifica:

1. Que el usuario y contraseña sean correctos
2. Que el usuario tenga permisos para acceder a la base de datos
3. Que MySQL esté ejecutándose

### Error al crear tablas

Si hay problemas al crear las tablas:

1. Verifica que la base de datos exista
2. Asegúrate de que el usuario tenga permisos para crear tablas
3. Revisa los logs para ver errores específicos

## Volver a SQLite

Si necesitas volver a usar SQLite, simplemente cambia `USE_MYSQL` a `False` en `config.py` o en las variables de entorno.

## Migración de datos

Para migrar datos de SQLite a MySQL, puedes usar herramientas como:

1. Flask-Migrate
2. Exportar/importar datos manualmente
3. Usar scripts personalizados de migración

Nota: La migración de datos no está incluida en este proyecto y requiere pasos adicionales.
