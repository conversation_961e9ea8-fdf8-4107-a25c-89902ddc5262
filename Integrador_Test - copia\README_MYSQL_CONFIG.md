# Configuración de MySQL para PlantCare

Este documento explica cómo se ha configurado PlantCare para usar MySQL como base de datos.

## Configuración actual

La aplicación está configurada para usar MySQL con los siguientes parámetros:

- **Host**: localhost
- **Usuario**: root
- **Contraseña**: 12345678
- **Base de datos**: PlantcareDB

## Archivos modificados

1. **config.py**: Contiene la configuración de la conexión a MySQL.
2. **database.py**: Contiene la lógica para conectarse a la base de datos.
3. **test_mysql_connection.py**: Script para probar la conexión a MySQL.
4. **initialize_db.py**: Script para inicializar las tablas en la base de datos.

## Tablas creadas

Se han creado las siguientes tablas en la base de datos:

- **usuarios**: Almacena la información de los usuarios.
- **configuracionesusuario**: Almacena la configuración de cada usuario.
- **cattipousuario**: Catálogo de tipos de usuario.
- **plantasusuario**: Almacena las plantas de cada usuario.
- **cuidadosplantas**: Almacena la información de cuidados de cada planta.
- **cattipoplanta**: Catálogo de tipos de plantas.
- **catfamiliabotanica**: Catálogo de familias botánicas.
- **catestadosalud**: Catálogo de estados de salud de las plantas.
- **cattiposuelo**: Catálogo de tipos de suelo.
- **catcondicionluz**: Catálogo de condiciones de luz.
- **recordatorios**: Almacena los recordatorios de cuidados.
- **diagnosticos**: Almacena los diagnósticos de las plantas.
- **catenfermedad**: Catálogo de enfermedades.
- **temasforo**: Almacena los temas del foro.
- **respuestasforo**: Almacena las respuestas a los temas del foro.
- **eventostalleres**: Almacena la información de eventos y talleres.
- **registroeventos**: Almacena los registros a eventos.
- **tiendas**: Almacena la información de tiendas.
- Y otras tablas relacionadas.

## Cómo cambiar la configuración

Si necesitas cambiar la configuración de la conexión a MySQL, debes modificar el archivo `config.py`:

```python
# Configuración MySQL con tus credenciales
MYSQL_HOST = 'localhost'  # Cambia si tu servidor MySQL está en otro host
MYSQL_USER = 'root'       # Cambia si usas otro usuario
MYSQL_PASSWORD = '12345678'  # Cambia a tu contraseña
MYSQL_DB = 'PlantcareDB'  # Cambia si usas otro nombre de base de datos
```

## Cómo verificar la conexión

Puedes verificar la conexión a MySQL ejecutando el script `test_mysql_connection.py`:

```bash
python test_mysql_connection.py
```

Este script intentará conectarse a MySQL y mostrará información sobre la conexión y las tablas existentes.

## Cómo inicializar la base de datos

Si necesitas reinicializar la base de datos, puedes ejecutar el script `initialize_db.py`:

```bash
python initialize_db.py
```

Este script creará todas las tablas necesarias para la aplicación.

## Solución de problemas

### Error de conexión

Si recibes un error como "Access denied for user", verifica:

1. Que el usuario y contraseña sean correctos.
2. Que el usuario tenga permisos para acceder a la base de datos.
3. Que MySQL esté ejecutándose.

### Error al crear tablas

Si hay problemas al crear las tablas:

1. Verifica que la base de datos exista.
2. Asegúrate de que el usuario tenga permisos para crear tablas.
3. Revisa los logs para ver errores específicos.

## Respaldo y restauración

### Respaldo de la base de datos

Para hacer un respaldo de la base de datos, puedes usar el siguiente comando:

```bash
mysqldump -u root -p PlantcareDB > plantcare_backup.sql
```

### Restauración de la base de datos

Para restaurar la base de datos desde un respaldo:

```bash
mysql -u root -p PlantcareDB < plantcare_backup.sql
```

## Notas adicionales

- La aplicación usa SQLAlchemy como ORM para interactuar con la base de datos.
- Los modelos de la base de datos están definidos en el archivo `models/models.py`.
- La aplicación crea automáticamente las tablas si no existen al iniciar.
