# 🧠 Guía Completa para Entrenar la IA de Diagnóstico de Plantas

## 📋 Índice
1. [Preparación del Entorno](#preparación-del-entorno)
2. [Obtención de Datos](#obtención-de-datos)
3. [Preparación del Dataset](#preparación-del-dataset)
4. [Entrenamiento del Modelo](#entrenamiento-del-modelo)
5. [Evaluación y Optimización](#evaluación-y-optimización)
6. [Despliegue](#despliegue)
7. [Mantenimiento](#mantenimiento)

## 🛠️ Preparación del Entorno

### 1. Instalar Dependencias

```bash
# Navegar al directorio del proyecto
cd "Integrador_Test - copia/ai_diagnosis"

# Instalar dependencias de IA
pip install -r requirements_ai.txt

# Para GPU (opcional pero recomendado)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

### 2. Verificar Hardware

```python
import torch
print(f"CUDA disponible: {torch.cuda.is_available()}")
print(f"Dispositivo: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'CPU'}")
```

## 📊 Obtención de Datos

### Datasets Recomendados

#### 1. **PlantVillage Dataset** (Recomendado)
- **URL**: https://www.kaggle.com/emmarex/plantdisease
- **Tamaño**: ~1.2GB
- **Clases**: 38 clases (15 plantas, 26 enfermedades)
- **Imágenes**: ~54,000 imágenes

#### 2. **Plant Pathology 2020**
- **URL**: https://www.kaggle.com/c/plant-pathology-2020-fgvc7
- **Enfoque**: Manzanas específicamente
- **Calidad**: Muy alta

#### 3. **New Plant Diseases Dataset**
- **URL**: https://www.kaggle.com/vipoooool/new-plant-diseases-dataset
- **Ventaja**: Datos aumentados incluidos

### Estructura de Datos Requerida

```
data/
├── train/
│   ├── Apple___Apple_scab/
│   │   ├── imagen1.jpg
│   │   ├── imagen2.jpg
│   │   └── ...
│   ├── Apple___Black_rot/
│   │   ├── imagen1.jpg
│   │   └── ...
│   └── ...
├── validation/
│   ├── Apple___Apple_scab/
│   └── ...
└── test/
    ├── Apple___Apple_scab/
    └── ...
```

## 🔧 Preparación del Dataset

### 1. Script de Descarga Automática

```python
# download_dataset.py
import kaggle
import zipfile
import os

def download_plantvillage():
    """Descargar dataset PlantVillage"""
    
    # Configurar API de Kaggle (requiere kaggle.json)
    kaggle.api.authenticate()
    
    # Descargar dataset
    kaggle.api.dataset_download_files(
        'emmarex/plantdisease',
        path='data/',
        unzip=True
    )
    
    print("Dataset descargado exitosamente!")

if __name__ == "__main__":
    download_plantvillage()
```

### 2. Preprocesamiento de Datos

```python
# preprocess_data.py
import os
import shutil
from sklearn.model_selection import train_test_split
import random

def organize_dataset(source_dir, target_dir, train_ratio=0.8, val_ratio=0.1):
    """Organizar dataset en train/val/test"""
    
    os.makedirs(f"{target_dir}/train", exist_ok=True)
    os.makedirs(f"{target_dir}/validation", exist_ok=True)
    os.makedirs(f"{target_dir}/test", exist_ok=True)
    
    for class_name in os.listdir(source_dir):
        class_path = os.path.join(source_dir, class_name)
        if not os.path.isdir(class_path):
            continue
        
        # Obtener todas las imágenes
        images = [f for f in os.listdir(class_path) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        random.shuffle(images)
        
        # Dividir datos
        train_size = int(len(images) * train_ratio)
        val_size = int(len(images) * val_ratio)
        
        train_images = images[:train_size]
        val_images = images[train_size:train_size + val_size]
        test_images = images[train_size + val_size:]
        
        # Crear directorios de clase
        for split in ['train', 'validation', 'test']:
            os.makedirs(f"{target_dir}/{split}/{class_name}", exist_ok=True)
        
        # Copiar imágenes
        for img in train_images:
            shutil.copy2(
                os.path.join(class_path, img),
                f"{target_dir}/train/{class_name}/{img}"
            )
        
        for img in val_images:
            shutil.copy2(
                os.path.join(class_path, img),
                f"{target_dir}/validation/{class_name}/{img}"
            )
        
        for img in test_images:
            shutil.copy2(
                os.path.join(class_path, img),
                f"{target_dir}/test/{class_name}/{img}"
            )
        
        print(f"{class_name}: {len(train_images)} train, {len(val_images)} val, {len(test_images)} test")

if __name__ == "__main__":
    organize_dataset("data/PlantVillage", "data/organized")
```

## 🚀 Entrenamiento del Modelo

### 1. Entrenamiento Básico

```python
from train_model import PlantTrainer

# Crear entrenador
trainer = PlantTrainer(
    data_dir="data/organized/train",
    model_name='efficientnet_b0',
    batch_size=32,
    learning_rate=0.001
)

# Preparar datos
trainer.prepare_data(train_split=0.9)

# Entrenar modelo
trainer.train(
    num_epochs=50,
    save_path='models/plant_disease_model_v1.pth'
)
```

### 2. Entrenamiento Avanzado con Transfer Learning

```python
import torch
import torch.nn as nn
from torchvision import models

class AdvancedPlantClassifier(nn.Module):
    def __init__(self, num_classes, pretrained=True):
        super().__init__()
        
        # Usar EfficientNet pre-entrenado
        self.backbone = models.efficientnet_b3(pretrained=pretrained)
        
        # Congelar capas iniciales
        for param in list(self.backbone.parameters())[:-10]:
            param.requires_grad = False
        
        # Reemplazar clasificador
        self.backbone.classifier = nn.Sequential(
            nn.Dropout(0.3),
            nn.Linear(self.backbone.classifier[1].in_features, 512),
            nn.ReLU(),
            nn.BatchNorm1d(512),
            nn.Dropout(0.4),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.BatchNorm1d(256),
            nn.Dropout(0.2),
            nn.Linear(256, num_classes)
        )
    
    def forward(self, x):
        return self.backbone(x)
```

### 3. Configuración de Entrenamiento Optimizada

```python
# Configuración avanzada
config = {
    'model_name': 'efficientnet_b3',
    'batch_size': 16,  # Reducir si hay problemas de memoria
    'learning_rate': 0.0001,
    'weight_decay': 1e-4,
    'num_epochs': 100,
    'patience': 15,  # Para early stopping
    'scheduler': 'cosine',
    'augmentation': 'heavy'
}

# Optimizador con scheduler
optimizer = torch.optim.AdamW(
    model.parameters(),
    lr=config['learning_rate'],
    weight_decay=config['weight_decay']
)

scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
    optimizer,
    T_max=config['num_epochs']
)
```

## 📈 Evaluación y Optimización

### 1. Métricas de Evaluación

```python
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns

def evaluate_model(model, test_loader, class_names):
    """Evaluar modelo en datos de prueba"""
    
    model.eval()
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for images, labels in test_loader:
            outputs = model(images.to(device))
            _, preds = torch.max(outputs, 1)
            
            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    # Reporte de clasificación
    report = classification_report(
        all_labels, all_preds,
        target_names=class_names,
        output_dict=True
    )
    
    # Matriz de confusión
    cm = confusion_matrix(all_labels, all_preds)
    
    # Visualizar matriz de confusión
    plt.figure(figsize=(15, 12))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=class_names,
                yticklabels=class_names)
    plt.title('Matriz de Confusión')
    plt.ylabel('Etiqueta Real')
    plt.xlabel('Predicción')
    plt.xticks(rotation=45)
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig('confusion_matrix.png', dpi=300)
    plt.show()
    
    return report
```

### 2. Optimización de Hiperparámetros

```python
import optuna

def objective(trial):
    """Función objetivo para optimización con Optuna"""
    
    # Sugerir hiperparámetros
    lr = trial.suggest_float('lr', 1e-5, 1e-2, log=True)
    batch_size = trial.suggest_categorical('batch_size', [16, 32, 64])
    dropout = trial.suggest_float('dropout', 0.1, 0.5)
    
    # Entrenar modelo con hiperparámetros sugeridos
    trainer = PlantTrainer(
        data_dir="data/organized/train",
        batch_size=batch_size,
        learning_rate=lr
    )
    
    # Entrenar por pocas épocas para prueba rápida
    accuracy = trainer.train(num_epochs=10, return_accuracy=True)
    
    return accuracy

# Ejecutar optimización
study = optuna.create_study(direction='maximize')
study.optimize(objective, n_trials=50)

print(f"Mejores hiperparámetros: {study.best_params}")
```

## 🚀 Despliegue

### 1. Guardar Modelo para Producción

```python
def save_production_model(model, class_names, save_path):
    """Guardar modelo para producción"""
    
    torch.save({
        'model_state_dict': model.state_dict(),
        'class_names': class_names,
        'model_architecture': 'efficientnet_b3',
        'input_size': (224, 224),
        'mean': [0.485, 0.456, 0.406],
        'std': [0.229, 0.224, 0.225]
    }, save_path)
    
    print(f"Modelo guardado en: {save_path}")
```

### 2. Iniciar API de Diagnóstico

```bash
# Iniciar API
cd ai_diagnosis
python diagnosis_api.py
```

### 3. Probar API

```python
import requests

# Probar API
response = requests.get("http://127.0.0.1:8000/health")
print(response.json())

# Probar diagnóstico
with open("test_image.jpg", "rb") as f:
    files = {"file": f}
    response = requests.post("http://127.0.0.1:8000/diagnose", files=files)
    print(response.json())
```

## 🔄 Mantenimiento

### 1. Reentrenamiento Periódico

```python
def retrain_model(new_data_path, existing_model_path):
    """Reentrenar modelo con nuevos datos"""
    
    # Cargar modelo existente
    model = PlantDiseaseClassifier(num_classes=38)
    model.load_state_dict(torch.load(existing_model_path))
    
    # Preparar nuevos datos
    trainer = PlantTrainer(new_data_path)
    trainer.model = model  # Usar modelo existente
    
    # Entrenar con learning rate reducido
    trainer.learning_rate = 0.0001
    trainer.train(num_epochs=20)
```

### 2. Monitoreo de Rendimiento

```python
def monitor_predictions(predictions_log):
    """Monitorear predicciones para detectar drift"""
    
    # Analizar confianza promedio
    avg_confidence = np.mean([p['confidence'] for p in predictions_log])
    
    if avg_confidence < 0.7:
        print("⚠️ Confianza baja detectada - considerar reentrenamiento")
    
    # Analizar distribución de clases
    class_counts = {}
    for p in predictions_log:
        class_name = p['plant_name']
        class_counts[class_name] = class_counts.get(class_name, 0) + 1
    
    print(f"Distribución de predicciones: {class_counts}")
```

## 📊 Métricas de Éxito

### Objetivos de Rendimiento:
- **Precisión General**: >85%
- **Precisión por Clase**: >80% para clases principales
- **Tiempo de Inferencia**: <3 segundos
- **Confianza Promedio**: >75%

### Monitoreo Continuo:
- Revisar métricas semanalmente
- Reentrenar mensualmente con nuevos datos
- Actualizar modelo cuando precisión baje <80%

## 🎯 Próximos Pasos

1. **Implementar el entrenamiento** con el dataset PlantVillage
2. **Evaluar el modelo** en datos de prueba
3. **Optimizar hiperparámetros** para mejor rendimiento
4. **Desplegar la API** de diagnóstico
5. **Integrar con la aplicación web**
6. **Recopilar feedback** de usuarios para mejoras

¡Con esta guía tendrás un sistema de IA completamente funcional para diagnóstico de plantas!
