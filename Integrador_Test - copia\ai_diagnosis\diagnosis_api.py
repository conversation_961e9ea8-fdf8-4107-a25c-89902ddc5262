"""
API FastAPI para el sistema de diagnóstico de plantas
"""

from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import os
import tempfile
import shutil
from PIL import Image
import io
import base64
from typing import Dict, Optional

from plant_disease_model import PlantDiagnosisSystem, diagnose_plant_image

# Crear aplicación FastAPI
app = FastAPI(
    title="Plant Diagnosis API",
    description="API para diagnóstico de enfermedades en plantas usando IA",
    version="1.0.0"
)

# Configurar CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Inicializar sistema de diagnóstico
diagnosis_system = PlantDiagnosisSystem()

@app.get("/")
async def root():
    """Endpoint raíz"""
    return {
        "message": "Plant Diagnosis API",
        "version": "1.0.0",
        "status": "active",
        "endpoints": {
            "diagnose": "/diagnose",
            "diagnose_base64": "/diagnose/base64",
            "health": "/health"
        }
    }

@app.get("/health")
async def health_check():
    """Verificar estado de la API"""
    return {
        "status": "healthy",
        "model_loaded": diagnosis_system.model is not None,
        "device": str(diagnosis_system.device)
    }

@app.post("/diagnose")
async def diagnose_plant(file: UploadFile = File(...)):
    """
    Diagnosticar planta desde archivo de imagen
    """
    try:
        # Validar tipo de archivo
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="El archivo debe ser una imagen")
        
        # Crear archivo temporal
        with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as temp_file:
            # Copiar contenido del archivo subido
            shutil.copyfileobj(file.file, temp_file)
            temp_path = temp_file.name
        
        try:
            # Realizar diagnóstico
            result = diagnosis_system.predict(temp_path)
            
            # Agregar metadatos
            result.update({
                "filename": file.filename,
                "file_size": file.size if hasattr(file, 'size') else None,
                "api_version": "1.0.0"
            })
            
            return JSONResponse(content=result)
            
        finally:
            # Limpiar archivo temporal
            if os.path.exists(temp_path):
                os.unlink(temp_path)
                
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error procesando imagen: {str(e)}")

@app.post("/diagnose/base64")
async def diagnose_plant_base64(data: Dict):
    """
    Diagnosticar planta desde imagen en base64
    """
    try:
        # Extraer datos
        image_data = data.get('image')
        filename = data.get('filename', 'image.jpg')
        
        if not image_data:
            raise HTTPException(status_code=400, detail="No se proporcionó imagen en base64")
        
        # Decodificar base64
        try:
            # Remover prefijo si existe (data:image/jpeg;base64,)
            if ',' in image_data:
                image_data = image_data.split(',')[1]
            
            image_bytes = base64.b64decode(image_data)
            image = Image.open(io.BytesIO(image_bytes))
            
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"Error decodificando imagen base64: {str(e)}")
        
        # Crear archivo temporal
        with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as temp_file:
            image.save(temp_file.name, 'JPEG')
            temp_path = temp_file.name
        
        try:
            # Realizar diagnóstico
            result = diagnosis_system.predict(temp_path)
            
            # Agregar metadatos
            result.update({
                "filename": filename,
                "api_version": "1.0.0",
                "input_format": "base64"
            })
            
            return JSONResponse(content=result)
            
        finally:
            # Limpiar archivo temporal
            if os.path.exists(temp_path):
                os.unlink(temp_path)
                
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error procesando imagen: {str(e)}")

@app.get("/classes")
async def get_classes():
    """Obtener lista de clases que puede detectar el modelo"""
    return {
        "classes": diagnosis_system.class_names,
        "total_classes": len(diagnosis_system.class_names)
    }

@app.post("/batch_diagnose")
async def batch_diagnose(files: list[UploadFile] = File(...)):
    """
    Diagnosticar múltiples plantas
    """
    if len(files) > 10:
        raise HTTPException(status_code=400, detail="Máximo 10 imágenes por lote")
    
    results = []
    
    for i, file in enumerate(files):
        try:
            # Validar tipo de archivo
            if not file.content_type.startswith('image/'):
                results.append({
                    "filename": file.filename,
                    "error": "El archivo debe ser una imagen"
                })
                continue
            
            # Crear archivo temporal
            with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as temp_file:
                shutil.copyfileobj(file.file, temp_file)
                temp_path = temp_file.name
            
            try:
                # Realizar diagnóstico
                result = diagnosis_system.predict(temp_path)
                result.update({
                    "filename": file.filename,
                    "batch_index": i
                })
                results.append(result)
                
            finally:
                # Limpiar archivo temporal
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                    
        except Exception as e:
            results.append({
                "filename": file.filename,
                "batch_index": i,
                "error": str(e)
            })
    
    return {
        "results": results,
        "total_processed": len(results),
        "api_version": "1.0.0"
    }

@app.get("/model/info")
async def get_model_info():
    """Obtener información del modelo"""
    return {
        "model_loaded": diagnosis_system.model is not None,
        "device": str(diagnosis_system.device),
        "classes_count": len(diagnosis_system.class_names),
        "model_architecture": diagnosis_system.model.model_name if diagnosis_system.model else None,
        "input_size": "224x224",
        "supported_formats": ["JPEG", "PNG", "JPG"]
    }

# Función para ejecutar la API
def run_api(host="127.0.0.1", port=8000, reload=True):
    """Ejecutar la API"""
    uvicorn.run(
        "diagnosis_api:app",
        host=host,
        port=port,
        reload=reload,
        log_level="info"
    )

if __name__ == "__main__":
    print("🚀 Iniciando API de Diagnóstico de Plantas...")
    print("📡 API disponible en: http://127.0.0.1:8000")
    print("📚 Documentación en: http://127.0.0.1:8000/docs")
    
    run_api()
