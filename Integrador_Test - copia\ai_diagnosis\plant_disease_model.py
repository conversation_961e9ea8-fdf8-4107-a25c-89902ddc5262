"""
Modelo de IA para diagnóstico de enfermedades en plantas
"""

import torch
import torch.nn as nn
import torchvision.transforms as transforms
from torchvision import models
import cv2
import numpy as np
from PIL import Image
import json
import os
from typing import Dict, List, Tuple, Optional

class PlantDiseaseClassifier(nn.Module):
    """
    Modelo de clasificación de enfermedades en plantas usando CNN
    """
    
    def __init__(self, num_classes: int = 38, model_name: str = 'efficientnet_b0'):
        super(PlantDiseaseClassifier, self).__init__()
        
        self.num_classes = num_classes
        self.model_name = model_name
        
        # Cargar modelo pre-entrenado
        if model_name == 'efficientnet_b0':
            self.backbone = models.efficientnet_b0(pretrained=True)
            self.backbone.classifier = nn.Sequential(
                nn.Dropout(0.2),
                nn.Linear(self.backbone.classifier[1].in_features, 512),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(512, num_classes)
            )
        elif model_name == 'resnet50':
            self.backbone = models.resnet50(pretrained=True)
            self.backbone.fc = nn.Sequential(
                nn.Dropout(0.2),
                nn.Linear(self.backbone.fc.in_features, 512),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(512, num_classes)
            )
        else:
            raise ValueError(f"Modelo {model_name} no soportado")
    
    def forward(self, x):
        return self.backbone(x)

class PlantDiagnosisSystem:
    """
    Sistema completo de diagnóstico de plantas
    """
    
    def __init__(self, model_path: Optional[str] = None):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = None
        self.class_names = self._load_class_names()
        self.transform = self._get_transforms()
        
        if model_path and os.path.exists(model_path):
            self.load_model(model_path)
        else:
            # Usar modelo pre-entrenado básico
            self.model = PlantDiseaseClassifier(len(self.class_names))
            self.model.to(self.device)
    
    def _load_class_names(self) -> List[str]:
        """Cargar nombres de clases de enfermedades"""
        # Clases basadas en el dataset PlantVillage
        return [
            'Apple___Apple_scab',
            'Apple___Black_rot',
            'Apple___Cedar_apple_rust',
            'Apple___healthy',
            'Blueberry___healthy',
            'Cherry_(including_sour)___Powdery_mildew',
            'Cherry_(including_sour)___healthy',
            'Corn_(maize)___Cercospora_leaf_spot Gray_leaf_spot',
            'Corn_(maize)___Common_rust_',
            'Corn_(maize)___Northern_Leaf_Blight',
            'Corn_(maize)___healthy',
            'Grape___Black_rot',
            'Grape___Esca_(Black_Measles)',
            'Grape___Leaf_blight_(Isariopsis_Leaf_Spot)',
            'Grape___healthy',
            'Orange___Haunglongbing_(Citrus_greening)',
            'Peach___Bacterial_spot',
            'Peach___healthy',
            'Pepper,_bell___Bacterial_spot',
            'Pepper,_bell___healthy',
            'Potato___Early_blight',
            'Potato___Late_blight',
            'Potato___healthy',
            'Raspberry___healthy',
            'Soybean___healthy',
            'Squash___Powdery_mildew',
            'Strawberry___Leaf_scorch',
            'Strawberry___healthy',
            'Tomato___Bacterial_spot',
            'Tomato___Early_blight',
            'Tomato___Late_blight',
            'Tomato___Leaf_Mold',
            'Tomato___Septoria_leaf_spot',
            'Tomato___Spider_mites Two-spotted_spider_mite',
            'Tomato___Target_Spot',
            'Tomato___Tomato_Yellow_Leaf_Curl_Virus',
            'Tomato___Tomato_mosaic_virus',
            'Tomato___healthy'
        ]
    
    def _get_transforms(self):
        """Transformaciones para preprocesar imágenes"""
        return transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            )
        ])
    
    def preprocess_image(self, image_path: str) -> torch.Tensor:
        """Preprocesar imagen para el modelo"""
        try:
            # Cargar imagen
            if isinstance(image_path, str):
                image = Image.open(image_path).convert('RGB')
            else:
                image = image_path.convert('RGB')
            
            # Aplicar transformaciones
            image_tensor = self.transform(image).unsqueeze(0)
            return image_tensor.to(self.device)
            
        except Exception as e:
            raise ValueError(f"Error procesando imagen: {e}")
    
    def predict(self, image_path: str) -> Dict:
        """Realizar predicción de enfermedad"""
        if self.model is None:
            return self._mock_prediction()
        
        try:
            # Preprocesar imagen
            image_tensor = self.preprocess_image(image_path)
            
            # Realizar predicción
            self.model.eval()
            with torch.no_grad():
                outputs = self.model(image_tensor)
                probabilities = torch.nn.functional.softmax(outputs, dim=1)
                confidence, predicted = torch.max(probabilities, 1)
            
            # Obtener clase predicha
            predicted_class = self.class_names[predicted.item()]
            confidence_score = confidence.item()
            
            # Parsear resultado
            plant_name, condition = self._parse_prediction(predicted_class)
            
            # Generar diagnóstico
            diagnosis = self._generate_diagnosis(plant_name, condition, confidence_score)
            
            return diagnosis
            
        except Exception as e:
            return {
                'error': f"Error en predicción: {e}",
                'plant_name': 'Desconocida',
                'condition': 'Error',
                'confidence': 0.0,
                'diagnosis': 'No se pudo procesar la imagen',
                'recommendations': ['Intenta con una imagen más clara']
            }
    
    def _parse_prediction(self, predicted_class: str) -> Tuple[str, str]:
        """Parsear clase predicha en planta y condición"""
        parts = predicted_class.split('___')
        if len(parts) >= 2:
            plant_name = parts[0].replace('_', ' ')
            condition = parts[1].replace('_', ' ')
        else:
            plant_name = predicted_class.replace('_', ' ')
            condition = 'Desconocida'
        
        return plant_name, condition
    
    def _generate_diagnosis(self, plant_name: str, condition: str, confidence: float) -> Dict:
        """Generar diagnóstico completo"""
        
        # Base del diagnóstico
        diagnosis = {
            'plant_name': plant_name,
            'condition': condition,
            'confidence': round(confidence * 100, 2),
            'severity': self._assess_severity(condition, confidence),
            'diagnosis': '',
            'recommendations': [],
            'treatment': [],
            'prevention': []
        }
        
        # Generar diagnóstico específico
        if 'healthy' in condition.lower():
            diagnosis.update({
                'diagnosis': f'Tu {plant_name} parece estar saludable.',
                'recommendations': [
                    'Continúa con el cuidado actual',
                    'Mantén un riego adecuado',
                    'Asegúrate de que reciba suficiente luz',
                    'Revisa periódicamente por signos de enfermedad'
                ],
                'treatment': ['No se requiere tratamiento'],
                'prevention': [
                    'Mantén buena ventilación',
                    'Evita el exceso de humedad',
                    'Fertiliza según las necesidades de la planta'
                ]
            })
        else:
            diagnosis.update({
                'diagnosis': f'Se detectó {condition} en tu {plant_name}.',
                'recommendations': self._get_disease_recommendations(condition),
                'treatment': self._get_treatment_plan(condition),
                'prevention': self._get_prevention_tips(condition)
            })
        
        return diagnosis
    
    def _assess_severity(self, condition: str, confidence: float) -> str:
        """Evaluar severidad de la condición"""
        if 'healthy' in condition.lower():
            return 'Saludable'
        elif confidence > 0.8:
            return 'Alta confianza'
        elif confidence > 0.6:
            return 'Confianza moderada'
        else:
            return 'Baja confianza - requiere verificación'
    
    def _get_disease_recommendations(self, condition: str) -> List[str]:
        """Obtener recomendaciones específicas por enfermedad"""
        recommendations_db = {
            'scab': [
                'Mejora la circulación de aire',
                'Evita regar las hojas',
                'Aplica fungicida preventivo'
            ],
            'rust': [
                'Retira hojas afectadas',
                'Mejora la ventilación',
                'Aplica fungicida específico para roya'
            ],
            'blight': [
                'Retira inmediatamente las partes afectadas',
                'Mejora el drenaje del suelo',
                'Aplica fungicida sistémico'
            ],
            'spot': [
                'Retira hojas manchadas',
                'Evita regar por aspersión',
                'Aplica fungicida preventivo'
            ]
        }
        
        condition_lower = condition.lower()
        for key, recommendations in recommendations_db.items():
            if key in condition_lower:
                return recommendations
        
        return [
            'Consulta con un especialista en plantas',
            'Aísla la planta si es posible',
            'Mejora las condiciones de crecimiento'
        ]
    
    def _get_treatment_plan(self, condition: str) -> List[str]:
        """Obtener plan de tratamiento"""
        if 'healthy' in condition.lower():
            return ['No se requiere tratamiento']
        
        return [
            'Retira partes afectadas con herramientas limpias',
            'Aplica tratamiento fungicida apropiado',
            'Mejora las condiciones ambientales',
            'Monitorea el progreso semanalmente'
        ]
    
    def _get_prevention_tips(self, condition: str) -> List[str]:
        """Obtener consejos de prevención"""
        return [
            'Mantén buena circulación de aire',
            'Evita el exceso de humedad',
            'Inspecciona regularmente las plantas',
            'Usa herramientas limpias para el mantenimiento',
            'Mantén un programa de fertilización adecuado'
        ]
    
    def _mock_prediction(self) -> Dict:
        """Predicción simulada para desarrollo"""
        return {
            'plant_name': 'Planta Detectada',
            'condition': 'Análisis en Desarrollo',
            'confidence': 75.0,
            'severity': 'Sistema en Desarrollo',
            'diagnosis': 'El sistema de IA está en desarrollo. Esta es una respuesta simulada.',
            'recommendations': [
                'El sistema de diagnóstico está siendo entrenado',
                'Pronto estará disponible el análisis completo',
                'Por ahora, consulta con un especialista'
            ],
            'treatment': ['Sistema en desarrollo'],
            'prevention': ['Mantén cuidados básicos de la planta']
        }
    
    def load_model(self, model_path: str):
        """Cargar modelo entrenado"""
        try:
            self.model = PlantDiseaseClassifier(len(self.class_names))
            self.model.load_state_dict(torch.load(model_path, map_location=self.device))
            self.model.to(self.device)
            self.model.eval()
            print(f"Modelo cargado desde: {model_path}")
        except Exception as e:
            print(f"Error cargando modelo: {e}")
            self.model = None
    
    def save_model(self, model_path: str):
        """Guardar modelo entrenado"""
        if self.model is not None:
            torch.save(self.model.state_dict(), model_path)
            print(f"Modelo guardado en: {model_path}")

# Función de utilidad para usar el sistema
def diagnose_plant_image(image_path: str, model_path: Optional[str] = None) -> Dict:
    """
    Función principal para diagnosticar una imagen de planta

    Args:
        image_path: Ruta a la imagen de la planta
        model_path: Ruta al modelo entrenado (opcional)

    Returns:
        Diccionario con el diagnóstico completo
    """
    system = PlantDiagnosisSystem(model_path)
    return system.predict(image_path)

if __name__ == "__main__":
    # Ejemplo de uso
    system = PlantDiagnosisSystem()

    # Simular diagnóstico
    result = system._mock_prediction()
    print("Ejemplo de diagnóstico:")
    print(json.dumps(result, indent=2, ensure_ascii=False))
