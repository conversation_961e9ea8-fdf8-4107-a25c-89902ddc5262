# Dependencias para el sistema de IA de diagnóstico de plantas

# Frameworks de Machine Learning
tensorflow==2.13.0
torch==2.0.1
torchvision==0.15.2
transformers==4.30.0

# Procesamiento de imágenes
opencv-python==********
Pillow==10.0.0
scikit-image==0.21.0
albumentations==1.3.1

# Análisis de <PERSON>
numpy==1.24.3
pandas==2.0.3
matplotlib==3.7.1
seaborn==0.12.2

# APIs y servicios
requests==2.31.0
fastapi==0.100.0
uvicorn==0.22.0

# Utilidades
tqdm==4.65.0
python-multipart==0.0.6
aiofiles==23.1.0

# Modelos pre-entrenados
timm==0.9.2
efficientnet-pytorch==0.7.1

# Opcional: Para usar modelos de Hugging Face
datasets==2.13.0
accelerate==0.20.3
