"""
Sistema de entrenamiento para el modelo de diagnóstico de plantas
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torchvision.transforms as transforms
from torchvision import datasets
import os
import json
from PIL import Image
import numpy as np
from tqdm import tqdm
import matplotlib.pyplot as plt
from sklearn.metrics import classification_report, confusion_matrix
import seaborn as sns

from plant_disease_model import PlantDiseaseClassifier

class PlantDataset(Dataset):
    """Dataset personalizado para imágenes de plantas"""
    
    def __init__(self, data_dir, transform=None, class_names=None):
        self.data_dir = data_dir
        self.transform = transform
        self.images = []
        self.labels = []
        self.class_names = class_names or []
        
        self._load_data()
    
    def _load_data(self):
        """Cargar datos del directorio"""
        if not os.path.exists(self.data_dir):
            print(f"Directorio {self.data_dir} no existe")
            return
        
        for class_idx, class_name in enumerate(os.listdir(self.data_dir)):
            class_path = os.path.join(self.data_dir, class_name)
            if os.path.isdir(class_path):
                if class_name not in self.class_names:
                    self.class_names.append(class_name)
                
                for img_name in os.listdir(class_path):
                    if img_name.lower().endswith(('.png', '.jpg', '.jpeg')):
                        img_path = os.path.join(class_path, img_name)
                        self.images.append(img_path)
                        self.labels.append(self.class_names.index(class_name))
    
    def __len__(self):
        return len(self.images)
    
    def __getitem__(self, idx):
        img_path = self.images[idx]
        label = self.labels[idx]
        
        try:
            image = Image.open(img_path).convert('RGB')
            if self.transform:
                image = self.transform(image)
            return image, label
        except Exception as e:
            print(f"Error cargando imagen {img_path}: {e}")
            # Retornar imagen en blanco en caso de error
            blank_image = Image.new('RGB', (224, 224), color='white')
            if self.transform:
                blank_image = self.transform(blank_image)
            return blank_image, label

class PlantTrainer:
    """Entrenador para el modelo de diagnóstico de plantas"""
    
    def __init__(self, data_dir, model_name='efficientnet_b0', batch_size=32, learning_rate=0.001):
        self.data_dir = data_dir
        self.model_name = model_name
        self.batch_size = batch_size
        self.learning_rate = learning_rate
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Transformaciones de datos
        self.train_transform = transforms.Compose([
            transforms.Resize((256, 256)),
            transforms.RandomCrop(224),
            transforms.RandomHorizontalFlip(),
            transforms.RandomRotation(10),
            transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        self.val_transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        self.model = None
        self.train_loader = None
        self.val_loader = None
        self.class_names = []
        
    def prepare_data(self, train_split=0.8):
        """Preparar datos de entrenamiento y validación"""
        print("Preparando datos...")
        
        # Cargar dataset completo
        full_dataset = PlantDataset(self.data_dir, transform=None)
        self.class_names = full_dataset.class_names
        
        if len(full_dataset) == 0:
            raise ValueError("No se encontraron datos en el directorio especificado")
        
        # Dividir en entrenamiento y validación
        train_size = int(train_split * len(full_dataset))
        val_size = len(full_dataset) - train_size
        
        train_indices = list(range(train_size))
        val_indices = list(range(train_size, len(full_dataset)))
        
        # Crear datasets con transformaciones
        train_dataset = PlantDataset(self.data_dir, transform=self.train_transform, class_names=self.class_names)
        val_dataset = PlantDataset(self.data_dir, transform=self.val_transform, class_names=self.class_names)
        
        # Crear subsets
        train_subset = torch.utils.data.Subset(train_dataset, train_indices)
        val_subset = torch.utils.data.Subset(val_dataset, val_indices)
        
        # Crear data loaders
        self.train_loader = DataLoader(train_subset, batch_size=self.batch_size, shuffle=True, num_workers=4)
        self.val_loader = DataLoader(val_subset, batch_size=self.batch_size, shuffle=False, num_workers=4)
        
        print(f"Datos preparados: {len(train_subset)} entrenamiento, {len(val_subset)} validación")
        print(f"Clases encontradas: {len(self.class_names)}")
        
    def create_model(self):
        """Crear modelo"""
        print(f"Creando modelo {self.model_name}...")
        self.model = PlantDiseaseClassifier(len(self.class_names), self.model_name)
        self.model.to(self.device)
        
    def train(self, num_epochs=50, save_path='models/plant_disease_model.pth'):
        """Entrenar el modelo"""
        if self.model is None:
            self.create_model()
        
        # Configurar optimizador y función de pérdida
        criterion = nn.CrossEntropyLoss()
        optimizer = optim.Adam(self.model.parameters(), lr=self.learning_rate)
        scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=10, gamma=0.1)
        
        # Listas para tracking
        train_losses = []
        val_losses = []
        train_accuracies = []
        val_accuracies = []
        
        best_val_acc = 0.0
        
        print(f"Iniciando entrenamiento por {num_epochs} épocas...")
        
        for epoch in range(num_epochs):
            # Entrenamiento
            self.model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0
            
            train_pbar = tqdm(self.train_loader, desc=f'Época {epoch+1}/{num_epochs} - Entrenamiento')
            for images, labels in train_pbar:
                images, labels = images.to(self.device), labels.to(self.device)
                
                optimizer.zero_grad()
                outputs = self.model(images)
                loss = criterion(outputs, labels)
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                train_total += labels.size(0)
                train_correct += (predicted == labels).sum().item()
                
                train_pbar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'Acc': f'{100.*train_correct/train_total:.2f}%'
                })
            
            # Validación
            self.model.eval()
            val_loss = 0.0
            val_correct = 0
            val_total = 0
            
            with torch.no_grad():
                for images, labels in self.val_loader:
                    images, labels = images.to(self.device), labels.to(self.device)
                    outputs = self.model(images)
                    loss = criterion(outputs, labels)
                    
                    val_loss += loss.item()
                    _, predicted = torch.max(outputs.data, 1)
                    val_total += labels.size(0)
                    val_correct += (predicted == labels).sum().item()
            
            # Calcular métricas
            train_acc = 100. * train_correct / train_total
            val_acc = 100. * val_correct / val_total
            
            train_losses.append(train_loss / len(self.train_loader))
            val_losses.append(val_loss / len(self.val_loader))
            train_accuracies.append(train_acc)
            val_accuracies.append(val_acc)
            
            print(f'Época {epoch+1}/{num_epochs}:')
            print(f'  Train Loss: {train_loss/len(self.train_loader):.4f}, Train Acc: {train_acc:.2f}%')
            print(f'  Val Loss: {val_loss/len(self.val_loader):.4f}, Val Acc: {val_acc:.2f}%')
            
            # Guardar mejor modelo
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                torch.save(self.model.state_dict(), save_path)
                print(f'  Nuevo mejor modelo guardado con {val_acc:.2f}% de precisión')
            
            scheduler.step()
        
        # Guardar métricas de entrenamiento
        self._save_training_metrics(train_losses, val_losses, train_accuracies, val_accuracies)
        
        print(f'Entrenamiento completado. Mejor precisión de validación: {best_val_acc:.2f}%')
        
    def _save_training_metrics(self, train_losses, val_losses, train_accuracies, val_accuracies):
        """Guardar métricas de entrenamiento"""
        metrics = {
            'train_losses': train_losses,
            'val_losses': val_losses,
            'train_accuracies': train_accuracies,
            'val_accuracies': val_accuracies,
            'class_names': self.class_names
        }
        
        os.makedirs('models', exist_ok=True)
        with open('models/training_metrics.json', 'w') as f:
            json.dump(metrics, f, indent=2)
        
        # Crear gráficos
        self._plot_training_metrics(train_losses, val_losses, train_accuracies, val_accuracies)
    
    def _plot_training_metrics(self, train_losses, val_losses, train_accuracies, val_accuracies):
        """Crear gráficos de métricas de entrenamiento"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
        
        # Gráfico de pérdida
        ax1.plot(train_losses, label='Entrenamiento')
        ax1.plot(val_losses, label='Validación')
        ax1.set_title('Pérdida durante el entrenamiento')
        ax1.set_xlabel('Época')
        ax1.set_ylabel('Pérdida')
        ax1.legend()
        ax1.grid(True)
        
        # Gráfico de precisión
        ax2.plot(train_accuracies, label='Entrenamiento')
        ax2.plot(val_accuracies, label='Validación')
        ax2.set_title('Precisión durante el entrenamiento')
        ax2.set_xlabel('Época')
        ax2.set_ylabel('Precisión (%)')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        plt.savefig('models/training_metrics.png', dpi=300, bbox_inches='tight')
        plt.close()

def download_sample_dataset():
    """Descargar dataset de ejemplo para entrenamiento"""
    print("Para entrenar el modelo, necesitas un dataset de imágenes de plantas.")
    print("Datasets recomendados:")
    print("1. PlantVillage Dataset: https://www.kaggle.com/emmarex/plantdisease")
    print("2. Plant Pathology 2020: https://www.kaggle.com/c/plant-pathology-2020-fgvc7")
    print("3. New Plant Diseases Dataset: https://www.kaggle.com/vipoooool/new-plant-diseases-dataset")
    print("\nEstructura requerida del dataset:")
    print("data/")
    print("├── train/")
    print("│   ├── clase1/")
    print("│   │   ├── imagen1.jpg")
    print("│   │   └── imagen2.jpg")
    print("│   └── clase2/")
    print("│       ├── imagen3.jpg")
    print("│       └── imagen4.jpg")

if __name__ == "__main__":
    # Ejemplo de uso
    data_dir = "data/train"  # Cambiar por tu directorio de datos
    
    if not os.path.exists(data_dir):
        print("Dataset no encontrado.")
        download_sample_dataset()
    else:
        trainer = PlantTrainer(data_dir)
        trainer.prepare_data()
        trainer.train(num_epochs=10)  # Reducido para pruebas
