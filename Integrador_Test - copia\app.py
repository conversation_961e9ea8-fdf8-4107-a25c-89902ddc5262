import os
from datetime import datetime, timezone
from flask import Flask, render_template, flash, redirect, url_for, request, session
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
from flask_wtf.csrf import CSRFProtect, generate_csrf
import json
import hashlib
from config import Config  # Importar la clase Config
from database import db  # Importar db desde database.py

# Initialize extensions
login_manager = LoginManager()
csrf = CSRFProtect()

# Exportar db para que otros módulos puedan importarlo desde app
__all__ = ['db', 'create_app', 'load_users', 'save_users', 'User']

def load_users():
    """Cargar usuarios desde JSON"""
    try:
        with open('data/users.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        return {}

def save_users(users):
    """Guardar usuarios en JSON"""
    os.makedirs('data', exist_ok=True)
    with open('data/users.json', 'w') as f:
        json.dump(users, f)

def hash_password(password):
    """Hash de contraseña usando SHA256"""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password, password_hash):
    """Verificar contraseña - compatible con múltiples formatos"""
    # Si es hash SHA256 simple (nuevo formato)
    if len(password_hash) == 64 and not password_hash.startswith('pbkdf2:'):
        return hash_password(password) == password_hash

    # Si es hash pbkdf2 (formato anterior)
    if password_hash.startswith('pbkdf2:'):
        from werkzeug.security import check_password_hash
        return check_password_hash(password_hash, password)

    # Fallback para otros formatos
    return hash_password(password) == password_hash

class User:
    def __init__(self, id, username, password_hash):
        self.id = id
        self.username = username
        self.password_hash = password_hash
        # Añadir UsuarioID para compatibilidad con el modelo de base de datos
        self.UsuarioID = id

    def is_authenticated(self):
        return True

    def is_active(self):
        return True

    def is_anonymous(self):
        return False

    def get_id(self):
        return str(self.id)

def create_app(config_class=Config):
    """Application factory function"""
    # Create Flask app
    app = Flask(__name__)
    
    # Load configuration
    app.config.from_object(config_class)
    
    # Ensure directories exist
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    os.makedirs('data', exist_ok=True)
    
    # Initialize extensions
    login_manager.init_app(app)
    csrf.init_app(app)
    
    # Configure login
    login_manager.login_view = 'auth.login'
    login_manager.login_message_category = 'info'
    
    # Inicializar base de datos
    try:
        from database import init_app
        init_app(app)
    except ImportError as e:
        print(f"Advertencia: No se pudo inicializar la base de datos: {e}")
    
    @login_manager.user_loader
    def load_user(user_id):
        # Si estamos usando JSON, cargar usuario desde JSON
        if app.config.get('USE_JSON_DB', False):
            try:
                from database_json import UserJson
                user_data = UserJson.query_by_id(user_id)
                if user_data:
                    return User(
                        id=user_data['UsuarioID'],
                        username=user_data['Username'],
                        password_hash=user_data['PasswordHash']
                    )
            except ImportError:
                pass
        
        # Si no estamos usando JSON o hubo un error, usar el método tradicional
        users = load_users()
        if user_id in users:
            user_data = users[user_id]
            return User(
                id=user_id,
                username=user_data['username'],
                password_hash=user_data['password_hash']
            )
        return None

    # Register blueprints
    try:
        # Importar el blueprint de ai_diagnosis (reemplaza al antiguo diagnosis)
        from routes.ai_diagnosis import diagnosis_bp
        app.register_blueprint(diagnosis_bp, url_prefix='/diagnosis')
        print("Blueprint de diagnosis (AI) registrado correctamente")

    except ImportError as e:
        print(f"Advertencia: No se pudo cargar el blueprint de diagnosis: {e}")

    try:
        # Importar el blueprint de API
        from routes.api import api_bp
        app.register_blueprint(api_bp, url_prefix='/api')

        # Deshabilitar CSRF para el blueprint de API
        csrf.exempt(api_bp)

        print("Blueprint de API registrado correctamente")

    except ImportError as e:
        print(f"Advertencia: No se pudo cargar el blueprint de API: {e}")

    try:
        # Importar el blueprint de administración simplificado
        from routes.admin_simple import admin_bp
        app.register_blueprint(admin_bp)
        print("Blueprint de administración registrado correctamente")

    except ImportError as e:
        print(f"Advertencia: No se pudo cargar el blueprint de administración: {e}")

    # Error handlers
    @app.errorhandler(404)
    def page_not_found(e):
        return render_template('errors/404.html'), 404

    @app.errorhandler(500)
    def internal_server_error(e):
        return render_template('errors/500.html'), 500

    # Main routes
    @app.route('/')
    def index():
        return render_template('home.html')

    @app.route('/home')
    def home():
        return render_template('home.html')

    @app.route('/login', methods=['GET', 'POST'])
    def login():
        if request.method == 'POST':
            print(f"🔍 LOGIN ATTEMPT - Form data: {dict(request.form)}")
            username = request.form.get('username')
            password = request.form.get('password')
            print(f"🔍 LOGIN ATTEMPT - Username: {username}, Password: {'***' if password else 'None'}")

            if not username or not password:
                flash('Por favor, completa todos los campos', 'error')
                return render_template('login.html')

            # Cargar usuarios
            users = load_users()

            # Buscar usuario por username (compatible con múltiples formatos)
            user_found = None
            user_id = None
            for uid, user_data in users.items():
                # Buscar por username directo o por display_username
                if (user_data.get('username') == username or
                    user_data.get('display_username') == username):
                    user_found = user_data
                    user_id = uid
                    break

            if user_found and verify_password(password, user_found['password_hash']):
                # Login exitoso
                display_name = user_found.get('display_username') or user_found.get('username')
                user = User(
                    id=user_id,
                    username=display_name,
                    password_hash=user_found['password_hash']
                )
                login_user(user)
                flash('¡Bienvenido de vuelta!', 'success')
                return redirect(url_for('index'))
            else:
                flash('Usuario o contraseña incorrectos', 'error')

        return render_template('login.html')

    @app.route('/register', methods=['GET', 'POST'])
    def register():
        if request.method == 'POST':
            first_name = request.form.get('first_name')
            last_name = request.form.get('last_name')
            username = request.form.get('username')
            email = request.form.get('email')
            password = request.form.get('password')
            confirm_password = request.form.get('confirm_password')

            # Validaciones
            if not all([first_name, last_name, username, email, password, confirm_password]):
                flash('Por favor, completa todos los campos', 'error')
                return render_template('login.html')

            if password != confirm_password:
                flash('Las contraseñas no coinciden', 'error')
                return render_template('login.html')

            if len(password) < 6:
                flash('La contraseña debe tener al menos 6 caracteres', 'error')
                return render_template('login.html')

            # Cargar usuarios existentes
            users = load_users()

            # Verificar si el usuario ya existe
            for user_data in users.values():
                if user_data.get('username') == username:
                    flash('El nombre de usuario ya existe', 'error')
                    return render_template('login.html')
                if user_data.get('email') == email:
                    flash('El correo electrónico ya está registrado', 'error')
                    return render_template('login.html')

            # Crear nuevo usuario
            user_id = str(len(users) + 1)
            new_user = {
                'username': username,
                'email': email,
                'password_hash': hash_password(password),
                'first_name': first_name,
                'last_name': last_name,
                'created_at': datetime.now().isoformat()
            }

            users[user_id] = new_user
            save_users(users)

            # Login automático después del registro
            user = User(
                id=user_id,
                username=username,
                password_hash=new_user['password_hash']
            )
            login_user(user)

            flash('¡Cuenta creada exitosamente! Bienvenido a PlantCare', 'success')
            return redirect(url_for('index'))

        return render_template('login.html')

    @app.route('/logout')
    @login_required
    def logout():
        logout_user()
        flash('Has cerrado sesión exitosamente', 'info')
        return redirect(url_for('index'))

    @app.route('/login-simple')
    def login_simple():
        """Página de login simple para pruebas"""
        return render_template('admin_login_test.html')

    @app.route('/test-admin-login')
    def test_admin_login():
        """Página de prueba específica para login de admin"""
        return render_template('admin_login_test.html')

    @app.route('/simple-login')
    def simple_login_page():
        """Página de login simple sin CSRF"""
        return render_template('simple_login.html')

    @csrf.exempt
    @app.route('/login-simple-post', methods=['POST'])
    def login_simple_post():
        """Login POST sin CSRF para pruebas"""
        username = request.form.get('username')
        password = request.form.get('password')

        if not username or not password:
            flash('Por favor, completa todos los campos.', 'danger')
            return redirect(url_for('simple_login_page'))

        # Cargar usuarios
        users = load_users()

        # Buscar usuario
        user_data = None
        user_id = None
        for uid, data in users.items():
            if data.get('username') == username:
                user_data = data
                user_id = uid
                break

        if not user_data:
            flash('Usuario no encontrado.', 'danger')
            return redirect(url_for('simple_login_page'))

        # Verificar contraseña
        if verify_password(password, user_data['password_hash']):
            # Crear objeto usuario y hacer login
            user = User(user_id, user_data)
            login_user(user)
            flash(f'¡Bienvenido, {user_data.get("display_username", username)}!', 'success')
            return redirect(url_for('index'))
        else:
            flash('Contraseña incorrecta.', 'danger')
            return redirect(url_for('simple_login_page'))

    @app.route('/status')
    def status():
        """Página de estado del usuario"""
        if current_user.is_authenticated:
            return f"""
            <h1>✅ Usuario Autenticado</h1>
            <p><strong>ID:</strong> {current_user.id}</p>
            <p><strong>Username:</strong> {current_user.username}</p>
            <p><strong>Autenticado:</strong> Sí</p>
            <hr>
            <a href="/logout">Cerrar Sesión</a> |
            <a href="/login-simple">Login</a> |
            <a href="/">Inicio</a>
            """
        else:
            return f"""
            <h1>❌ Usuario NO Autenticado</h1>
            <p>No hay usuario logueado</p>
            <hr>
            <a href="/login-simple">Iniciar Sesión</a> |
            <a href="/">Inicio</a>
            """

    @app.route('/biblioteca')
    def biblioteca():
        return render_template('biblioteca.html')

    @app.route('/scanner')
    def scanner():
        return redirect('/diagnosis/scanner')

    @app.route('/calendario')
    def calendario():
        return render_template('calendario.html')

    @app.route('/recomendaciones')
    def recomendaciones():
        return render_template('recomendaciones.html')

    @app.route('/foro')
    def foro():
        return render_template('foro.html')

    @app.route('/perfil')
    def perfil():
        return render_template('perfil.html')

    @app.route('/ajustes')
    def ajustes():
        return render_template('ajustes.html')

    @app.route('/plant-details')
    def plant_details():
        return render_template('plant_details.html')

    @app.route('/planta/<int:plant_id>')
    def planta_detalle(plant_id):
        return render_template('planta-detalle.html', plant_id=plant_id)



    # Ruta alternativa para biblioteca en caso de problemas con el blueprint
    @app.route('/biblioteca-alt')
    def biblioteca_alt():
        return render_template('biblioteca.html')

    # Context processor
    @app.context_processor
    def inject_now():
        return {
            'now': datetime.now(timezone.utc),
            'csrf_token': generate_csrf()
        }

    # Función global para templates para verificar si el usuario actual es admin
    @app.template_global()
    def current_user_is_admin():
        """Función global para templates para verificar si el usuario actual es admin"""
        try:
            if current_user.is_authenticated:
                # Verificar si el usuario actual es admin
                users = load_users()
                user_data = users.get(str(current_user.id))
                if user_data:
                    return user_data.get('is_admin', False) or user_data.get('role') == 'admin'
            return False
        except:
            # Para desarrollo, permitir acceso si hay errores
            return False

    return app

# Create and run app if this file is run directly
if __name__ == '__main__':
    app = create_app()
    print("\n✅ PlantCare está ejecutándose!")
    print("🌐 Accede a la aplicación en: http://127.0.0.1:5000")
    print("🌐 Desde otros dispositivos en tu red: http://<tu-ip-local>:5000")
    print("🛑 Presiona CTRL+C para detener el servidor\n")
    app.run(debug=True, host='0.0.0.0', port=5000)

