#!/usr/bin/env python3
"""
Limpieza automática de archivos no utilizados (sin confirmación)
"""

import os
import shutil
import json
from datetime import datetime

def auto_cleanup():
    """Limpieza automática sin confirmación"""
    
    print("🧹 LIMPIEZA AUTOMÁTICA DE ARCHIVOS NO UTILIZADOS")
    print("=" * 60)
    
    # Archivos que se pueden eliminar automáticamente (archivos de prueba/temporales)
    files_to_remove = [
        # Scripts de prueba
        'test_admin_system.py',
        'test_ai_service.py', 
        'test_calendario.py',
        'test_diagnosis.py',
        'test_entrenamiento.py',
        'test_final.py',
        'test_frontend_simulation.py',
        'test_login_admin.py',
        'test_login_simple.py',
        'test_mysql_connection.py',
        'test_recommendations.py',
        'test_server.py',
        
        # Scripts de debug/fix
        'debug_login.py',
        'fix_admin_login.py',
        'fix_routes.py',
        'fix_syntax.py',
        'final_fix.py',
        'complete_audit.py',
        'check_hash.py',
        'verify_admin.py',
        'sync_admin_user.py',
        
        # Scripts de creación/inicialización duplicados
        'create_admin.py',
        'create_admin_simple.py',
        'init_app.py',
        'init_db.py',
        'init_db_mysql.py',
        'init_mysql.py',
        'initialize_db.py',
        'crear_modelo_simple.py',
        
        # Scripts de entrenamiento duplicados
        'entrenar_bugambilia.py',
        'entrenar_modelo.py',
        'entrenar_modelo_completo.py',
        'entrenar_modelo_completo_final.py',
        'entrenar_modelo_corregido.py',
        'entrenar_modelo_final.py',
        'entrenar_modelo_personalizado.py',
        'entrenar_modelo_simple.py',
        'train_and_integrate.py',
        'train_model.py',
        'probar_modelo.py',
        'monitorear_entrenamiento.py',
        
        # Otros archivos temporales
        'data_preparation.py',
        'auth_middleware.py',
        'recommendations.py',
        'minimal_server.py',
        'simple_app.py',
        
        # Archivos HTML temporales
        'admin_login_fixed.html',
        'test_page.html',
        
        # Logs
        'entrenamiento_log.txt'
    ]
    
    # Routes no utilizados
    routes_to_remove = [
        'routes/admin.py',  # Reemplazado por admin_simple.py
        'routes/auth.py',   # No se usa
        'routes/auth_middleware.py',
        'routes/diagnosis.py',  # Reemplazado por ai_diagnosis.py
        'routes/forum.py',
        'routes/plants.py',
        'routes/profile.py',
        'routes/recomendaciones.py',
        'routes/reminders.py',
        'routes/settings.py',
        'routes/views.py'
    ]
    
    # Templates no utilizados
    templates_to_remove = [
        'templates/detalle.html',
        'templates/diagnosis_result.html',
        'templates/tema-detalle.html'
    ]
    
    # Crear backup
    backup_dir = 'backup_unused_files'
    os.makedirs(backup_dir, exist_ok=True)
    
    removed_files = []
    
    print("\n💾 Creando backup y eliminando archivos...")
    
    # Procesar todos los archivos a eliminar
    all_files = files_to_remove + routes_to_remove + templates_to_remove
    
    for file_path in all_files:
        if os.path.exists(file_path):
            try:
                # Crear backup
                backup_path = os.path.join(backup_dir, file_path)
                os.makedirs(os.path.dirname(backup_path), exist_ok=True)
                shutil.copy2(file_path, backup_path)
                
                # Eliminar archivo original
                os.remove(file_path)
                removed_files.append(file_path)
                print(f"   ✅ {file_path}")
                
            except Exception as e:
                print(f"   ❌ Error con {file_path}: {e}")
    
    # Limpiar directorios vacíos
    empty_dirs = []
    dirs_to_check = [
        'routes/__pycache__',
        'utils/__pycache__',
        'models/__pycache__'
    ]
    
    for dir_path in dirs_to_check:
        if os.path.exists(dir_path):
            try:
                files = os.listdir(dir_path)
                if not files or all(f.endswith('.pyc') for f in files):
                    shutil.rmtree(dir_path)
                    empty_dirs.append(dir_path)
                    print(f"   🗑️  Directorio eliminado: {dir_path}")
            except Exception as e:
                print(f"   ❌ Error eliminando directorio {dir_path}: {e}")
    
    # Crear requirements limpio
    core_requirements = [
        "Flask==2.3.3",
        "Flask-Login==0.6.3", 
        "Flask-WTF==1.1.1",
        "Werkzeug==2.3.7",
        "Jinja2==3.1.2",
        "WTForms==3.0.1",
        "Pillow==10.0.0",
        "requests==2.31.0"
    ]
    
    with open('requirements_clean.txt', 'w') as f:
        f.write("# Dependencias CORE para PlantCare\n")
        f.write("# Solo las dependencias realmente utilizadas\n\n")
        for req in core_requirements:
            f.write(f"{req}\n")
    
    # Generar reporte
    report = {
        'timestamp': datetime.now().isoformat(),
        'files_removed': len(removed_files),
        'directories_removed': len(empty_dirs),
        'removed_files': removed_files,
        'removed_directories': empty_dirs,
        'backup_location': backup_dir
    }
    
    with open('cleanup_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n🎉 LIMPIEZA COMPLETADA!")
    print(f"   ✅ {len(removed_files)} archivos eliminados")
    print(f"   ✅ {len(empty_dirs)} directorios eliminados")
    print(f"   💾 Backup creado en: {backup_dir}")
    print(f"   📦 requirements_clean.txt generado")
    print(f"   📋 Reporte guardado en: cleanup_report.json")
    
    return len(removed_files)

def verify_core_files():
    """Verificar que los archivos core estén presentes"""
    
    print("\n🔍 VERIFICANDO ARCHIVOS CORE...")
    
    core_files = [
        'app.py',
        'config.py',
        'database.py',
        'routes/admin_simple.py',
        'routes/api.py',
        'routes/ai_diagnosis.py',
        'templates/home.html',
        'templates/login.html',
        'static/css/styles.css'
    ]
    
    missing_files = []
    for file_path in core_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
            print(f"   ❌ FALTA: {file_path}")
        else:
            print(f"   ✅ {file_path}")
    
    if missing_files:
        print(f"\n⚠️  ARCHIVOS CORE FALTANTES: {len(missing_files)}")
        return False
    else:
        print(f"\n✅ Todos los archivos core están presentes")
        return True

def main():
    """Función principal"""
    
    # Verificar archivos core antes de limpiar
    if not verify_core_files():
        print("❌ No se puede proceder - faltan archivos core")
        return
    
    # Ejecutar limpieza
    removed_count = auto_cleanup()
    
    print(f"\n🚀 PRÓXIMOS PASOS:")
    print(f"   1. Reinicia el servidor: python app.py")
    print(f"   2. Verifica que todo funcione")
    print(f"   3. Si hay problemas, restaura desde backup_unused_files/")
    
    return removed_count

if __name__ == "__main__":
    main()
