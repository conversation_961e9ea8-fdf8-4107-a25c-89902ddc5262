from functools import wraps
from flask import request, jsonify, current_app
import jwt
from datetime import datetime, timedelta
from flask_login import current_user

def generate_token(user_id):
    """Generate a new JWT token for the user"""
    payload = {
        'user_id': user_id,
        'exp': datetime.utcnow() + timedelta(hours=24),
        'iat': datetime.utcnow()
    }
    return jwt.encode(
        payload,
        current_app.config.get('SECRET_KEY'),
        algorithm='HS256'
    )

def token_required(f):
    """Decorator to protect API routes with JWT authentication"""
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        
        # Check for token in headers
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            try:
                token = auth_header.split(" ")[1]
            except IndexError:
                token = auth_header
        
        if not token:
            return jsonify({
                'success': False,
                'message': 'Acceso no autorizado'
            }), 401
        
        try:
            # Decode and verify the token
            data = jwt.decode(
                token,
                current_app.config.get('SECRET_KEY'),
                algorithms=['HS256']
            )
            
            # Additional validation can be done here
            user_id = data['user_id']
            
            # You could optionally verify the user exists in database
            
        except jwt.ExpiredSignatureError:
            return jsonify({
                'success': False,
                'message': 'Token expirado. Por favor, inicie sesión nuevamente.'
            }), 401
        except jwt.InvalidTokenError:
            return jsonify({
                'success': False,
                'message': 'Token inválido. Por favor, inicie sesión nuevamente.'
            }), 401
        
        return f(*args, **kwargs)
    
    return decorated