import hashlib
import json

# Verificar hash actual
password = "PlantCare2025!"
expected_hash = hashlib.sha256(password.encode()).hexdigest()

print(f"Contraseña: {password}")
print(f"Hash esperado: {expected_hash}")

# Verificar hash en archivo
try:
    with open('data/users.json', 'r') as f:
        users = json.load(f)
    
    stored_hash = users["1"]["password_hash"]
    print(f"Hash almacenado: {stored_hash}")
    print(f"¿Coinciden?: {expected_hash == stored_hash}")
    
except Exception as e:
    print(f"Error: {e}")
