#!/usr/bin/env python3
"""
Auditoría completa y corrección de errores en PlantCare
"""

import os
import re
import json
from pathlib import Path

def audit_and_fix_all():
    """Realiza una auditoría completa y corrige todos los errores"""
    
    print("🔍 AUDITORÍA COMPLETA DE PLANTCARE")
    print("=" * 50)
    
    errors_found = []
    fixes_applied = []
    
    # 1. VERIFICAR ESTRUCTURA DE ARCHIVOS
    print("\n1. 📁 Verificando estructura de archivos...")
    
    required_files = [
        'app.py',
        'config.py',
        'ai_service.py',
        'database_json.py',
        'templates/home.html',
        'templates/biblioteca.html',
        'templates/calendario.html',
        'templates/recomendaciones.html',
        'templates/foro.html',
        'templates/perfil.html',
        'templates/ajustes.html',
        'templates/scanner.html',
        'templates/login.html',
        'templates/register.html',
        'static/css/styles.css',
        'static/js/main.js'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        errors_found.append(f"Archivos faltantes: {missing_files}")
    else:
        print("✅ Todos los archivos principales están presentes")
    
    # 2. BUSCAR Y CORREGIR RUTAS PROBLEMÁTICAS
    print("\n2. 🔗 Buscando rutas problemáticas en plantillas...")
    
    # Patrones de rutas problemáticas
    problematic_patterns = [
        r"url_for\('auth\.[^']+'\)",
        r"url_for\('profile\.[^']+'\)",
        r"url_for\('plants\.[^']+'\)",
        r"url_for\('diagnosis\.[^']+'\)",
        r"url_for\('reminders\.[^']+'\)",
        r"url_for\('forum\.[^']+'\)",
        r"url_for\('settings\.[^']+'\)",
        r"url_for\('views\.[^']+'\)",
        r"url_for\('recomendaciones\.[^']+'\)",
        r"url_for\('[^']+_alt'\)",
    ]
    
    # Mapeo de correcciones
    route_corrections = {
        # Auth routes
        r"url_for\('auth\.login'\)": '"/login"',
        r"url_for\('auth\.register'\)": '"/register"',
        r"url_for\('auth\.logout'\)": '"/login"',
        
        # Profile routes
        r"url_for\('profile\.index'\)": '"/perfil"',
        r"url_for\('profile\.user_profile'\)": '"/perfil"',
        r"url_for\('profile\.ajustes'\)": '"/ajustes"',
        
        # Plants routes
        r"url_for\('plants\.biblioteca'\)": '"/biblioteca"',
        r"url_for\('plants\.plant_detail',\s*plant_id=\d+\)": '"/biblioteca"',
        
        # Diagnosis routes
        r"url_for\('diagnosis\.scanner'\)": '"/diagnosis/scanner"',
        
        # Reminders routes
        r"url_for\('reminders\.calendario'\)": '"/calendario"',
        r"url_for\('reminders\.calendar'\)": '"/calendario"',
        
        # Forum routes
        r"url_for\('forum\.index'\)": '"/foro"',
        r"url_for\('forum\.forum_home'\)": '"/foro"',
        
        # Settings routes
        r"url_for\('settings\.index'\)": '"/ajustes"',
        r"url_for\('settings\.update_privacy'\)": '"/ajustes"',
        
        # Views routes
        r"url_for\('views\.login'\)": '"/login"',
        r"url_for\('views\.register'\)": '"/register"',
        
        # Recomendaciones routes
        r"url_for\('recomendaciones\.index'\)": '"/recomendaciones"',
        
        # Alt routes
        r"url_for\('calendario_alt'\)": '"/calendario"',
        r"url_for\('biblioteca_alt'\)": '"/biblioteca"',
        
        # Simple routes
        r"url_for\('home'\)": '"/"',
        r"url_for\('biblioteca'\)": '"/biblioteca"',
        r"url_for\('scanner'\)": '"/scanner"',
        r"url_for\('calendario'\)": '"/calendario"',
        r"url_for\('recomendaciones'\)": '"/recomendaciones"',
        r"url_for\('foro'\)": '"/foro"',
        r"url_for\('perfil'\)": '"/perfil"',
        r"url_for\('ajustes'\)": '"/ajustes"',
        r"url_for\('index'\)": '"/"',
    }
    
    templates_dir = "templates"
    fixed_templates = []
    
    for root, dirs, files in os.walk(templates_dir):
        for file in files:
            if file.endswith('.html'):
                file_path = os.path.join(root, file)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    original_content = content
                    changes_made = 0
                    
                    # Aplicar correcciones
                    for pattern, replacement in route_corrections.items():
                        matches = re.findall(pattern, content)
                        if matches:
                            content = re.sub(pattern, replacement, content)
                            changes_made += len(matches)
                    
                    # Corregir sintaxis incorrecta {{ "/ruta" }}
                    syntax_patterns = [
                        (r'\{\{\s*"(/[^"]*?)"\s*\}\}', r'\1'),
                        (r'href="\{\{\s*"(/[^"]*?)"\s*\}\}"', r'href="\1"'),
                        (r'action="\{\{\s*"(/[^"]*?)"\s*\}\}"', r'action="\1"'),
                        (r'src="\{\{\s*"(/[^"]*?)"\s*\}\}"', r'src="\1"'),
                    ]
                    
                    for pattern, replacement in syntax_patterns:
                        matches = re.findall(pattern, content)
                        if matches:
                            content = re.sub(pattern, replacement, content)
                            changes_made += len(matches)
                    
                    if changes_made > 0:
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(content)
                        
                        fixed_templates.append((file_path, changes_made))
                        fixes_applied.append(f"Corregidas {changes_made} rutas en {file_path}")
                
                except Exception as e:
                    errors_found.append(f"Error procesando {file_path}: {e}")
    
    if fixed_templates:
        print(f"✅ Corregidas rutas en {len(fixed_templates)} plantillas")
        for file_path, changes in fixed_templates:
            print(f"   - {file_path}: {changes} cambios")
    else:
        print("✅ No se encontraron rutas problemáticas")
    
    # 3. VERIFICAR ARCHIVOS ESTÁTICOS FALTANTES
    print("\n3. 🖼️ Verificando archivos estáticos...")
    
    static_files_referenced = [
        'static/assets/hero-bg.jpg',
        'static/assets/agave.webp',
        'static/assets/nopal.jpg',
        'static/assets/yuca.jpg',
        'static/assets/biznaga.jpg',
        'static/assets/gobernadora.jpg',
        'static/assets/Mezquite.jpg',
        'static/assets/lechuguilla.jpg',
        'static/assets/ocotillo.jpg',
        'static/css/home.css',
        'static/css/biblioteca.css',
        'static/css/scanner.css',
        'static/css/user-menu.css',
        'static/js/home.js',
        'static/js/biblioteca.js',
        'static/js/scanner.js'
    ]
    
    missing_static = []
    for file_path in static_files_referenced:
        if not os.path.exists(file_path):
            missing_static.append(file_path)
    
    if missing_static:
        errors_found.append(f"Archivos estáticos faltantes: {missing_static}")
        print(f"⚠️ Archivos estáticos faltantes: {len(missing_static)}")
        for file in missing_static:
            print(f"   - {file}")
    else:
        print("✅ Todos los archivos estáticos están presentes")
    
    # 4. VERIFICAR CONFIGURACIÓN DE RUTAS EN APP.PY
    print("\n4. ⚙️ Verificando configuración de rutas...")
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            app_content = f.read()
        
        # Verificar que todas las rutas principales estén definidas
        required_routes = [
            '@app.route(\'/\')',
            '@app.route(\'/home\')',
            '@app.route(\'/login\')',
            '@app.route(\'/register\')',
            '@app.route(\'/biblioteca\')',
            '@app.route(\'/scanner\')',
            '@app.route(\'/calendario\')',
            '@app.route(\'/recomendaciones\')',
            '@app.route(\'/foro\')',
            '@app.route(\'/perfil\')',
            '@app.route(\'/ajustes\')'
        ]
        
        missing_routes = []
        for route in required_routes:
            if route not in app_content:
                missing_routes.append(route)
        
        if missing_routes:
            errors_found.append(f"Rutas faltantes en app.py: {missing_routes}")
        else:
            print("✅ Todas las rutas principales están definidas")
    
    except Exception as e:
        errors_found.append(f"Error verificando app.py: {e}")
    
    # 5. RESUMEN FINAL
    print("\n" + "=" * 50)
    print("📊 RESUMEN DE LA AUDITORÍA")
    print("=" * 50)
    
    if errors_found:
        print(f"❌ Se encontraron {len(errors_found)} tipos de errores:")
        for i, error in enumerate(errors_found, 1):
            print(f"   {i}. {error}")
    else:
        print("✅ No se encontraron errores críticos")
    
    if fixes_applied:
        print(f"\n🔧 Se aplicaron {len(fixes_applied)} correcciones:")
        for i, fix in enumerate(fixes_applied, 1):
            print(f"   {i}. {fix}")
    
    print(f"\n🎯 Estado final:")
    print(f"   - Errores encontrados: {len(errors_found)}")
    print(f"   - Correcciones aplicadas: {len(fixes_applied)}")
    
    return len(errors_found), len(fixes_applied)

if __name__ == "__main__":
    errors, fixes = audit_and_fix_all()
    
    if errors == 0:
        print("\n🎉 ¡AUDITORÍA COMPLETADA! La aplicación está lista.")
    else:
        print(f"\n⚠️ Auditoría completada con {errors} errores pendientes.")
    
    print("🔄 Reinicia el servidor para aplicar todos los cambios.")
