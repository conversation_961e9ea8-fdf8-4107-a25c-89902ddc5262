"""
Script muy simple para crear un modelo de ejemplo.
"""

import os
import json
import torch
import torch.nn as nn
from torchvision import models

# Configuración
MODEL_PATH = "models/plant_disease_model.pth"
CLASS_NAMES_PATH = "models/class_names.json"

# Asegurarse de que exista el directorio
os.makedirs(os.path.dirname(MODEL_PATH), exist_ok=True)

# Crear un modelo simple
print("Creando modelo...")
model = models.resnet18(weights='IMAGENET1K_V1')

# Congelar todos los parámetros
for param in model.parameters():
    param.requires_grad = False

# Reemplazar la capa final para nuestra tarea de clasificación
num_ftrs = model.fc.in_features
model.fc = nn.Sequential(
    nn.Linear(num_ftrs, 256),
    nn.ReLU(),
    nn.Dropout(0.2),
    nn.Linear(256, 2)  # Solo 2 clases: bugambilia y bugambilia enfermedades
)

# Guardar el modelo
torch.save(model, MODEL_PATH)
print(f"Modelo guardado en: {MODEL_PATH}")

# Guardar los nombres de las clases
class_names = ["bugambilia", "bugambilia_enfermedades"]
with open(CLASS_NAMES_PATH, 'w', encoding='utf-8') as f:
    json.dump(class_names, f, ensure_ascii=False, indent=2)

print(f"Nombres de clases guardados en: {CLASS_NAMES_PATH}")
print("¡Modelo creado con éxito!")
