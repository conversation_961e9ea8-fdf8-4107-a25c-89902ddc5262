#!/usr/bin/env python3
"""
Script simple para crear el usuario administrador
"""

import json
import os
import hashlib
from datetime import datetime

def hash_password(password):
    """Hash de contraseña usando SHA256"""
    return hashlib.sha256(password.encode()).hexdigest()

def create_admin_user():
    """Crear usuario administrador"""
    
    print("🔧 Creando usuario administrador...")
    
    # Credenciales del admin
    admin_username = "admin"
    admin_password = "PlantCare2025!"
    admin_email = "<EMAIL>"
    
    # Crear hash de contraseña
    password_hash = hash_password(admin_password)
    
    # Crear estructura de usuarios
    users = {
        "1": {
            "username": admin_username,
            "display_username": admin_username,
            "email": admin_email,
            "password_hash": password_hash,
            "first_name": "Administrador",
            "last_name": "PlantCare",
            "created_at": datetime.now().isoformat(),
            "is_admin": True,
            "role": "admin"
        }
    }
    
    # Crear directorio si no existe
    os.makedirs('data', exist_ok=True)
    
    # Guardar usuarios
    try:
        with open('data/users.json', 'w', encoding='utf-8') as f:
            json.dump(users, f, indent=2, ensure_ascii=False)
        
        print("✅ Usuario administrador creado exitosamente!")
        print(f"\n📋 Credenciales:")
        print(f"   Usuario: {admin_username}")
        print(f"   Contraseña: {admin_password}")
        print(f"   Email: {admin_email}")
        print(f"   Hash: {password_hash[:20]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creando usuario: {e}")
        return False

def test_password():
    """Probar que la contraseña funciona"""
    
    print("\n🧪 Probando contraseña...")
    
    try:
        # Cargar usuarios
        with open('data/users.json', 'r', encoding='utf-8') as f:
            users = json.load(f)
        
        # Obtener usuario admin
        admin = users.get("1")
        if not admin:
            print("❌ Usuario admin no encontrado")
            return False
        
        # Probar contraseña
        test_password = "PlantCare2025!"
        expected_hash = hash_password(test_password)
        stored_hash = admin['password_hash']
        
        print(f"   Contraseña de prueba: {test_password}")
        print(f"   Hash esperado: {expected_hash[:20]}...")
        print(f"   Hash almacenado: {stored_hash[:20]}...")
        
        if expected_hash == stored_hash:
            print("✅ ¡Contraseña correcta!")
            return True
        else:
            print("❌ Contraseña incorrecta")
            return False
            
    except Exception as e:
        print(f"❌ Error probando contraseña: {e}")
        return False

def main():
    """Función principal"""
    
    print("🛠️  CREACIÓN DE USUARIO ADMINISTRADOR")
    print("=" * 40)
    
    # Crear usuario
    if create_admin_user():
        # Probar contraseña
        if test_password():
            print("\n🎉 ¡TODO LISTO!")
            print("\n🚀 Pasos siguientes:")
            print("   1. Ve a: http://127.0.0.1:5000/login")
            print("   2. Usa las credenciales:")
            print("      Usuario: admin")
            print("      Contraseña: PlantCare2025!")
            print("   3. Después del login, ve a: http://127.0.0.1:5000/admin")
        else:
            print("\n⚠️  Usuario creado pero hay problemas con la contraseña")
    else:
        print("\n❌ No se pudo crear el usuario")

if __name__ == "__main__":
    main()
