import os
import shutil
import random
import json
from pathlib import Path
import numpy as np
from PIL import Image
import cv2
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split

class DataPreparation:
    def __init__(self, raw_data_dir, processed_data_dir, class_names_file=None):
        """
        Initialize the data preparation process.
        
        Args:
            raw_data_dir: Directory containing raw plant images
            processed_data_dir: Directory where processed images will be saved
            class_names_file: Optional path to class_names.json
        """
        self.raw_data_dir = raw_data_dir
        self.processed_data_dir = processed_data_dir
        self.train_dir = os.path.join(processed_data_dir, 'train')
        self.val_dir = os.path.join(processed_data_dir, 'val')
        
        # Load class names if provided
        self.class_names = []
        if class_names_file and os.path.exists(class_names_file):
            with open(class_names_file, 'r', encoding='utf-8') as f:
                self.class_names = json.load(f)
    
    def create_directory_structure(self):
        """Create the necessary directory structure for training"""
        # Create main directories
        os.makedirs(self.processed_data_dir, exist_ok=True)
        os.makedirs(self.train_dir, exist_ok=True)
        os.makedirs(self.val_dir, exist_ok=True)
        
        # Create subdirectories for each class
        for class_name in self.class_names:
            os.makedirs(os.path.join(self.train_dir, class_name), exist_ok=True)
            os.makedirs(os.path.join(self.val_dir, class_name), exist_ok=True)
    
    def preprocess_images(self, target_size=(224, 224), augment=True):
        """
        Preprocess all images and organize them into the train/val directory structure.
        
        Args:
            target_size: The size to resize images to
            augment: Whether to perform data augmentation
        """
        print("Starting image preprocessing...")
        
        for class_idx, class_name in enumerate(self.class_names):
            print(f"Processing class: {class_name} ({class_idx+1}/{len(self.class_names)})")
            
            # Get paths for all images of this class in the raw data
            # This assumes images are organized by class in the raw data directory
            class_dir = os.path.join(self.raw_data_dir, class_name)
            if not os.path.exists(class_dir):
                print(f"Warning: Directory not found for class {class_name}. Skipping.")
                continue
            
            # Get all image files
            image_files = [f for f in os.listdir(class_dir) 
                          if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
            
            if not image_files:
                print(f"Warning: No images found for class {class_name}. Skipping.")
                continue
            
            # Split into training and validation sets
            train_files, val_files = train_test_split(
                image_files, test_size=0.2, random_state=42
            )
            
            # Process training images
            for img_file in train_files:
                self._process_image(
                    os.path.join(class_dir, img_file),
                    os.path.join(self.train_dir, class_name),
                    target_size,
                    augment
                )
            
            # Process validation images (no augmentation)
            for img_file in val_files:
                self._process_image(
                    os.path.join(class_dir, img_file),
                    os.path.join(self.val_dir, class_name),
                    target_size,
                    False
                )
        
        print("Image preprocessing complete.")
    
    def _process_image(self, src_path, dst_dir, target_size, augment):
        """
        Process a single image: resize, normalize, and optionally augment.
        
        Args:
            src_path: Source image path
            dst_dir: Destination directory
            target_size: Size to resize the image to
            augment: Whether to create augmented versions
        """
        try:
            # Load and resize image
            img = cv2.imread(src_path)
            if img is None:
                print(f"Warning: Could not read image {src_path}. Skipping.")
                return
            
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            img_resized = cv2.resize(img, target_size)
            
            # Save the resized image
            filename = os.path.basename(src_path)
            base_name, ext = os.path.splitext(filename)
            
            # Save the original resized image
            out_path = os.path.join(dst_dir, filename)
            cv2.imwrite(out_path, cv2.cvtColor(img_resized, cv2.COLOR_RGB2BGR))
            
            # Perform augmentation if requested
            if augment:
                # Create 4 augmented versions of each training image
                
                # 1. Horizontal flip
                img_h_flip = cv2.flip(img_resized, 1)
                cv2.imwrite(
                    os.path.join(dst_dir, f"{base_name}_h_flip{ext}"), 
                    cv2.cvtColor(img_h_flip, cv2.COLOR_RGB2BGR)
                )
                
                # 2. Vertical flip
                img_v_flip = cv2.flip(img_resized, 0)
                cv2.imwrite(
                    os.path.join(dst_dir, f"{base_name}_v_flip{ext}"), 
                    cv2.cvtColor(img_v_flip, cv2.COLOR_RGB2BGR)
                )
                
                # 3. Rotation (90 degrees)
                img_rot = cv2.rotate(img_resized, cv2.ROTATE_90_CLOCKWISE)
                cv2.imwrite(
                    os.path.join(dst_dir, f"{base_name}_rot90{ext}"), 
                    cv2.cvtColor(img_rot, cv2.COLOR_RGB2BGR)
                )
                
                # 4. Brightness adjustment
                brightness = np.ones(img_resized.shape, dtype="uint8") * 30
                img_bright = cv2.add(img_resized, brightness)
                cv2.imwrite(
                    os.path.join(dst_dir, f"{base_name}_bright{ext}"), 
                    cv2.cvtColor(img_bright, cv2.COLOR_RGB2BGR)
                )
                
        except Exception as e:
            print(f"Error processing image {src_path}: {str(e)}")
    
    def verify_dataset(self):
        """
        Verify the processed dataset structure and print statistics.
        
        Returns:
            dict: Statistics about the processed dataset
        """
        stats = {
            'train': {},
            'val': {},
            'total_train': 0,
            'total_val': 0
        }
        
        # Check training data
        for class_name in self.class_names:
            class_dir = os.path.join(self.train_dir, class_name)
            if os.path.exists(class_dir):
                count = len([f for f in os.listdir(class_dir) 
                            if f.lower().endswith(('.png', '.jpg', '.jpeg'))])
                stats['train'][class_name] = count
                stats['total_train'] += count
            else:
                stats['train'][class_name] = 0
        
        # Check validation data
        for class_name in self.class_names:
            class_dir = os.path.join(self.val_dir, class_name)
            if os.path.exists(class_dir):
                count = len([f for f in os.listdir(class_dir) 
                            if f.lower().endswith(('.png', '.jpg', '.jpeg'))])
                stats['val'][class_name] = count
                stats['total_val'] += count
            else:
                stats['val'][class_name] = 0
        
        # Print statistics
        print(f"Dataset Statistics:")
        print(f"- Total training images: {stats['total_train']}")
        print(f"- Total validation images: {stats['total_val']}")
        print(f"- Classes distribution:")
        
        for class_name in self.class_names:
            train_count = stats['train'].get(class_name, 0)
            val_count = stats['val'].get(class_name, 0)
            print(f"  - {class_name}: {train_count} training, {val_count} validation")
        
        return stats

if __name__ == "__main__":
    # Example usage:
    # 1. Define directories and load class names
    # raw_data_dir = "path/to/raw_plant_images"
    # processed_data_dir = "path/to/processed_data"
    # class_names_file = "models/class_names.json"
    
    # 2. Create DataPreparation instance and process images
    # data_prep = DataPreparation(raw_data_dir, processed_data_dir, class_names_file)
    # data_prep.create_directory_structure()
    # data_prep.preprocess_images()
    # data_prep.verify_dataset()
    pass