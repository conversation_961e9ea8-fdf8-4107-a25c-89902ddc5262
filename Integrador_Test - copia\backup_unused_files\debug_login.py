#!/usr/bin/env python3
"""
Script de depuración para el login del administrador
"""

import json
import hashlib
import sys
import os

def main():
    print("🔍 DEPURACIÓN DEL LOGIN ADMINISTRADOR")
    print("=" * 50)
    
    # Verificar archivo de usuarios
    users_file = 'data/users.json'
    if not os.path.exists(users_file):
        print(f"❌ Archivo {users_file} no existe")
        return
    
    try:
        with open(users_file, 'r', encoding='utf-8') as f:
            users = json.load(f)
        print(f"✅ Archivo de usuarios cargado: {len(users)} usuarios")
    except Exception as e:
        print(f"❌ Error cargando usuarios: {e}")
        return
    
    # Verificar usuario admin
    admin = users.get("1")
    if not admin:
        print("❌ Usuario admin (ID=1) no encontrado")
        return
    
    print(f"\n📋 Datos del usuario admin:")
    print(f"   Username: {admin.get('username')}")
    print(f"   Email: {admin.get('email')}")
    print(f"   Es admin: {admin.get('is_admin')}")
    print(f"   Rol: {admin.get('role')}")
    
    # Verificar hash de contraseña
    stored_hash = admin.get('password_hash', '')
    print(f"\n🔐 Hash almacenado: {stored_hash}")
    
    # Probar contraseña
    test_password = "PlantCare2025!"
    expected_hash = hashlib.sha256(test_password.encode()).hexdigest()
    
    print(f"\n🧪 Prueba de contraseña:")
    print(f"   Contraseña: {test_password}")
    print(f"   Hash esperado: {expected_hash}")
    print(f"   Hash almacenado: {stored_hash}")
    print(f"   ¿Coinciden?: {expected_hash == stored_hash}")
    
    if expected_hash == stored_hash:
        print("\n✅ ¡La contraseña debería funcionar!")
        print("\n📋 Credenciales para usar:")
        print(f"   Usuario: admin")
        print(f"   Contraseña: {test_password}")
    else:
        print("\n❌ Los hashes no coinciden. Corrigiendo...")
        
        # Corregir hash
        admin['password_hash'] = expected_hash
        users["1"] = admin
        
        try:
            with open(users_file, 'w', encoding='utf-8') as f:
                json.dump(users, f, indent=2, ensure_ascii=False)
            print("✅ Hash corregido en el archivo")
            print("\n📋 Credenciales actualizadas:")
            print(f"   Usuario: admin")
            print(f"   Contraseña: {test_password}")
        except Exception as e:
            print(f"❌ Error guardando corrección: {e}")
    
    print("\n🔗 URLs para probar:")
    print("   Login: http://127.0.0.1:5000/login")
    print("   Panel Admin: http://127.0.0.1:5000/admin")
    
    print("\n💡 Consejos:")
    print("   - Asegúrate de que el servidor esté ejecutándose")
    print("   - Usa exactamente: admin / PlantCare2025!")
    print("   - Verifica que no haya espacios extra")
    print("   - Prueba refrescar la página de login")

if __name__ == "__main__":
    main()
