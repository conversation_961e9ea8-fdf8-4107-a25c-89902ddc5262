"""
Script de entrenamiento completo para TODAS las plantas disponibles.
Optimizado para Windows y máximo rendimiento.
"""

import os
import json
import shutil
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torchvision import datasets, transforms, models
import matplotlib.pyplot as plt
import random
from datetime import datetime

if __name__ == '__main__':
    print("🌱 INICIANDO ENTRENAMIENTO COMPLETO DE IA PARA PLANTAS 🌱")
    print("=" * 60)
    
    # Configuración
    SRC_DIR = "../plantas imagenes/PLANTAS"
    PROCESSED_DIR = "data/processed_plant_images_completo_final"
    MODEL_PATH = "models/plant_disease_model.pth"
    CLASS_NAMES_PATH = "models/class_names.json"
    BATCH_SIZE = 8  # Reducido para mejor estabilidad
    NUM_EPOCHS = 15  # Aumentado para mejor entrenamiento
    LEARNING_RATE = 0.001

    # Crear directorios
    os.makedirs(os.path.dirname(MODEL_PATH), exist_ok=True)
    os.makedirs(PROCESSED_DIR, exist_ok=True)
    
    train_dir = os.path.join(PROCESSED_DIR, 'train')
    val_dir = os.path.join(PROCESSED_DIR, 'val')
    os.makedirs(train_dir, exist_ok=True)
    os.makedirs(val_dir, exist_ok=True)

    # Lista de plantas disponibles
    plantas_disponibles = [
        'agave', 'biznaga', 'bugambilia', 'gobernadora', 'lavanda',
        'lechugilla', 'mezquite', 'naranjo', 'nopal', 'ocotillo',
        'ojo de poeta', 'palo verde', 'pata de elefante', 'rosal', 'yuca'
    ]

    print(f"📂 Procesando {len(plantas_disponibles)} tipos de plantas...")
    
    total_imagenes = 0
    clases_procesadas = []

    # Procesar cada planta
    for planta in plantas_disponibles:
        print(f"\n🌿 Procesando: {planta}")
        
        # Rutas de origen
        planta_sana_src = os.path.join(SRC_DIR, planta)
        planta_enf_src = os.path.join(SRC_DIR, f"{planta} enfermedades")
        
        # Verificar que existan las carpetas
        if not os.path.exists(planta_sana_src):
            print(f"   ⚠️  No encontrada carpeta: {planta}")
            continue
        if not os.path.exists(planta_enf_src):
            print(f"   ⚠️  No encontrada carpeta: {planta} enfermedades")
            continue
        
        # Crear directorios de destino
        planta_train = os.path.join(train_dir, planta)
        planta_enf_train = os.path.join(train_dir, f"{planta}_enfermedades")
        planta_val = os.path.join(val_dir, planta)
        planta_enf_val = os.path.join(val_dir, f"{planta}_enfermedades")
        
        for dir_path in [planta_train, planta_enf_train, planta_val, planta_enf_val]:
            os.makedirs(dir_path, exist_ok=True)
        
        # Obtener archivos de imágenes
        try:
            sana_files = [f for f in os.listdir(planta_sana_src) 
                         if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
            enf_files = [f for f in os.listdir(planta_enf_src) 
                        if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
            
            if len(sana_files) == 0 and len(enf_files) == 0:
                print(f"   ⚠️  No hay imágenes para {planta}")
                continue
            
            # Dividir en entrenamiento y validación (80/20)
            random.seed(42)
            random.shuffle(sana_files)
            random.shuffle(enf_files)
            
            sana_train = sana_files[:int(len(sana_files)*0.8)]
            sana_val = sana_files[int(len(sana_files)*0.8):]
            enf_train = enf_files[:int(len(enf_files)*0.8)]
            enf_val = enf_files[int(len(enf_files)*0.8):]
            
            # Copiar archivos
            copied_count = 0
            
            # Plantas sanas - entrenamiento
            for f in sana_train:
                try:
                    shutil.copy(os.path.join(planta_sana_src, f), os.path.join(planta_train, f))
                    copied_count += 1
                except Exception as e:
                    print(f"     Error copiando {f}: {str(e)}")
            
            # Plantas sanas - validación
            for f in sana_val:
                try:
                    shutil.copy(os.path.join(planta_sana_src, f), os.path.join(planta_val, f))
                    copied_count += 1
                except Exception as e:
                    print(f"     Error copiando {f}: {str(e)}")
            
            # Plantas enfermas - entrenamiento
            for f in enf_train:
                try:
                    shutil.copy(os.path.join(planta_enf_src, f), os.path.join(planta_enf_train, f))
                    copied_count += 1
                except Exception as e:
                    print(f"     Error copiando {f}: {str(e)}")
            
            # Plantas enfermas - validación
            for f in enf_val:
                try:
                    shutil.copy(os.path.join(planta_enf_src, f), os.path.join(planta_enf_val, f))
                    copied_count += 1
                except Exception as e:
                    print(f"     Error copiando {f}: {str(e)}")
            
            print(f"   ✅ {planta}: {len(sana_train)}+{len(sana_val)} sanas, {len(enf_train)}+{len(enf_val)} enfermas")
            print(f"      Total copiadas: {copied_count}")
            
            total_imagenes += copied_count
            clases_procesadas.extend([planta, f"{planta}_enfermedades"])
            
        except Exception as e:
            print(f"   ❌ Error procesando {planta}: {str(e)}")
            continue

    print(f"\n📊 RESUMEN DEL PROCESAMIENTO:")
    print(f"   Total de imágenes procesadas: {total_imagenes}")
    print(f"   Total de clases: {len(clases_procesadas)}")
    print(f"   Clases: {clases_procesadas}")

    if total_imagenes == 0:
        print("❌ No se procesaron imágenes. Terminando.")
        exit(1)

    # Transformaciones de datos
    print(f"\n🔄 Configurando transformaciones de datos...")
    data_transforms = {
        'train': transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.RandomHorizontalFlip(p=0.5),
            transforms.RandomRotation(10),
            transforms.ColorJitter(brightness=0.1, contrast=0.1),
            transforms.ToTensor(),
            transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
        ]),
        'val': transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
        ]),
    }

    # Cargar datasets
    print(f"📚 Cargando datasets...")
    try:
        image_datasets = {
            'train': datasets.ImageFolder(train_dir, data_transforms['train']),
            'val': datasets.ImageFolder(val_dir, data_transforms['val'])
        }
        
        dataloaders = {
            'train': DataLoader(image_datasets['train'], batch_size=BATCH_SIZE,
                               shuffle=True, num_workers=0),
            'val': DataLoader(image_datasets['val'], batch_size=BATCH_SIZE,
                             shuffle=False, num_workers=0)
        }
        
        dataset_sizes = {x: len(image_datasets[x]) for x in ['train', 'val']}
        class_names = image_datasets['train'].classes
        
        print(f"✅ Datasets cargados exitosamente:")
        print(f"   Entrenamiento: {dataset_sizes['train']} imágenes")
        print(f"   Validación: {dataset_sizes['val']} imágenes")
        print(f"   Clases detectadas: {len(class_names)}")
        
        # Guardar nombres de clases
        with open(CLASS_NAMES_PATH, 'w', encoding='utf-8') as f:
            json.dump(class_names, f, ensure_ascii=False, indent=2)
        print(f"💾 Nombres de clases guardados en: {CLASS_NAMES_PATH}")
        
    except Exception as e:
        print(f"❌ Error cargando datasets: {str(e)}")
        exit(1)

    # Configurar dispositivo y modelo
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"\n🖥️  Usando dispositivo: {device}")
    
    print(f"🤖 Creando modelo ResNet18...")
    model = models.resnet18(weights='IMAGENET1K_V1')
    
    # Congelar capas base para transfer learning
    for param in model.parameters():
        param.requires_grad = False
    
    # Reemplazar clasificador final
    num_ftrs = model.fc.in_features
    model.fc = nn.Sequential(
        nn.Linear(num_ftrs, 512),
        nn.ReLU(),
        nn.Dropout(0.3),
        nn.Linear(512, 256),
        nn.ReLU(),
        nn.Dropout(0.2),
        nn.Linear(256, len(class_names))
    )
    
    model = model.to(device)
    
    # Configurar entrenamiento
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.fc.parameters(), lr=LEARNING_RATE)
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)
    
    print(f"🚀 INICIANDO ENTRENAMIENTO - {NUM_EPOCHS} ÉPOCAS")
    print("=" * 60)
    
    best_acc = 0.0
    history = {'train_loss': [], 'val_loss': [], 'train_acc': [], 'val_acc': []}
    start_time = datetime.now()
    
    for epoch in range(NUM_EPOCHS):
        print(f'\n📈 Época {epoch+1}/{NUM_EPOCHS}')
        print('-' * 40)
        
        for phase in ['train', 'val']:
            if phase == 'train':
                model.train()
            else:
                model.eval()
            
            running_loss = 0.0
            running_corrects = 0
            
            batch_count = 0
            for inputs, labels in dataloaders[phase]:
                inputs = inputs.to(device)
                labels = labels.to(device)
                
                optimizer.zero_grad()
                
                with torch.set_grad_enabled(phase == 'train'):
                    outputs = model(inputs)
                    _, preds = torch.max(outputs, 1)
                    loss = criterion(outputs, labels)
                    
                    if phase == 'train':
                        loss.backward()
                        optimizer.step()
                
                running_loss += loss.item() * inputs.size(0)
                running_corrects += torch.sum(preds == labels.data)
                
                batch_count += 1
                if batch_count % 10 == 0:
                    print(f"   Batch {batch_count}: Loss {loss.item():.4f}")
            
            if phase == 'train':
                scheduler.step()
            
            epoch_loss = running_loss / dataset_sizes[phase]
            epoch_acc = running_corrects.double() / dataset_sizes[phase]
            
            history[f'{phase}_loss'].append(epoch_loss)
            history[f'{phase}_acc'].append(epoch_acc.item())
            
            print(f'{phase.upper()} - Loss: {epoch_loss:.4f} Acc: {epoch_acc:.4f}')
            
            if phase == 'val' and epoch_acc > best_acc:
                best_acc = epoch_acc
                torch.save(model, MODEL_PATH)
                print(f'🏆 ¡Nuevo mejor modelo! Precisión: {best_acc:.4f}')
    
    end_time = datetime.now()
    training_time = end_time - start_time
    
    print(f"\n🎉 ¡ENTRENAMIENTO COMPLETADO!")
    print("=" * 60)
    print(f"⏱️  Tiempo total: {training_time}")
    print(f"🏆 Mejor precisión: {best_acc:.4f}")
    print(f"💾 Modelo guardado en: {MODEL_PATH}")
    print(f"📋 Clases guardadas en: {CLASS_NAMES_PATH}")
    
    # Crear gráfica de entrenamiento
    try:
        plt.figure(figsize=(15, 5))
        
        plt.subplot(1, 2, 1)
        plt.plot(history['train_loss'], label='Entrenamiento', color='blue')
        plt.plot(history['val_loss'], label='Validación', color='red')
        plt.title('Pérdida del Modelo')
        plt.ylabel('Pérdida')
        plt.xlabel('Época')
        plt.legend()
        plt.grid(True)
        
        plt.subplot(1, 2, 2)
        plt.plot(history['train_acc'], label='Entrenamiento', color='blue')
        plt.plot(history['val_acc'], label='Validación', color='red')
        plt.title('Precisión del Modelo')
        plt.ylabel('Precisión')
        plt.xlabel('Época')
        plt.legend()
        plt.grid(True)
        
        plt.tight_layout()
        
        plot_path = os.path.join(os.path.dirname(MODEL_PATH), 'training_history.png')
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"📊 Gráfica guardada en: {plot_path}")
        
    except Exception as e:
        print(f"⚠️  No se pudo crear la gráfica: {str(e)}")
    
    print(f"\n🌟 ¡TU IA ESTÁ LISTA PARA DETECTAR {len(class_names)} TIPOS DE PLANTAS!")
    print("🚀 Ahora puedes usar el scanner en tu aplicación web.")
