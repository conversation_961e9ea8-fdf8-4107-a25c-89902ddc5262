"""
Script final para entrenar un modelo con imágenes de bugambilia.
"""

import os
import json
import shutil
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torchvision import datasets, transforms, models
import matplotlib.pyplot as plt
import random

if __name__ == '__main__':
    # Configuración
    SRC_DIR = "../plantas imagenes/PLANTAS"  # Directorio con las imágenes originales
    PROCESSED_DIR = "data/processed_plant_images_final"  # Directorio para imágenes procesadas
    MODEL_PATH = "models/plant_disease_model.pth"  # Ruta para guardar el modelo
    CLASS_NAMES_PATH = "models/class_names.json"  # Ruta para guardar nombres de clases
    BATCH_SIZE = 16
    NUM_EPOCHS = 10

    # Asegurarse de que existan los directorios
    os.makedirs(os.path.dirname(MODEL_PATH), exist_ok=True)
    os.makedirs(PROCESSED_DIR, exist_ok=True)

# Crear directorios para datos procesados
train_dir = os.path.join(PROCESSED_DIR, 'train')
val_dir = os.path.join(PROCESSED_DIR, 'val')
os.makedirs(train_dir, exist_ok=True)
os.makedirs(val_dir, exist_ok=True)

# Crear directorios para las clases
bugambilia_train = os.path.join(train_dir, 'bugambilia')
bugambilia_enf_train = os.path.join(train_dir, 'bugambilia_enfermedades')
bugambilia_val = os.path.join(val_dir, 'bugambilia')
bugambilia_enf_val = os.path.join(val_dir, 'bugambilia_enfermedades')

os.makedirs(bugambilia_train, exist_ok=True)
os.makedirs(bugambilia_enf_train, exist_ok=True)
os.makedirs(bugambilia_val, exist_ok=True)
os.makedirs(bugambilia_enf_val, exist_ok=True)

# Copiar imágenes de bugambilia
print("Copiando imágenes de bugambilia...")
bugambilia_src = os.path.join(SRC_DIR, 'bugambilia')
bugambilia_enf_src = os.path.join(SRC_DIR, 'bugambilia enfermedades')

# Verificar que las carpetas existan
if not os.path.exists(bugambilia_src):
    print(f"Error: No se encontró la carpeta {bugambilia_src}")
    exit(1)
if not os.path.exists(bugambilia_enf_src):
    print(f"Error: No se encontró la carpeta {bugambilia_enf_src}")
    exit(1)

# Obtener archivos de imágenes
bugambilia_files = [f for f in os.listdir(bugambilia_src) 
                   if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
bugambilia_enf_files = [f for f in os.listdir(bugambilia_enf_src) 
                       if f.lower().endswith(('.jpg', '.jpeg', '.png'))]

# Dividir en conjuntos de entrenamiento y validación
random.seed(42)
random.shuffle(bugambilia_files)
random.shuffle(bugambilia_enf_files)

bugambilia_train_files = bugambilia_files[:int(len(bugambilia_files)*0.8)]
bugambilia_val_files = bugambilia_files[int(len(bugambilia_files)*0.8):]
bugambilia_enf_train_files = bugambilia_enf_files[:int(len(bugambilia_enf_files)*0.8)]
bugambilia_enf_val_files = bugambilia_enf_files[int(len(bugambilia_enf_files)*0.8):]

# Copiar archivos
for f in bugambilia_train_files:
    try:
        shutil.copy(os.path.join(bugambilia_src, f), os.path.join(bugambilia_train, f))
    except Exception as e:
        print(f"Error al copiar {f}: {str(e)}")

for f in bugambilia_val_files:
    try:
        shutil.copy(os.path.join(bugambilia_src, f), os.path.join(bugambilia_val, f))
    except Exception as e:
        print(f"Error al copiar {f}: {str(e)}")

for f in bugambilia_enf_train_files:
    try:
        shutil.copy(os.path.join(bugambilia_enf_src, f), os.path.join(bugambilia_enf_train, f))
    except Exception as e:
        print(f"Error al copiar {f}: {str(e)}")

for f in bugambilia_enf_val_files:
    try:
        shutil.copy(os.path.join(bugambilia_enf_src, f), os.path.join(bugambilia_enf_val, f))
    except Exception as e:
        print(f"Error al copiar {f}: {str(e)}")

print(f"Imágenes de bugambilia: {len(bugambilia_train_files)} entrenamiento, {len(bugambilia_val_files)} validación")
print(f"Imágenes de bugambilia enfermedades: {len(bugambilia_enf_train_files)} entrenamiento, {len(bugambilia_enf_val_files)} validación")

# Transformaciones para las imágenes
data_transforms = {
    'train': transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.RandomHorizontalFlip(),
        transforms.RandomRotation(15),
        transforms.ColorJitter(brightness=0.1, contrast=0.1, saturation=0.1),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    ]),
    'val': transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    ]),
}

# Cargar los conjuntos de datos
print("Cargando conjuntos de datos...")
try:
    # Cargar los conjuntos de datos
    image_datasets = {
        'train': datasets.ImageFolder(train_dir, data_transforms['train']),
        'val': datasets.ImageFolder(val_dir, data_transforms['val'])
    }
    
    # Crear dataloaders (sin multiprocessing para Windows)
    dataloaders = {
        'train': DataLoader(image_datasets['train'], batch_size=BATCH_SIZE,
                           shuffle=True, num_workers=0),
        'val': DataLoader(image_datasets['val'], batch_size=BATCH_SIZE,
                         shuffle=False, num_workers=0)
    }
    
    # Obtener tamaños de los conjuntos de datos
    dataset_sizes = {x: len(image_datasets[x]) for x in ['train', 'val']}
    
    # Obtener nombres de clases y guardarlos
    class_names = image_datasets['train'].classes
    class_to_idx = image_datasets['train'].class_to_idx
    
    print(f"Clases encontradas: {class_names}")
    print(f"Imágenes de entrenamiento: {dataset_sizes['train']}")
    print(f"Imágenes de validación: {dataset_sizes['val']}")
    
    # Guardar los nombres de las clases en un archivo JSON
    with open(CLASS_NAMES_PATH, 'w', encoding='utf-8') as f:
        json.dump(class_names, f, ensure_ascii=False, indent=2)
    
    print(f"Nombres de clases guardados en {CLASS_NAMES_PATH}")
    
    # Configurar el dispositivo (GPU o CPU)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Usando dispositivo: {device}")
    
    # Crear un modelo preentrenado
    print("Creando modelo...")
    model = models.resnet18(weights='IMAGENET1K_V1')
    
    # Congelar todos los parámetros para que no se actualicen durante el entrenamiento
    for param in model.parameters():
        param.requires_grad = False
    
    # Reemplazar la capa final para nuestra tarea de clasificación
    num_ftrs = model.fc.in_features
    model.fc = nn.Sequential(
        nn.Linear(num_ftrs, 256),
        nn.ReLU(),
        nn.Dropout(0.2),
        nn.Linear(256, len(class_names))
    )
    
    # Mover el modelo al dispositivo (GPU o CPU)
    model = model.to(device)
    
    # Definir función de pérdida y optimizador
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.fc.parameters(), lr=0.001)
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)
    
    # Entrenar el modelo
    print(f"Iniciando entrenamiento por {NUM_EPOCHS} épocas...")
    
    best_acc = 0.0
    history = {'train_loss': [], 'val_loss': [], 'train_acc': [], 'val_acc': []}
    
    for epoch in range(NUM_EPOCHS):
        print(f'Época {epoch+1}/{NUM_EPOCHS}')
        print('-' * 10)
        
        # Cada época tiene una fase de entrenamiento y validación
        for phase in ['train', 'val']:
            if phase == 'train':
                model.train()  # Modo de entrenamiento
            else:
                model.eval()   # Modo de evaluación
            
            running_loss = 0.0
            running_corrects = 0
            
            # Iterar sobre los datos
            for inputs, labels in dataloaders[phase]:
                inputs = inputs.to(device)
                labels = labels.to(device)
                
                # Poner a cero los gradientes del parámetro
                optimizer.zero_grad()
                
                # Forward
                with torch.set_grad_enabled(phase == 'train'):
                    outputs = model(inputs)
                    _, preds = torch.max(outputs, 1)
                    loss = criterion(outputs, labels)
                    
                    # Backward + optimize solo si es fase de entrenamiento
                    if phase == 'train':
                        loss.backward()
                        optimizer.step()
                
                # Estadísticas
                running_loss += loss.item() * inputs.size(0)
                running_corrects += torch.sum(preds == labels.data)
            
            if phase == 'train':
                scheduler.step()
            
            epoch_loss = running_loss / dataset_sizes[phase]
            epoch_acc = running_corrects.double() / dataset_sizes[phase]
            
            # Guardar historial
            if phase == 'train':
                history['train_loss'].append(epoch_loss)
                history['train_acc'].append(epoch_acc.item())
            else:
                history['val_loss'].append(epoch_loss)
                history['val_acc'].append(epoch_acc.item())
            
            print(f'{phase} Loss: {epoch_loss:.4f} Acc: {epoch_acc:.4f}')
            
            # Guardar el mejor modelo
            if phase == 'val' and epoch_acc > best_acc:
                best_acc = epoch_acc
                torch.save(model, MODEL_PATH)
                print(f"Mejor modelo guardado con precisión: {best_acc:.4f}")
        
        print()
    
    print(f'Entrenamiento completado. Mejor precisión de validación: {best_acc:.4f}')
    
    # Graficar el historial de entrenamiento
    plt.figure(figsize=(12, 4))
    
    # Graficar pérdida
    plt.subplot(1, 2, 1)
    plt.plot(history['train_loss'], label='Entrenamiento')
    plt.plot(history['val_loss'], label='Validación')
    plt.title('Pérdida del modelo')
    plt.ylabel('Pérdida')
    plt.xlabel('Época')
    plt.legend()
    
    # Graficar precisión
    plt.subplot(1, 2, 2)
    plt.plot(history['train_acc'], label='Entrenamiento')
    plt.plot(history['val_acc'], label='Validación')
    plt.title('Precisión del modelo')
    plt.ylabel('Precisión')
    plt.xlabel('Época')
    plt.legend()
    
    plt.tight_layout()
    
    # Guardar la gráfica
    plot_path = os.path.join(os.path.dirname(MODEL_PATH), 'training_history.png')
    plt.savefig(plot_path)
    print(f"Gráfica de entrenamiento guardada en {plot_path}")
    plt.close()
    
    print("\n=== Entrenamiento completado ===")
    print(f"Modelo guardado en: {MODEL_PATH}")
    print(f"Nombres de clases guardados en: {CLASS_NAMES_PATH}")
    print(f"Gráfica de entrenamiento guardada en: {plot_path}")
    
except Exception as e:
    print(f"Error durante el entrenamiento: {str(e)}")
    import traceback
    traceback.print_exc()
