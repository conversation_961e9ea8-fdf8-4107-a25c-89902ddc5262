#!/usr/bin/env python3
"""
Corrección final y definitiva de TODAS las rutas problemáticas
"""

import os
import re

def final_fix_all_routes():
    """Corrección final de todas las rutas problemáticas"""
    
    print("🔧 CORRECCIÓN FINAL DE TODAS LAS RUTAS")
    print("=" * 50)
    
    templates_dir = "templates"
    fixed_files = []
    
    # Buscar CUALQUIER url_for que no sea para archivos estáticos
    problematic_pattern = r"url_for\('(?!static)[^']+'\)"
    
    for root, dirs, files in os.walk(templates_dir):
        for file in files:
            if file.endswith('.html'):
                file_path = os.path.join(root, file)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    original_content = content
                    
                    # Encontrar todas las rutas problemáticas
                    matches = re.findall(problematic_pattern, content)
                    
                    if matches:
                        print(f"\n📄 {file_path}:")
                        for match in matches:
                            print(f"   ❌ {match}")
                        
                        # Aplicar correcciones específicas
                        replacements = {
                            # Auth
                            r"url_for\('auth\.login'\)": '"/login"',
                            r"url_for\('auth\.register'\)": '"/register"',
                            r"url_for\('auth\.logout'\)": '"/login"',
                            
                            # Profile
                            r"url_for\('profile\.index'\)": '"/perfil"',
                            r"url_for\('profile\.user_profile'\)": '"/perfil"',
                            r"url_for\('profile\.ajustes'\)": '"/ajustes"',
                            
                            # Plants
                            r"url_for\('plants\.biblioteca'\)": '"/biblioteca"',
                            r"url_for\('plants\.plant_detail',\s*plant_id=\d+\)": '"/biblioteca"',
                            
                            # Diagnosis
                            r"url_for\('diagnosis\.scanner'\)": '"/diagnosis/scanner"',
                            
                            # Reminders
                            r"url_for\('reminders\.calendario'\)": '"/calendario"',
                            r"url_for\('reminders\.calendar'\)": '"/calendario"',
                            
                            # Forum
                            r"url_for\('forum\.index'\)": '"/foro"',
                            r"url_for\('forum\.forum_home'\)": '"/foro"',
                            
                            # Settings
                            r"url_for\('settings\.index'\)": '"/ajustes"',
                            r"url_for\('settings\.update_privacy'\)": '"/ajustes"',
                            
                            # Views
                            r"url_for\('views\.login'\)": '"/login"',
                            r"url_for\('views\.register'\)": '"/register"',
                            
                            # Recomendaciones
                            r"url_for\('recomendaciones\.index'\)": '"/recomendaciones"',
                            
                            # Alt routes
                            r"url_for\('calendario_alt'\)": '"/calendario"',
                            r"url_for\('biblioteca_alt'\)": '"/biblioteca"',
                            
                            # Simple routes
                            r"url_for\('home'\)": '"/"',
                            r"url_for\('biblioteca'\)": '"/biblioteca"',
                            r"url_for\('scanner'\)": '"/scanner"',
                            r"url_for\('calendario'\)": '"/calendario"',
                            r"url_for\('recomendaciones'\)": '"/recomendaciones"',
                            r"url_for\('foro'\)": '"/foro"',
                            r"url_for\('perfil'\)": '"/perfil"',
                            r"url_for\('ajustes'\)": '"/ajustes"',
                            r"url_for\('index'\)": '"/"',
                            r"url_for\('login'\)": '"/login"',
                            r"url_for\('register'\)": '"/register"',
                        }
                        
                        changes_made = 0
                        for pattern, replacement in replacements.items():
                            if re.search(pattern, content):
                                content = re.sub(pattern, replacement, content)
                                changes_made += 1
                        
                        # Corrección agresiva: reemplazar cualquier url_for restante
                        remaining_matches = re.findall(problematic_pattern, content)
                        for match in remaining_matches:
                            # Extraer el nombre de la ruta
                            route_name = match.replace("url_for('", "").replace("')", "")
                            
                            # Mapear a rutas simples
                            if 'login' in route_name:
                                simple_route = '"/login"'
                            elif 'register' in route_name:
                                simple_route = '"/register"'
                            elif 'biblioteca' in route_name or 'plants' in route_name:
                                simple_route = '"/biblioteca"'
                            elif 'scanner' in route_name or 'diagnosis' in route_name:
                                simple_route = '"/diagnosis/scanner"'
                            elif 'calendario' in route_name or 'calendar' in route_name or 'reminders' in route_name:
                                simple_route = '"/calendario"'
                            elif 'recomendaciones' in route_name:
                                simple_route = '"/recomendaciones"'
                            elif 'foro' in route_name or 'forum' in route_name:
                                simple_route = '"/foro"'
                            elif 'perfil' in route_name or 'profile' in route_name:
                                simple_route = '"/perfil"'
                            elif 'ajustes' in route_name or 'settings' in route_name:
                                simple_route = '"/ajustes"'
                            elif 'home' in route_name or 'index' in route_name:
                                simple_route = '"/"'
                            else:
                                simple_route = '"/"'  # Default fallback
                            
                            content = content.replace(f"url_for('{route_name}')", simple_route)
                            changes_made += 1
                        
                        # Corregir sintaxis {{ "/ruta" }}
                        syntax_fixes = [
                            (r'\{\{\s*"(/[^"]*?)"\s*\}\}', r'\1'),
                            (r'href="\{\{\s*"(/[^"]*?)"\s*\}\}"', r'href="\1"'),
                            (r'action="\{\{\s*"(/[^"]*?)"\s*\}\}"', r'action="\1"'),
                            (r'src="\{\{\s*"(/[^"]*?)"\s*\}\}"', r'src="\1"'),
                        ]
                        
                        for pattern, replacement in syntax_fixes:
                            content = re.sub(pattern, replacement, content)
                        
                        # Guardar archivo corregido
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(content)
                        
                        fixed_files.append((file_path, changes_made))
                        print(f"   ✅ Corregido con {changes_made} cambios")
                
                except Exception as e:
                    print(f"❌ Error procesando {file_path}: {e}")
    
    print(f"\n🎉 CORRECCIÓN COMPLETADA")
    print(f"   - Archivos corregidos: {len(fixed_files)}")
    
    if fixed_files:
        print("\n📋 Resumen de cambios:")
        for file_path, changes in fixed_files:
            print(f"   - {file_path}: {changes} cambios")
    
    return len(fixed_files)

if __name__ == "__main__":
    files_fixed = final_fix_all_routes()
    
    if files_fixed > 0:
        print(f"\n🔄 Reinicia el servidor para aplicar los {files_fixed} archivos corregidos")
    else:
        print("\n✅ No se encontraron rutas problemáticas")
    
    print("🎯 Todas las rutas deberían funcionar correctamente ahora")
