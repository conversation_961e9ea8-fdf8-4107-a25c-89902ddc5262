from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migra<PERSON>
from flask import Flask, g
import pyodbc

# Initialize SQLAlchemy without binding to an app yet
db = SQLAlchemy()

def init_app(app):
    """Initialize database extensions with the Flask application.
    
    This function connects the database and related extensions to the Flask app.
    It should be called from the application factory function.
    
    Args:
        app: The Flask application instance
    
    Returns:
        Migrate: The Flask-Migrate extension instance for database migrations
    """
    # Configure SQLAlchemy if not already configured
    if not app.config.get('SQLALCHEMY_DATABASE_URI'):
        app.config['SQLALCHEMY_DATABASE_URI'] = 'mssql+pyodbc://sa:****@David/plantcaredb?driver=ODBC+Driver+17+for+SQL+Server'
        app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # Initialize database extensions
    db.init_app(app)
    migrate = Migrate(app, db)
    
    # Set up database connection helper function
    def get_db_connection():
        """Maintains a single database connection per request"""
        if 'db_conn' not in g:
            connection_string = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=David;DATABASE=plantcaredb;UID=sa;PWD=****'
            g.db_conn = pyodbc.connect(connection_string)
        return g.db_conn
    
    # Set up database execution utility function
    def execute_query(query, params=None, fetch=True):
        """Execute a SQL query and optionally return results"""
        conn = get_db_connection()
        cursor = conn.cursor()
        
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
                
            if fetch:
                return cursor.fetchall()
            else:
                conn.commit()
                return True
        except Exception as e:
            conn.rollback()
            raise e
    
    # Set up stored procedure execution utility function
    def call_stored_procedure(procedure_name, params=None, fetch=True):
        """Execute a stored procedure and optionally return results"""
        conn = get_db_connection()
        cursor = conn.cursor()
        
        try:
            if params:
                param_placeholders = ','.join(['?' for _ in params])
                cursor.execute(f"EXEC {procedure_name} {param_placeholders}", params)
            else:
                cursor.execute(f"EXEC {procedure_name}")
                
            if fetch:
                return cursor.fetchall()
            else:
                conn.commit()
                return True
        except Exception as e:
            conn.rollback()
            raise e
    
    # Close database connections at the end of each request
    @app.teardown_appcontext
    def close_db_connection(exception):
        """Close the database connection at the end of each request"""
        conn = g.pop('db_conn', None)
        if conn is not None:
            conn.close()
    
    # Make database utilities available for import from routes
    app.config['get_db_connection'] = get_db_connection
    app.config['execute_query'] = execute_query
    app.config['call_stored_procedure'] = call_stored_procedure
    
    return migrate