import pyodbc
import os

def init_database():
    """Initialize the database with the SQL schema file."""
    try:
        # Connect to server (without specifying a database)
        conn = pyodbc.connect(
            'DRIVER={ODBC Driver 17 for SQL Server};'
            'SERVER=David;'
            'UID=sa;'
            'PWD=1234;'
        )
        
        cursor = conn.cursor()
        
        # Check if database exists
        cursor.execute("SELECT database_id FROM sys.databases WHERE Name = 'plantcare'")
        result = cursor.fetchone()
        
        if result:
            print("Database 'plantcare' already exists.")
        else:
            # Create database
            cursor.execute("CREATE DATABASE plantcare")
            print("Database 'plantcare' created successfully.")
        
        conn.close()
        
        # Connect to the new database
        conn = pyodbc.connect(
            'DRIVER={ODBC Driver 17 for SQL Server};'
            'SERVER=David;'
            'DATABASE=plantcare;'
            'UID=sa;'
            'PWD=1234;'
        )
        
        cursor = conn.cursor()
        
        # Read SQL file
        with open('Plantcare78.sql', 'r', encoding='utf-8') as f:
            sql_script = f.read()
        
        # Split the script on GO statements (SQL Server batch separator)
        batches = sql_script.split('GO')
        
        # Execute each batch
        for batch in batches:
            if batch.strip():
                try:
                    cursor.execute(batch)
                    conn.commit()
                except Exception as e:
                    print(f"Error executing batch: {str(e)}")
                    conn.rollback()
        
        cursor.close()
        conn.close()
        
        print("Database schema created successfully.")
        
    except Exception as e:
        print(f"Error initializing database: {str(e)}")

if __name__ == '__main__':
    init_database()