"""
Script para inicializar la base de datos MySQL para PlantCare usando SQLAlchemy.
Este script crea las tablas necesarias utilizando los modelos de SQLAlchemy.
"""

from flask import Flask
from config import Config
from database import db
from sqlalchemy import create_engine, text
import os

def create_app():
    """Crear una aplicación Flask para inicializar la base de datos"""
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # Inicializar la base de datos
    db.init_app(app)
    
    return app

def create_database(app):
    """Crear la base de datos si no existe"""
    # Extraer el nombre de la base de datos de la URL
    db_name = app.config['MYSQL_DB']
    
    # Crear una conexión a MySQL sin especificar la base de datos
    engine = create_engine(f"mysql+pymysql://{app.config['MYSQL_USER']}:{app.config['MYSQL_PASSWORD']}@{app.config['MYSQL_HOST']}")
    
    # Crear la base de datos si no existe
    with engine.connect() as conn:
        conn.execute(text(f"CREATE DATABASE IF NOT EXISTS {db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"))
        print(f"Base de datos '{db_name}' creada o ya existente")

def create_tables(app):
    """Crear las tablas usando SQLAlchemy"""
    with app.app_context():
        # Importar los modelos aquí para evitar problemas de importación circular
        from models.models import User, Plant, PlantType, PlantFamily, HealthStatus, PlantCare, SoilType, LightCondition
        from models.models import Reminder, ReminderType
        
        # Crear todas las tablas
        db.create_all()
        print("Tablas creadas correctamente")

def main():
    """Función principal para inicializar la base de datos"""
    app = create_app()
    
    try:
        # Crear la base de datos
        create_database(app)
        
        # Crear las tablas
        create_tables(app)
        
        print("Inicialización de la base de datos MySQL completada")
    except Exception as e:
        print(f"Error al inicializar la base de datos: {e}")

if __name__ == "__main__":
    main()
