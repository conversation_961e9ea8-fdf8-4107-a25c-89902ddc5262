"""
Script para inicializar la base de datos MySQL con los modelos de SQLAlchemy.
Este script crea las tablas necesarias para la aplicación.
"""

from flask import Flask
from config import Config
from database import db
import os

def create_app():
    """Crear una aplicación Flask para inicializar la base de datos"""
    app = Flask(__name__)
    app.config.from_object(Config)

    # Inicializar la base de datos
    db.init_app(app)

    return app

def initialize_database(app):
    """Inicializar la base de datos con los modelos de SQLAlchemy"""
    with app.app_context():
        # Importar los modelos aquí para evitar problemas de importación circular
        try:
            # Intentar importar todos los modelos
            from models.models import User, Plant, PlantType, PlantFamily, HealthStatus, PlantCare, SoilType, LightCondition
            from models.models import Reminder, ReminderType
            from models.models import Diagnosis, Disease
            from models.models import ForumTopic, ForumReply
            from models.models import Event, EventRegistration, Store

            # Crear todas las tablas
            db.create_all()
            print("Tablas creadas correctamente")

            # Verificar si se crearon las tablas
            # En versiones más recientes de SQLAlchemy, usamos inspector
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()

            if tables:
                print("\nTablas creadas en la base de datos:")
                for table in tables:
                    print(f"- {table}")
            else:
                print("\nNo se encontraron tablas en la base de datos.")

        except ImportError as e:
            print(f"Error al importar modelos: {e}")
            print("Asegúrate de que todos los modelos estén definidos correctamente.")
        except Exception as e:
            print(f"Error al crear las tablas: {e}")

def main():
    """Función principal para inicializar la base de datos"""
    app = create_app()

    try:
        # Inicializar la base de datos
        initialize_database(app)

        print("\nInicialización de la base de datos completada")
    except Exception as e:
        print(f"Error al inicializar la base de datos: {e}")

if __name__ == "__main__":
    main()
