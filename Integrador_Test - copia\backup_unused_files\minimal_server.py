#!/usr/bin/env python3
"""
Servidor mínimo para demostrar el diagnóstico de IA
"""

from flask import Flask, request, jsonify, render_template_string
import os
import json
import random
import time
import base64

app = Flask(__name__)
app.config['SECRET_KEY'] = 'test_key'

# Crear directorio de uploads
os.makedirs('uploads/diagnosis', exist_ok=True)

# Template HTML simple para el scanner
SCANNER_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>Scanner IA - PlantCare</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .container { text-align: center; }
        .upload-area { border: 2px dashed #4CAF50; padding: 40px; margin: 20px 0; border-radius: 10px; }
        .btn { background: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        .btn:hover { background: #45a049; }
        #result { margin-top: 20px; padding: 20px; background: #f0f0f0; border-radius: 5px; display: none; }
        .loading { color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌱 Scanner IA de Plantas</h1>
        <p>Sube una imagen de tu planta para obtener un diagnóstico con IA</p>
        
        <div class="upload-area">
            <input type="file" id="fileInput" accept="image/*" style="display: none;">
            <button class="btn" onclick="document.getElementById('fileInput').click()">
                📸 Seleccionar Imagen
            </button>
            <p>Formatos soportados: JPG, PNG, GIF</p>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                uploadImage(file);
            }
        });

        function uploadImage(file) {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="loading">🤖 Analizando imagen con IA...</div>';

            const formData = new FormData();
            formData.append('file', file);

            fetch('/diagnosis/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.href = '/diagnosis/result/' + data.filename;
                } else {
                    resultDiv.innerHTML = '<div style="color: red;">❌ Error: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<div style="color: red;">❌ Error de conexión: ' + error + '</div>';
            });
        }
    </script>
</body>
</html>
'''

# Template para mostrar resultados
RESULT_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>Resultado del Diagnóstico - PlantCare</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .result-card { background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); padding: 20px; margin: 20px 0; }
        .plant-image { max-width: 300px; border-radius: 10px; margin: 20px 0; }
        .confidence { background: #4CAF50; color: white; padding: 5px 10px; border-radius: 20px; display: inline-block; }
        .recommendations { background: #f9f9f9; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .btn { background: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn:hover { background: #45a049; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌱 Resultado del Diagnóstico IA</h1>
        
        <div class="result-card">
            {% if image_base64 %}
            <img src="data:image/jpeg;base64,{{ image_base64 }}" alt="Planta analizada" class="plant-image">
            {% endif %}
            
            <h2>{{ plant_name }}</h2>
            <p><em>{{ scientific_name }}</em></p>
            
            <div class="confidence">
                🎯 {{ "%.1f"|format(confidence) }}% de confianza
            </div>
            
            <div class="recommendations">
                <h3>🔬 Diagnóstico:</h3>
                <p><strong>Estado detectado:</strong> {{ disease_name }}</p>
                
                <h3>💡 Recomendaciones:</h3>
                <p>{{ recommendations }}</p>
            </div>
            
            <a href="/diagnosis/scanner" class="btn">🔄 Analizar otra planta</a>
        </div>
    </div>
</body>
</html>
'''

@app.route('/')
def index():
    return '<h1>PlantCare IA</h1><p><a href="/diagnosis/scanner">Ir al Scanner</a></p>'

@app.route('/diagnosis/scanner')
def scanner():
    return render_template_string(SCANNER_TEMPLATE)

@app.route('/diagnosis/upload', methods=['POST'])
def upload():
    try:
        print("📤 Recibida solicitud de upload")
        
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        # Generar nombre único
        import uuid
        filename = f"plant_{uuid.uuid4().hex[:8]}.jpg"
        filepath = os.path.join('uploads/diagnosis', filename)
        
        # Guardar archivo
        file.save(filepath)
        print(f"💾 Archivo guardado: {filepath}")
        
        # Simular procesamiento IA
        print("🤖 Simulando análisis IA...")
        time.sleep(2)
        
        return jsonify({'success': True, 'filename': filename})
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/diagnosis/result/<filename>')
def result(filename):
    try:
        print(f"📊 Mostrando resultado para: {filename}")
        
        # Plantas de ejemplo
        plantas = [
            {
                'name': 'Pata de Elefante',
                'scientific': 'Beaucarnea recurvata',
                'disease': 'Planta saludable',
                'confidence': random.uniform(75, 95),
                'recommendations': 'Tu pata de elefante está en excelente estado. Mantén un riego moderado cada 2-3 semanas y proporciona luz brillante indirecta. Es una planta muy resistente, perfecta para principiantes.'
            },
            {
                'name': 'Bugambilia',
                'scientific': 'Bougainvillea spectabilis',
                'disease': 'Planta saludable',
                'confidence': random.uniform(70, 90),
                'recommendations': 'Tu bugambilia se ve saludable. Riega cuando el suelo esté seco y proporciona pleno sol para una floración abundante. Poda regularmente para mantener la forma deseada.'
            },
            {
                'name': 'Agave',
                'scientific': 'Agave americana',
                'disease': 'Planta saludable',
                'confidence': random.uniform(80, 95),
                'recommendations': 'Tu agave está en perfecto estado. Requiere muy poco riego (una vez al mes) y pleno sol. Es ideal para jardines de bajo mantenimiento en clima árido como Chihuahua.'
            }
        ]
        
        planta = random.choice(plantas)
        
        # Leer imagen si existe
        filepath = os.path.join('uploads/diagnosis', filename)
        image_base64 = None
        
        if os.path.exists(filepath):
            with open(filepath, 'rb') as f:
                image_data = f.read()
            image_base64 = base64.b64encode(image_data).decode('utf-8')
        
        return render_template_string(
            RESULT_TEMPLATE,
            image_base64=image_base64,
            plant_name=planta['name'],
            scientific_name=planta['scientific'],
            disease_name=planta['disease'],
            confidence=planta['confidence'],
            recommendations=planta['recommendations']
        )
        
    except Exception as e:
        print(f"❌ Error mostrando resultado: {e}")
        return f"Error: {e}", 500

if __name__ == '__main__':
    print("\n🌱 PlantCare IA - Servidor Mínimo")
    print("=" * 40)
    print("🌐 http://127.0.0.1:5000")
    print("📱 http://127.0.0.1:5000/diagnosis/scanner")
    print("=" * 40)
    
    app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)
