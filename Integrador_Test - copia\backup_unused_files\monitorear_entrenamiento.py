"""
Script para monitorear el progreso del entrenamiento
"""

import os
import json
import time

def contar_imagenes_en_directorio(directorio):
    """Cuenta imágenes en un directorio"""
    if not os.path.exists(directorio):
        return 0
    
    total = 0
    for root, dirs, files in os.walk(directorio):
        for file in files:
            if file.lower().endswith(('.jpg', '.jpeg', '.png')):
                total += 1
    return total

def verificar_modelo():
    """Verifica si el modelo existe"""
    model_path = "models/plant_disease_model.pth"
    class_names_path = "models/class_names.json"
    
    model_exists = os.path.exists(model_path)
    classes_exist = os.path.exists(class_names_path)
    
    if model_exists:
        model_size = os.path.getsize(model_path) / (1024*1024)  # MB
        print(f"✅ Modelo encontrado: {model_size:.2f} MB")
    else:
        print("❌ Modelo no encontrado")
    
    if classes_exist:
        with open(class_names_path, 'r', encoding='utf-8') as f:
            classes = json.load(f)
        print(f"✅ Clases encontradas: {len(classes)}")
        print(f"   Clases: {classes[:5]}..." if len(classes) > 5 else f"   Clases: {classes}")
    else:
        print("❌ Archivo de clases no encontrado")

def main():
    print("🔍 MONITOREANDO ENTRENAMIENTO DE IA")
    print("=" * 50)
    
    # Verificar directorios de datos
    train_dir = "data/processed_plant_images_completo_final/train"
    val_dir = "data/processed_plant_images_completo_final/val"
    
    train_images = contar_imagenes_en_directorio(train_dir)
    val_images = contar_imagenes_en_directorio(val_dir)
    
    print(f"📊 DATOS PROCESADOS:")
    print(f"   Entrenamiento: {train_images} imágenes")
    print(f"   Validación: {val_images} imágenes")
    print(f"   Total: {train_images + val_images} imágenes")
    
    if train_images > 0:
        # Contar clases
        if os.path.exists(train_dir):
            clases = [d for d in os.listdir(train_dir) if os.path.isdir(os.path.join(train_dir, d))]
            print(f"   Clases: {len(clases)}")
            
            # Mostrar distribución por clase
            print(f"\n📈 DISTRIBUCIÓN POR CLASE:")
            for clase in sorted(clases)[:10]:  # Mostrar solo las primeras 10
                clase_path = os.path.join(train_dir, clase)
                num_imgs = len([f for f in os.listdir(clase_path) 
                              if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
                print(f"   {clase}: {num_imgs} imágenes")
            
            if len(clases) > 10:
                print(f"   ... y {len(clases) - 10} clases más")
    
    print(f"\n🤖 ESTADO DEL MODELO:")
    verificar_modelo()
    
    # Verificar si hay gráficas de entrenamiento
    plot_path = "models/training_history.png"
    if os.path.exists(plot_path):
        print(f"✅ Gráfica de entrenamiento disponible: {plot_path}")
    else:
        print("❌ Gráfica de entrenamiento no disponible aún")
    
    print(f"\n⏰ Monitoreo completado - {time.strftime('%H:%M:%S')}")

if __name__ == '__main__':
    main()
