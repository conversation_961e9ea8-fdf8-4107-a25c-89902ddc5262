"""
Script para probar el modelo entrenado
"""

import torch
import json
from torchvision import transforms
from PIL import Image
import os

def cargar_modelo():
    """Carga el modelo entrenado"""
    try:
        model_path = "models/plant_disease_model.pth"
        class_names_path = "models/class_names.json"
        
        # Cargar modelo (con weights_only=False para compatibilidad)
        model = torch.load(model_path, map_location='cpu', weights_only=False)
        model.eval()
        
        # Cargar nombres de clases
        with open(class_names_path, 'r', encoding='utf-8') as f:
            class_names = json.load(f)
        
        print(f"✅ Modelo cargado exitosamente")
        print(f"✅ {len(class_names)} clases disponibles")
        
        return model, class_names
        
    except Exception as e:
        print(f"❌ Error cargando modelo: {e}")
        return None, None

def predecir_imagen(model, class_names, imagen_path):
    """Predice la clase de una imagen"""
    try:
        # Transformaciones (mismas que en entrenamiento)
        transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
        ])
        
        # Cargar y procesar imagen
        imagen = Image.open(imagen_path).convert('RGB')
        imagen_tensor = transform(imagen).unsqueeze(0)
        
        # Predicción
        with torch.no_grad():
            outputs = model(imagen_tensor)
            probabilities = torch.nn.functional.softmax(outputs[0], dim=0)
            confidence, predicted = torch.max(probabilities, 0)
        
        clase_predicha = class_names[predicted.item()]
        confianza = confidence.item() * 100
        
        return clase_predicha, confianza, probabilities
        
    except Exception as e:
        print(f"❌ Error en predicción: {e}")
        return None, 0, None

def main():
    print("🧪 PROBANDO MODELO DE IA PARA PLANTAS")
    print("=" * 50)
    
    # Cargar modelo
    model, class_names = cargar_modelo()
    
    if model is None:
        print("❌ No se pudo cargar el modelo")
        return
    
    print(f"\n🌱 CLASES DISPONIBLES:")
    for i, clase in enumerate(class_names):
        print(f"   {i+1:2d}. {clase}")
    
    # Buscar imágenes de prueba
    test_dir = "data/processed_plant_images_completo_final/val"
    
    if os.path.exists(test_dir):
        print(f"\n🔍 PROBANDO CON IMÁGENES DE VALIDACIÓN:")
        
        # Probar con algunas imágenes de cada clase
        clases_probadas = 0
        for clase in class_names[:5]:  # Probar solo las primeras 5 clases
            clase_dir = os.path.join(test_dir, clase)
            if os.path.exists(clase_dir):
                imagenes = [f for f in os.listdir(clase_dir) 
                           if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                
                if imagenes:
                    # Probar con la primera imagen
                    imagen_path = os.path.join(clase_dir, imagenes[0])
                    
                    print(f"\n📸 Probando: {clase}")
                    print(f"   Imagen: {imagenes[0]}")
                    
                    clase_predicha, confianza, probabilities = predecir_imagen(
                        model, class_names, imagen_path
                    )
                    
                    if clase_predicha:
                        print(f"   🎯 Predicción: {clase_predicha}")
                        print(f"   📊 Confianza: {confianza:.2f}%")
                        
                        # Verificar si la predicción es correcta
                        if clase_predicha == clase:
                            print(f"   ✅ ¡CORRECTO!")
                        else:
                            print(f"   ❌ Incorrecto (esperado: {clase})")
                        
                        # Mostrar top 3 predicciones
                        top3_prob, top3_idx = torch.topk(probabilities, 3)
                        print(f"   📈 Top 3 predicciones:")
                        for i in range(3):
                            idx = top3_idx[i].item()
                            prob = top3_prob[i].item() * 100
                            print(f"      {i+1}. {class_names[idx]}: {prob:.2f}%")
                    
                    clases_probadas += 1
        
        print(f"\n✅ Prueba completada - {clases_probadas} clases probadas")
    
    else:
        print(f"❌ No se encontró directorio de validación: {test_dir}")
    
    print(f"\n🎉 ¡TU MODELO ESTÁ LISTO PARA USAR EN LA APLICACIÓN WEB!")

if __name__ == '__main__':
    main()
