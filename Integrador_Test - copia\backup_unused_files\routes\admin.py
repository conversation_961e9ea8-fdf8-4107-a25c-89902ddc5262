"""
Rutas de administración para gestión de plantas
"""

from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify
try:
    from flask_login import login_required, current_user
except ImportError:
    # Fallback si Flask-Login no está disponible
    def login_required(f):
        return f

    class MockUser:
        is_authenticated = False
        id = None

    current_user = MockUser()

try:
    from utils.admin_utils import (
        admin_required, is_admin, load_plants, add_plant,
        update_plant, delete_plant, load_users
    )
except ImportError as e:
    print(f"Error importando admin_utils: {e}")
    # Crear funciones mock para evitar errores
    def admin_required(f):
        return f

    def is_admin():
        return True

    def load_plants():
        return []

    def load_users():
        return []

    def add_plant(data):
        return False, "Admin utils no disponible"

    def update_plant(id, data):
        return False, "Admin utils no disponible"

    def delete_plant(id):
        return False, "Admin utils no disponible"

import os
from werkzeug.utils import secure_filename

admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

@admin_bp.route('/')
@login_required
@admin_required
def dashboard():
    """Panel de administración principal"""
    plants = load_plants()
    users = load_users()
    
    # Estadísticas
    total_plants = len([p for p in plants if p.get('Activo', True)])
    total_users = len([u for u in users if u.get('Activo', True)])
    native_plants = len([p for p in plants if p.get('EsNativa', False) and p.get('Activo', True)])
    
    stats = {
        'total_plants': total_plants,
        'total_users': total_users,
        'native_plants': native_plants,
        'recent_plants': [p for p in plants if p.get('Activo', True)][-5:]  # Últimas 5 plantas
    }
    
    return render_template('admin/dashboard.html', stats=stats)

@admin_bp.route('/plants')
@login_required
@admin_required
def plants_list():
    """Lista de todas las plantas"""
    plants = load_plants()
    active_plants = [p for p in plants if p.get('Activo', True)]
    return render_template('admin/plants_list.html', plants=active_plants)

@admin_bp.route('/plants/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_plant_form():
    """Formulario para agregar nueva planta"""
    if request.method == 'POST':
        try:
            # Recopilar datos del formulario
            plant_data = {
                'Nombre': request.form.get('nombre', '').strip(),
                'NombreCientifico': request.form.get('nombre_cientifico', '').strip(),
                'NombreComun': request.form.get('nombre_comun', '').strip(),
                'Descripcion': request.form.get('descripcion', '').strip(),
                'TipoPlantaID': int(request.form.get('tipo_planta_id', 1)),
                'FamiliaBotanicaID': int(request.form.get('familia_botanica_id', 1)),
                'Imagen': request.form.get('imagen', '').strip(),
                'RequerimientosSol': request.form.get('requerimientos_sol', '').strip(),
                'RequerimientosAgua': request.form.get('requerimientos_agua', '').strip(),
                'RequerimientosSuelo': request.form.get('requerimientos_suelo', '').strip(),
                'TemperaturaMin': float(request.form.get('temperatura_min', 0)),
                'TemperaturaMax': float(request.form.get('temperatura_max', 40)),
                'HumedadOptima': request.form.get('humedad_optima', '').strip(),
                'TiempoGerminacion': request.form.get('tiempo_germinacion', '').strip(),
                'TiempoCrecimiento': request.form.get('tiempo_crecimiento', '').strip(),
                'AlturaMadura': request.form.get('altura_madura', '').strip(),
                'Toxicidad': request.form.get('toxicidad', 'Ninguna').strip(),
                'DificultadCuidado': request.form.get('dificultad_cuidado', 'Fácil').strip(),
                'EsNativa': request.form.get('es_nativa') == 'on',
                'RegionNativa': request.form.get('region_nativa', '').strip(),
                'UsosMedicinales': request.form.get('usos_medicinales', '').strip(),
                'UsosTradicionales': request.form.get('usos_tradicionales', '').strip(),
                'ConsejosCuidado': request.form.get('consejos_cuidado', '').strip()
            }
            
            # Validaciones básicas
            if not plant_data['Nombre']:
                flash('El nombre de la planta es obligatorio.', 'danger')
                return render_template('admin/add_plant.html', form_data=plant_data)
            
            if not plant_data['NombreCientifico']:
                flash('El nombre científico es obligatorio.', 'danger')
                return render_template('admin/add_plant.html', form_data=plant_data)
            
            # Agregar planta
            success, result = add_plant(plant_data)
            
            if success:
                flash(f'Planta "{plant_data["Nombre"]}" agregada exitosamente con ID {result}.', 'success')
                return redirect(url_for('admin.plants_list'))
            else:
                flash(f'Error al agregar la planta: {result}', 'danger')
                return render_template('admin/add_plant.html', form_data=plant_data)
                
        except Exception as e:
            flash(f'Error inesperado: {str(e)}', 'danger')
            return render_template('admin/add_plant.html')
    
    return render_template('admin/add_plant.html')

@admin_bp.route('/plants/edit/<int:plant_id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_plant_form(plant_id):
    """Formulario para editar planta existente"""
    plants = load_plants()
    plant = None
    
    # Buscar la planta
    for p in plants:
        if p.get('PlantaID') == plant_id:
            plant = p
            break
    
    if not plant:
        flash('Planta no encontrada.', 'danger')
        return redirect(url_for('admin.plants_list'))
    
    if request.method == 'POST':
        try:
            # Recopilar datos del formulario (mismo código que add_plant_form)
            plant_data = {
                'Nombre': request.form.get('nombre', '').strip(),
                'NombreCientifico': request.form.get('nombre_cientifico', '').strip(),
                'NombreComun': request.form.get('nombre_comun', '').strip(),
                'Descripcion': request.form.get('descripcion', '').strip(),
                'TipoPlantaID': int(request.form.get('tipo_planta_id', 1)),
                'FamiliaBotanicaID': int(request.form.get('familia_botanica_id', 1)),
                'Imagen': request.form.get('imagen', '').strip(),
                'RequerimientosSol': request.form.get('requerimientos_sol', '').strip(),
                'RequerimientosAgua': request.form.get('requerimientos_agua', '').strip(),
                'RequerimientosSuelo': request.form.get('requerimientos_suelo', '').strip(),
                'TemperaturaMin': float(request.form.get('temperatura_min', 0)),
                'TemperaturaMax': float(request.form.get('temperatura_max', 40)),
                'HumedadOptima': request.form.get('humedad_optima', '').strip(),
                'TiempoGerminacion': request.form.get('tiempo_germinacion', '').strip(),
                'TiempoCrecimiento': request.form.get('tiempo_crecimiento', '').strip(),
                'AlturaMadura': request.form.get('altura_madura', '').strip(),
                'Toxicidad': request.form.get('toxicidad', 'Ninguna').strip(),
                'DificultadCuidado': request.form.get('dificultad_cuidado', 'Fácil').strip(),
                'EsNativa': request.form.get('es_nativa') == 'on',
                'RegionNativa': request.form.get('region_nativa', '').strip(),
                'UsosMedicinales': request.form.get('usos_medicinales', '').strip(),
                'UsosTradicionales': request.form.get('usos_tradicionales', '').strip(),
                'ConsejosCuidado': request.form.get('consejos_cuidado', '').strip(),
                'Activo': True
            }
            
            # Validaciones básicas
            if not plant_data['Nombre']:
                flash('El nombre de la planta es obligatorio.', 'danger')
                return render_template('admin/edit_plant.html', plant=plant, form_data=plant_data)
            
            # Actualizar planta
            success, result = update_plant(plant_id, plant_data)
            
            if success:
                flash(f'Planta "{plant_data["Nombre"]}" actualizada exitosamente.', 'success')
                return redirect(url_for('admin.plants_list'))
            else:
                flash(f'Error al actualizar la planta: {result}', 'danger')
                return render_template('admin/edit_plant.html', plant=plant, form_data=plant_data)
                
        except Exception as e:
            flash(f'Error inesperado: {str(e)}', 'danger')
            return render_template('admin/edit_plant.html', plant=plant)
    
    return render_template('admin/edit_plant.html', plant=plant)

@admin_bp.route('/plants/delete/<int:plant_id>', methods=['POST'])
@login_required
@admin_required
def delete_plant_action(plant_id):
    """Eliminar una planta"""
    success, result = delete_plant(plant_id)
    
    if success:
        flash(result, 'success')
    else:
        flash(f'Error: {result}', 'danger')
    
    return redirect(url_for('admin.plants_list'))

@admin_bp.route('/users')
@login_required
@admin_required
def users_list():
    """Lista de usuarios"""
    users = load_users()
    active_users = [u for u in users if u.get('Activo', True)]
    return render_template('admin/users_list.html', users=active_users)

@admin_bp.route('/api/plants')
@login_required
@admin_required
def api_plants():
    """API para obtener plantas (para AJAX)"""
    plants = load_plants()
    active_plants = [p for p in plants if p.get('Activo', True)]
    return jsonify(active_plants)

# Función para verificar si el usuario actual es admin (para templates)
@admin_bp.app_template_global()
def current_user_is_admin():
    """Función global para templates para verificar si el usuario actual es admin"""
    return is_admin() if current_user.is_authenticated else False
