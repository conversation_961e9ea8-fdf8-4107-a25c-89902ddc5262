from flask import Blueprint, render_template, redirect, url_for

recomendaciones_bp = Blueprint('recomendaciones', __name__)

@recomendaciones_bp.route('/')
def index():
    return render_template('recomendaciones.html')

@recomendaciones_bp.route('/detalle/<int:recomendacion_id>')
def detalle(recomendacion_id):
    # Código para mostrar detalle de recomendación
    return render_template('detalle-recomendacion.html', recomendacion_id=recomendacion_id)
