from flask import Blueprint, render_template, redirect, url_for
from flask_login import login_required

settings_bp = Blueprint('settings', __name__)

@settings_bp.route('/')
@login_required
def index():
    return render_template('ajustes.html')

@settings_bp.route('/notificaciones')
@login_required
def notificaciones():
    # Código para configurar notificaciones
    return render_template('notificaciones.html')
