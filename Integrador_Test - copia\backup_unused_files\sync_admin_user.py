#!/usr/bin/env python3
"""
Script para sincronizar el usuario administrador entre los dos sistemas
"""

import json
import os
from werkzeug.security import generate_password_hash
from datetime import datetime

def load_current_users():
    """Cargar usuarios del sistema actual"""
    try:
        from utils.auth import load_users
        return load_users()
    except:
        # Si no existe, crear estructura vacía
        return {}

def save_current_users(users):
    """Guardar usuarios en el sistema actual"""
    try:
        from utils.auth import save_users
        return save_users(users)
    except:
        # Fallback: guardar en archivo JSON directo
        try:
            users_file = os.path.join('data', 'users.json')
            os.makedirs(os.path.dirname(users_file), exist_ok=True)
            with open(users_file, 'w', encoding='utf-8') as f:
                json.dump(users, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"Error guardando usuarios: {e}")
            return False

def create_admin_in_current_system():
    """Crear usuario administrador en el sistema de autenticación actual"""
    
    print("🔧 Creando usuario administrador en sistema actual...")
    
    # Cargar usuarios existentes
    users = load_current_users()
    print(f"   Usuarios existentes: {len(users)}")
    
    # Credenciales del admin
    admin_username = "admin"
    admin_password = "PlantCare2025!"
    admin_email = "<EMAIL>"
    
    # Generar hash de contraseña
    password_hash = generate_password_hash(admin_password)
    
    # Crear usuario admin en formato del sistema actual
    admin_user = {
        'username': admin_username,
        'display_username': admin_username,
        'email': admin_email,
        'password_hash': password_hash,
        'first_name': 'Administrador',
        'last_name': 'PlantCare',
        'created_at': datetime.now().isoformat(),
        'is_admin': True,
        'role': 'admin'
    }
    
    # Buscar si ya existe un admin
    admin_id = None
    for uid, user_data in users.items():
        if user_data.get('username') == admin_username:
            admin_id = uid
            break
    
    # Si no existe, crear nuevo ID
    if admin_id is None:
        # Encontrar el próximo ID disponible
        existing_ids = [int(uid) for uid in users.keys() if uid.isdigit()]
        admin_id = str(max(existing_ids) + 1) if existing_ids else "1"
    
    # Agregar/actualizar usuario admin
    users[admin_id] = admin_user
    
    # Guardar usuarios
    if save_current_users(users):
        print(f"   ✅ Usuario admin creado/actualizado con ID: {admin_id}")
        return True, admin_id
    else:
        print(f"   ❌ Error guardando usuario admin")
        return False, None

def update_json_users():
    """Actualizar archivo JSON de usuarios para compatibilidad con admin"""
    
    print("🔧 Actualizando archivo JSON de usuarios...")
    
    try:
        # Cargar usuarios del sistema actual
        current_users = load_current_users()
        
        # Convertir a formato JSON para el sistema de admin
        json_users = []
        
        for uid, user_data in current_users.items():
            json_user = {
                "UsuarioID": int(uid),
                "Nombre": user_data.get('first_name', 'Usuario'),
                "Apellido": user_data.get('last_name', 'Ejemplo'),
                "Email": user_data.get('email', ''),
                "Username": user_data.get('username', ''),
                "PasswordHash": user_data.get('password_hash', ''),
                "FechaRegistro": user_data.get('created_at', datetime.now().isoformat()),
                "UltimoAcceso": None,
                "Activo": True,
                "RolID": 1 if user_data.get('is_admin', False) or user_data.get('role') == 'admin' else 2,
                "IsAdmin": user_data.get('is_admin', False) or user_data.get('role') == 'admin'
            }
            json_users.append(json_user)
        
        # Guardar en archivo JSON
        json_file = os.path.join('data', 'json', 'users.json')
        os.makedirs(os.path.dirname(json_file), exist_ok=True)
        
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(json_users, f, indent=2, ensure_ascii=False)
        
        print(f"   ✅ Archivo JSON actualizado con {len(json_users)} usuarios")
        return True
        
    except Exception as e:
        print(f"   ❌ Error actualizando JSON: {e}")
        return False

def test_login():
    """Probar el login del administrador"""
    
    print("🧪 Probando login del administrador...")
    
    try:
        from utils.auth import load_users, verify_password
        
        users = load_users()
        admin_username = "admin"
        admin_password = "PlantCare2025!"
        
        # Buscar usuario admin
        admin_found = False
        for uid, user_data in users.items():
            if user_data.get('username') == admin_username:
                admin_found = True
                print(f"   ✅ Usuario admin encontrado con ID: {uid}")
                
                # Probar contraseña
                is_valid = verify_password(admin_password, user_data['password_hash'])
                print(f"   ✅ Contraseña válida: {is_valid}")
                
                if is_valid:
                    print(f"   🎉 ¡Login funcionará correctamente!")
                    return True
                else:
                    print(f"   ❌ Contraseña incorrecta")
                    return False
        
        if not admin_found:
            print(f"   ❌ Usuario admin no encontrado")
            return False
            
    except Exception as e:
        print(f"   ❌ Error probando login: {e}")
        return False

def main():
    """Función principal"""
    
    print("🔄 SINCRONIZACIÓN DE USUARIO ADMINISTRADOR")
    print("=" * 50)
    
    # Paso 1: Crear admin en sistema actual
    success, admin_id = create_admin_in_current_system()
    if not success:
        print("❌ No se pudo crear usuario admin")
        return
    
    # Paso 2: Actualizar archivo JSON
    if not update_json_users():
        print("⚠️  Error actualizando JSON, pero el login debería funcionar")
    
    # Paso 3: Probar login
    if test_login():
        print("\n✅ ¡SINCRONIZACIÓN EXITOSA!")
        print("\n📋 CREDENCIALES FINALES:")
        print("   Usuario: admin")
        print("   Contraseña: PlantCare2025!")
        print("   Email: <EMAIL>")
        print("\n🚀 Ahora puedes hacer login y acceder al panel de administración")
    else:
        print("\n❌ Hubo problemas con la sincronización")
        print("   Revisa los errores arriba")

if __name__ == "__main__":
    main()
