{% extends 'base.html' %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/scanner.css') }}">
{% endblock %}

{% block title %}Resultado del Diagnóstico{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h3 class="mb-0">Resultado del Diagnóstico</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="image-container mb-3">
                                <img src="data:image/jpeg;base64,{{ image_base64 }}" class="img-fluid rounded" alt="Imagen de la planta">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="diagnosis-result">
                                <h4 class="mb-3">Planta identificada:</h4>
                                <div class="alert alert-info">
                                    <strong>{{ disease_name.replace('_', ' ').title() }}</strong>
                                </div>

                                <h4 class="mb-3">Nivel de confianza:</h4>
                                <div class="progress mb-3">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ confidence }}%;"
                                         aria-valuenow="{{ confidence }}" aria-valuemin="0" aria-valuemax="100">
                                        {{ "%.2f"|format(confidence) }}%
                                    </div>
                                </div>

                                <h4 class="mb-3">Recomendaciones:</h4>
                                <div class="alert alert-light border">
                                    <p>{{ recommendations }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <a href="/diagnosis/scanner" class="btn btn-primary">
                            <i class="fas fa-camera"></i> Realizar otro diagnóstico
                        </a>

                        {% if 'enfermedades' in disease_name %}
                        <a href="/diagnosis/scanner" class="btn btn-warning">
                            <i class="fas fa-book-medical"></i> Ver tratamientos recomendados
                        </a>
                        {% endif %}

                        <a href="/" class="btn btn-secondary">
                            <i class="fas fa-home"></i> Volver al inicio
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .image-container {
        max-height: 400px;
        overflow: hidden;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .diagnosis-result h4 {
        color: #2c3e50;
        border-bottom: 1px solid #eee;
        padding-bottom: 8px;
    }

    .progress {
        height: 25px;
    }

    .progress-bar {
        font-weight: bold;
    }
</style>
{% endblock %}
