#!/usr/bin/env python3
"""
Script para probar que el calendario funciona correctamente
"""

import requests

def test_calendario():
    """Probar el calendario y sus recursos"""
    
    base_url = "http://127.0.0.1:5000"
    
    print("🧪 Probando calendario...")
    
    # 1. Probar página principal del calendario
    print("\n1. 📄 Probando página del calendario...")
    try:
        response = requests.get(f"{base_url}/calendario", timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print(f"   ✅ Página cargada correctamente")
            # Verificar que contiene los enlaces a CSS y JS
            content = response.text
            if 'calendario.css' in content:
                print(f"   ✅ Enlace a CSS encontrado")
            else:
                print(f"   ❌ Enlace a CSS no encontrado")
            
            if 'calendario.js' in content:
                print(f"   ✅ Enlace a JS encontrado")
            else:
                print(f"   ❌ Enlace a JS no encontrado")
        else:
            print(f"   ❌ Error: {response.status_code}")
    except Exception as e:
        print(f"   💥 Error: {e}")
    
    # 2. Probar archivo CSS
    print("\n2. 🎨 Probando archivo CSS...")
    try:
        response = requests.get(f"{base_url}/static/css/calendario.css", timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print(f"   ✅ CSS cargado correctamente")
            print(f"   📏 Tamaño: {len(response.text)} caracteres")
        else:
            print(f"   ❌ Error: {response.status_code}")
    except Exception as e:
        print(f"   💥 Error: {e}")
    
    # 3. Probar archivo JavaScript
    print("\n3. ⚡ Probando archivo JavaScript...")
    try:
        response = requests.get(f"{base_url}/static/js/calendario.js", timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print(f"   ✅ JavaScript cargado correctamente")
            print(f"   📏 Tamaño: {len(response.text)} caracteres")
        else:
            print(f"   ❌ Error: {response.status_code}")
    except Exception as e:
        print(f"   💥 Error: {e}")
    
    print("\n🎉 Pruebas completadas!")

if __name__ == "__main__":
    test_calendario()
