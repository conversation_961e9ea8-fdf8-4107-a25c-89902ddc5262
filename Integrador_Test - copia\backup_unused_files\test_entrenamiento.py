"""
Script de prueba para verificar que el entrenamiento funciona.
"""

import os
import torch
from torchvision import datasets, transforms, models
from torch.utils.data import DataLoader
import torch.nn as nn

if __name__ == '__main__':
    print("=== INICIANDO PRUEBA DE ENTRENAMIENTO ===")
    
    # Verificar PyTorch
    print(f"PyTorch versión: {torch.__version__}")
    print(f"CUDA disponible: {torch.cuda.is_available()}")
    
    # Verificar directorios
    train_dir = "data/processed_plant_images_final/train"
    val_dir = "data/processed_plant_images_final/val"
    
    print(f"Directorio de entrenamiento existe: {os.path.exists(train_dir)}")
    print(f"Directorio de validación existe: {os.path.exists(val_dir)}")
    
    if os.path.exists(train_dir):
        clases = os.listdir(train_dir)
        print(f"Clases encontradas: {clases}")
        
        for clase in clases:
            clase_path = os.path.join(train_dir, clase)
            if os.path.isdir(clase_path):
                num_imagenes = len([f for f in os.listdir(clase_path) 
                                 if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
                print(f"  {clase}: {num_imagenes} imágenes")
    
    # Probar carga de datos
    try:
        print("\n=== PROBANDO CARGA DE DATOS ===")
        
        transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
        ])
        
        train_dataset = datasets.ImageFolder(train_dir, transform)
        train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True, num_workers=0)
        
        print(f"Dataset de entrenamiento cargado: {len(train_dataset)} imágenes")
        print(f"Clases: {train_dataset.classes}")
        
        # Probar una iteración
        for batch_idx, (data, target) in enumerate(train_loader):
            print(f"Batch {batch_idx}: {data.shape}, {target.shape}")
            if batch_idx >= 2:  # Solo probar 3 batches
                break
        
        print("✅ Carga de datos exitosa!")
        
    except Exception as e:
        print(f"❌ Error en carga de datos: {e}")
    
    # Probar modelo
    try:
        print("\n=== PROBANDO MODELO ===")
        
        model = models.resnet18(weights='IMAGENET1K_V1')
        num_classes = len(train_dataset.classes)
        
        # Modificar la última capa
        model.fc = nn.Linear(model.fc.in_features, num_classes)
        
        print(f"Modelo creado para {num_classes} clases")
        
        # Probar forward pass
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model = model.to(device)
        model.eval()
        
        with torch.no_grad():
            for data, target in train_loader:
                data = data.to(device)
                output = model(data)
                print(f"Output shape: {output.shape}")
                break
        
        print("✅ Modelo funcionando correctamente!")
        
    except Exception as e:
        print(f"❌ Error en modelo: {e}")
    
    print("\n=== PRUEBA COMPLETADA ===")
