#!/usr/bin/env python3
"""
Script para probar el login del administrador
"""

import requests
import json

def test_admin_login():
    """Probar el login del administrador"""
    
    base_url = "http://127.0.0.1:5000"
    
    print("🧪 Probando login del administrador...")
    
    # Crear sesión para mantener cookies
    session = requests.Session()
    
    # 1. Obtener página de login
    print("\n1. 📄 Obteniendo página de login...")
    try:
        response = session.get(f"{base_url}/login")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ Página de login accesible")
        else:
            print("   ❌ Error accediendo a login")
            return False
    except Exception as e:
        print(f"   💥 Error: {e}")
        return False
    
    # 2. Intentar login
    print("\n2. 🔑 Intentando login...")
    login_data = {
        'username': 'admin',
        'password': 'PlantCare2025!'
    }
    
    try:
        response = session.post(f"{base_url}/login", data=login_data)
        print(f"   Status: {response.status_code}")
        
        # Verificar si fue exitoso (redirección o mensaje de éxito)
        if response.status_code == 302:
            print("   ✅ Login exitoso (redirección)")
        elif response.status_code == 200:
            if "Bienvenido" in response.text or "welcome" in response.text.lower():
                print("   ✅ Login exitoso")
            else:
                print("   ❌ Login falló - credenciales incorrectas")
                return False
        else:
            print(f"   ❌ Error en login: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   💥 Error: {e}")
        return False
    
    # 3. Probar acceso al panel de admin
    print("\n3. 🛠️  Probando acceso al panel de admin...")
    try:
        response = session.get(f"{base_url}/admin")
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Panel de admin accesible")
            
            # Verificar contenido del panel
            if "Panel de Administración" in response.text or "Dashboard" in response.text:
                print("   ✅ Contenido del panel correcto")
                return True
            else:
                print("   ⚠️  Panel accesible pero contenido inesperado")
                return True
        else:
            print(f"   ❌ Error accediendo al panel: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   💥 Error: {e}")
        return False

def verify_user_file():
    """Verificar que el archivo de usuarios esté correcto"""
    
    print("\n🔍 Verificando archivo de usuarios...")
    
    try:
        with open('data/users.json', 'r', encoding='utf-8') as f:
            users = json.load(f)
        
        print(f"   📊 Usuarios encontrados: {len(users)}")
        
        # Verificar usuario admin
        admin = users.get("1")
        if admin:
            print(f"   ✅ Usuario admin encontrado:")
            print(f"      Username: {admin.get('username')}")
            print(f"      Email: {admin.get('email')}")
            print(f"      Es admin: {admin.get('is_admin')}")
            print(f"      Rol: {admin.get('role')}")
            return True
        else:
            print("   ❌ Usuario admin no encontrado")
            return False
            
    except Exception as e:
        print(f"   💥 Error: {e}")
        return False

def main():
    """Función principal"""
    
    print("🧪 PRUEBA COMPLETA DEL SISTEMA DE ADMINISTRACIÓN")
    print("=" * 55)
    
    # Verificar archivo de usuarios
    if not verify_user_file():
        print("\n❌ Problema con el archivo de usuarios")
        return
    
    # Probar login
    if test_admin_login():
        print("\n🎉 ¡TODAS LAS PRUEBAS PASARON!")
        print("\n✅ El sistema de administración está funcionando correctamente")
        print("\n📋 Puedes usar estas credenciales:")
        print("   Usuario: admin")
        print("   Contraseña: PlantCare2025!")
        print("\n🔗 URLs importantes:")
        print("   Login: http://127.0.0.1:5000/login")
        print("   Panel Admin: http://127.0.0.1:5000/admin")
        print("   Agregar Planta: http://127.0.0.1:5000/admin/plants/add")
    else:
        print("\n❌ Algunas pruebas fallaron")
        print("   Revisa los errores arriba")

if __name__ == "__main__":
    main()
