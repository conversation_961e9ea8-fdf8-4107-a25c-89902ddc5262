"""
Script simple para probar el login sin complicaciones
"""

import json
import hashlib
from datetime import datetime

def hash_password(password):
    """Hash simple de contraseña"""
    return hashlib.sha256(password.encode()).hexdigest()

def load_users():
    """Cargar usuarios del archivo JSON"""
    try:
        with open('data/users.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except:
        return {}

def save_users(users):
    """Guardar usuarios al archivo JSON"""
    try:
        with open('data/users.json', 'w', encoding='utf-8') as f:
            json.dump(users, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"Error guardando usuarios: {e}")
        return False

def crear_usuario_prueba():
    """Crear un usuario de prueba simple"""
    users = load_users()

    # Verificar si ya existe testuser
    for user_data in users.values():
        if user_data.get('username') == 'testuser':
            print("✅ Usuario testuser ya existe")
            return True

    # Crear usuario de prueba
    user_id = str(len(users) + 1)
    test_user = {
        'username': 'testuser',
        'email': '<EMAIL>',
        'password_hash': hash_password('123456'),
        'first_name': 'Test',
        'last_name': 'User',
        'created_at': datetime.now().isoformat()
    }

    users[user_id] = test_user

    if save_users(users):
        print("✅ Usuario de prueba creado:")
        print("   Username: testuser")
        print("   Password: 123456")
        return True
    else:
        print("❌ Error creando usuario de prueba")
        return False

def verificar_login(username, password):
    """Verificar login simple"""
    users = load_users()
    
    print(f"🔍 Buscando usuario: {username}")
    print(f"📊 Total usuarios en DB: {len(users)}")
    
    for uid, user_data in users.items():
        print(f"   Usuario {uid}: {user_data.get('username', 'N/A')}")
        
        if user_data.get('username') == username:
            print(f"✅ Usuario encontrado!")
            
            # Verificar contraseña
            stored_hash = user_data.get('password_hash', '')
            input_hash = hash_password(password)
            
            print(f"🔐 Hash almacenado: {stored_hash[:20]}...")
            print(f"🔐 Hash ingresado: {input_hash[:20]}...")
            
            if stored_hash == input_hash:
                print("✅ ¡Contraseña correcta!")
                return True
            else:
                print("❌ Contraseña incorrecta")
                return False
    
    print("❌ Usuario no encontrado")
    return False

def main():
    print("🔧 DIAGNÓSTICO SIMPLE DE LOGIN")
    print("=" * 40)
    
    # Verificar archivo de usuarios
    users = load_users()
    print(f"📊 Usuarios en base de datos: {len(users)}")
    
    # Siempre crear usuario de prueba para asegurar que funcione
    print("🔧 Creando/verificando usuario de prueba...")
    crear_usuario_prueba()
    users = load_users()
    
    # Mostrar usuarios disponibles
    print(f"\n👥 USUARIOS DISPONIBLES:")
    for uid, user_data in users.items():
        username = user_data.get('username', 'N/A')
        email = user_data.get('email', 'N/A')
        print(f"   {uid}: {username} ({email})")
    
    # Probar login con usuario existente
    print(f"\n🧪 PROBANDO LOGIN:")
    
    # Si existe testuser, probar con él
    if any(user.get('username') == 'testuser' for user in users.values()):
        print("Probando con testuser...")
        result = verificar_login('testuser', '123456')
        print(f"Resultado: {'✅ ÉXITO' if result else '❌ FALLO'}")
    
    # Probar con el primer usuario disponible
    if users:
        first_user = list(users.values())[0]
        username = first_user.get('username')
        if username and username != 'testuser':
            print(f"\nProbando con {username}...")
            # Nota: No sabemos la contraseña real, solo probamos la estructura
            print("(No podemos probar la contraseña real sin conocerla)")

if __name__ == '__main__':
    main()
