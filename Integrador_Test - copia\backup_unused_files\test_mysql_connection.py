"""
Script para probar la conexión a la base de datos MySQL.
"""

import pymysql
from config import Config

def test_connection():
    """Probar la conexión a la base de datos MySQL"""
    try:
        # Primero conectamos sin especificar la base de datos
        connection = pymysql.connect(
            host=Config.MYSQL_HOST,
            user=Config.MYSQL_USER,
            password=Config.MYSQL_PASSWORD,
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )

        # Verificar si la base de datos existe
        with connection.cursor() as cursor:
            cursor.execute(f"SHOW DATABASES LIKE '{Config.MYSQL_DB}'")
            result = cursor.fetchone()

            if not result:
                print(f"La base de datos '{Config.MYSQL_DB}' no existe. Creándola...")
                cursor.execute(f"CREATE DATABASE {Config.MYSQL_DB} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                connection.commit()
                print(f"Base de datos '{Config.MYSQL_DB}' creada correctamente.")
            else:
                print(f"La base de datos '{Config.MYSQL_DB}' ya existe.")

        # Cerrar la conexión inicial
        connection.close()

        # Conectar a la base de datos específica
        connection = pymysql.connect(
            host=Config.MYSQL_HOST,
            user=Config.MYSQL_USER,
            password=Config.MYSQL_PASSWORD,
            database=Config.MYSQL_DB,
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )

        # Verificar la conexión ejecutando una consulta simple
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION() as version")
            result = cursor.fetchone()
            print(f"Conexión exitosa a MySQL. Versión: {result['version']}")

            # Listar las tablas en la base de datos
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()

            if tables:
                print("\nTablas encontradas en la base de datos:")
                for table in tables:
                    table_name = list(table.values())[0]
                    print(f"- {table_name}")
            else:
                print("\nNo se encontraron tablas en la base de datos.")

        # Cerrar la conexión
        connection.close()
        print("\nConexión cerrada correctamente.")
        return True

    except Exception as e:
        print(f"Error al conectar a MySQL: {e}")
        return False

if __name__ == "__main__":
    print(f"Intentando conectar a MySQL en {Config.MYSQL_HOST}...")
    print(f"Base de datos: {Config.MYSQL_DB}")
    print(f"Usuario: {Config.MYSQL_USER}")

    test_connection()
