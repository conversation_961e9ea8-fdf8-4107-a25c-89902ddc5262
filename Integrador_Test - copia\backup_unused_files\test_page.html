<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prueba del Sistema de Administración</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .card {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .credentials {
            background: #e8f5e9;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }
        .step {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .link {
            display: inline-block;
            background: #4CAF50;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
        }
        .link:hover {
            background: #2E7D32;
        }
        .success {
            color: #2E7D32;
            font-weight: bold;
        }
        .warning {
            color: #FF9800;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="card">
        <h1>🛠️ Sistema de Administración PlantCare</h1>
        <p>El sistema está funcionando correctamente. Sigue estos pasos para probarlo:</p>
    </div>

    <div class="card">
        <h2>🔑 Credenciales de Administrador</h2>
        <div class="credentials">
            <p><strong>Usuario:</strong> admin</p>
            <p><strong>Contraseña:</strong> PlantCare2025!</p>
            <p><strong>Email:</strong> <EMAIL></p>
        </div>
    </div>

    <div class="card">
        <h2>📋 Pasos para Probar</h2>
        
        <div class="step">
            <h3>Paso 1: Iniciar Sesión</h3>
            <p>Haz clic en el botón de abajo para ir a la página de login:</p>
            <a href="http://127.0.0.1:5000/login" class="link" target="_blank">🔑 Ir a Login</a>
            <p><small>Usa las credenciales de arriba para iniciar sesión</small></p>
        </div>

        <div class="step">
            <h3>Paso 2: Acceder al Panel de Administración</h3>
            <p>Después de hacer login, haz clic aquí para ir al panel de administración:</p>
            <a href="http://127.0.0.1:5000/admin" class="link" target="_blank">🛠️ Panel de Administración</a>
        </div>

        <div class="step">
            <h3>Paso 3: Probar Funcionalidades</h3>
            <p>Una vez en el panel, puedes probar estas funciones:</p>
            <a href="http://127.0.0.1:5000/admin/plants" class="link" target="_blank">🌱 Ver Plantas</a>
            <a href="http://127.0.0.1:5000/admin/plants/add" class="link" target="_blank">➕ Agregar Planta</a>
            <a href="http://127.0.0.1:5000/admin/users" class="link" target="_blank">👥 Ver Usuarios</a>
        </div>
    </div>

    <div class="card">
        <h2>🌱 Ejemplo de Planta para Agregar</h2>
        <p>Puedes usar estos datos para probar el formulario de agregar plantas:</p>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace;">
            <strong>Nombre:</strong> Biznaga Barril<br>
            <strong>Nombre Científico:</strong> Ferocactus wislizeni<br>
            <strong>Nombre Común:</strong> Cactus Barril<br>
            <strong>Descripción:</strong> Cactus grande y cilíndrico del desierto de Chihuahua<br>
            <strong>Tipo:</strong> Cactus<br>
            <strong>Sol:</strong> Sol directo<br>
            <strong>Agua:</strong> Muy poco frecuente<br>
            <strong>✓ Es nativa de Chihuahua</strong><br>
            <strong>Región:</strong> Desierto de Chihuahua
        </div>
    </div>

    <div class="card">
        <h2>✅ Estado del Sistema</h2>
        <p class="success">✅ Usuario administrador creado correctamente</p>
        <p class="success">✅ Panel de administración funcionando</p>
        <p class="success">✅ Formularios de plantas operativos</p>
        <p class="success">✅ Base de datos JSON configurada</p>
        <p class="warning">⚠️ Recuerda: Solo los usuarios administradores pueden acceder al panel</p>
    </div>

    <div class="card">
        <h2>🔧 Solución de Problemas</h2>
        <p><strong>Si no puedes hacer login:</strong></p>
        <ul>
            <li>Verifica que estés usando exactamente: <code>admin</code> y <code>PlantCare2025!</code></li>
            <li>Asegúrate de que el servidor esté ejecutándose</li>
            <li>Prueba refrescar la página de login</li>
        </ul>
        
        <p><strong>Si el panel no carga:</strong></p>
        <ul>
            <li>Asegúrate de haber hecho login primero</li>
            <li>Ve directamente a: <a href="http://127.0.0.1:5000/admin">http://127.0.0.1:5000/admin</a></li>
        </ul>
    </div>
</body>
</html>
