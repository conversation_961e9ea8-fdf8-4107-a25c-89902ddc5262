#!/usr/bin/env python3
"""
Script para probar el endpoint de recomendaciones
"""

import requests
import json

def test_recommendations_endpoint():
    """Probar el endpoint de recomendaciones"""
    
    base_url = "http://127.0.0.1:5000"
    endpoint = f"{base_url}/api/plants/recommendations"
    
    print("🧪 Probando endpoint de recomendaciones...")
    
    # Probar GET
    print("\n1. Probando GET request...")
    try:
        response = requests.get(f"{endpoint}?page=1&sort=match", timeout=10)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Success: {data.get('success')}")
            print(f"Plants count: {len(data.get('plants', []))}")
            if data.get('plants'):
                print(f"First plant: {data['plants'][0]['name']}")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Error en GET: {e}")
    
    # Probar POST
    print("\n2. Probando POST request...")
    try:
        filters = {
            "experienceLevel": "beginner",
            "sunlight": "high",
            "wateringFrequency": "low"
        }
        
        response = requests.post(
            f"{endpoint}?page=1&sort=match",
            json=filters,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Success: {data.get('success')}")
            print(f"Plants count: {len(data.get('plants', []))}")
            if data.get('plants'):
                print(f"First plant: {data['plants'][0]['name']}")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Error en POST: {e}")

if __name__ == "__main__":
    test_recommendations_endpoint()
