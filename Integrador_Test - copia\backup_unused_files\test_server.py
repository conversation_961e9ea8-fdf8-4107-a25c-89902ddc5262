#!/usr/bin/env python3
"""
Servidor de prueba simplificado para PlantCare
"""

from flask import Flask, render_template, redirect, url_for, request, jsonify
from flask_wtf.csrf import CSRFProtect, generate_csrf
import os
import json
import random
import time

app = Flask(__name__)
app.config['SECRET_KEY'] = 'test_secret_key'
app.config['UPLOAD_FOLDER'] = 'uploads'

# Inicializar CSRF
csrf = CSRFProtect(app)

# Crear directorios necesarios
os.makedirs('uploads/diagnosis', exist_ok=True)

@app.context_processor
def inject_csrf_token():
    return dict(csrf_token=generate_csrf())

@app.route('/')
def index():
    return redirect(url_for('scanner'))

@app.route('/diagnosis/scanner')
def scanner():
    return render_template('scanner.html')

@app.route('/diagnosis/upload', methods=['POST'])
def upload_image():
    """Simula el análisis de IA de una imagen"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No se encontró ningún archivo'}), 400
        
        file = request.files['file']
        
        if file.filename == '':
            return jsonify({'error': 'No se seleccionó ningún archivo'}), 400
        
        # Simular el procesamiento
        time.sleep(2)  # Simular tiempo de procesamiento
        
        # Generar un nombre de archivo aleatorio
        import uuid
        filename = f"plant_{uuid.uuid4().hex[:8]}.jpg"
        
        # Guardar el archivo
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], 'diagnosis', filename)
        file.save(file_path)
        
        return jsonify({
            'success': True,
            'filename': filename
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/diagnosis/result/<filename>')
def result(filename):
    """Muestra el resultado del análisis"""
    try:
        # Plantas disponibles para la simulación
        plantas = [
            {
                'name': 'Pata de Elefante',
                'scientific': 'Beaucarnea recurvata',
                'disease': 'pata_de_elefante_sana',
                'confidence': random.uniform(75, 95),
                'recommendations': 'Esta planta está saludable. Mantén un riego moderado cada 2-3 semanas y proporciona luz brillante indirecta.'
            },
            {
                'name': 'Bugambilia',
                'scientific': 'Bougainvillea spectabilis',
                'disease': 'bugambilia_sana',
                'confidence': random.uniform(70, 90),
                'recommendations': 'Planta en excelente estado. Riega cuando el suelo esté seco y proporciona pleno sol para una floración abundante.'
            },
            {
                'name': 'Agave',
                'scientific': 'Agave americana',
                'disease': 'agave_sana',
                'confidence': random.uniform(80, 95),
                'recommendations': 'Agave saludable. Requiere muy poco riego (una vez al mes) y pleno sol. Perfecto para jardines de bajo mantenimiento.'
            }
        ]
        
        # Seleccionar una planta aleatoria
        planta = random.choice(plantas)
        
        # Leer la imagen para mostrarla (si existe)
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], 'diagnosis', filename)
        image_base64 = None
        
        if os.path.exists(file_path):
            import base64
            with open(file_path, 'rb') as f:
                image_data = f.read()
            image_base64 = base64.b64encode(image_data).decode('utf-8')
        
        return render_template(
            'diagnosis_result.html',
            image_base64=image_base64,
            disease_name=planta['disease'],
            plant_name=planta['name'],
            scientific_name=planta['scientific'],
            confidence=planta['confidence'],
            recommendations=planta['recommendations']
        )
        
    except Exception as e:
        return f"Error: {str(e)}", 500

@app.errorhandler(404)
def not_found(error):
    return "Página no encontrada", 404

@app.errorhandler(500)
def internal_error(error):
    return "Error interno del servidor", 500

if __name__ == '__main__':
    print("\n🌱 PlantCare Test Server")
    print("🌐 Servidor ejecutándose en: http://127.0.0.1:5000")
    print("🔬 Modo de prueba con IA simulada")
    print("🛑 Presiona CTRL+C para detener\n")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
