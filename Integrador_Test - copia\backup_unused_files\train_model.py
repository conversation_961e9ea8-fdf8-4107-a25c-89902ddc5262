import os
import json
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torchvision import datasets, transforms, models
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path

class ModelTrainer:
    def __init__(self, data_dir, model_save_path, class_names_path, batch_size=32, num_epochs=20):
        """
        Inicializa el entrenador del modelo.
        
        Args:
            data_dir: Directorio que contiene las carpetas 'train' y 'val'
            model_save_path: Ruta donde se guardará el modelo entrenado
            class_names_path: Ruta donde se guardarán los nombres de las clases
            batch_size: Tamaño del lote para entrenamiento
            num_epochs: Número de épocas para entrenar
        """
        self.data_dir = data_dir
        self.model_save_path = model_save_path
        self.class_names_path = class_names_path
        self.batch_size = batch_size
        self.num_epochs = num_epochs
        
        # Asegurarse de que exista el directorio para guardar el modelo
        os.makedirs(os.path.dirname(model_save_path), exist_ok=True)
        
        # Configurar el dispositivo (GPU o CPU)
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"Usando dispositivo: {self.device}")
        
        # Transformaciones para las imágenes
        self.data_transforms = {
            'train': transforms.Compose([
                transforms.RandomResizedCrop(224),
                transforms.RandomHorizontalFlip(),
                transforms.RandomRotation(15),
                transforms.ColorJitter(brightness=0.1, contrast=0.1, saturation=0.1),
                transforms.ToTensor(),
                transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
            ]),
            'val': transforms.Compose([
                transforms.Resize(256),
                transforms.CenterCrop(224),
                transforms.ToTensor(),
                transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
            ]),
        }
    
    def load_data(self):
        """Carga los datos de entrenamiento y validación"""
        print("Cargando conjuntos de datos...")
        
        # Cargar los conjuntos de datos
        image_datasets = {
            'train': datasets.ImageFolder(os.path.join(self.data_dir, 'train'), 
                                         self.data_transforms['train']),
            'val': datasets.ImageFolder(os.path.join(self.data_dir, 'val'), 
                                       self.data_transforms['val'])
        }
        
        # Crear dataloaders
        self.dataloaders = {
            'train': DataLoader(image_datasets['train'], batch_size=self.batch_size, 
                               shuffle=True, num_workers=4),
            'val': DataLoader(image_datasets['val'], batch_size=self.batch_size, 
                             shuffle=False, num_workers=4)
        }
        
        # Obtener tamaños de los conjuntos de datos
        self.dataset_sizes = {x: len(image_datasets[x]) for x in ['train', 'val']}
        
        # Obtener nombres de clases y guardarlos
        self.class_names = image_datasets['train'].classes
        self.class_to_idx = image_datasets['train'].class_to_idx
        
        print(f"Clases encontradas: {len(self.class_names)}")
        print(f"Imágenes de entrenamiento: {self.dataset_sizes['train']}")
        print(f"Imágenes de validación: {self.dataset_sizes['val']}")
        
        # Guardar los nombres de las clases en un archivo JSON
        with open(self.class_names_path, 'w', encoding='utf-8') as f:
            json.dump(self.class_names, f, ensure_ascii=False, indent=2)
        
        print(f"Nombres de clases guardados en {self.class_names_path}")
    
    def create_model(self):
        """Crea un modelo preentrenado y lo configura para transfer learning"""
        print("Creando modelo...")
        
        # Cargar un modelo preentrenado (ResNet-50)
        model = models.resnet50(weights='IMAGENET1K_V2')
        
        # Congelar todos los parámetros para que no se actualicen durante el entrenamiento
        for param in model.parameters():
            param.requires_grad = False
        
        # Reemplazar la capa final para nuestra tarea de clasificación
        num_ftrs = model.fc.in_features
        model.fc = nn.Sequential(
            nn.Linear(num_ftrs, 512),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(512, len(self.class_names))
        )
        
        # Mover el modelo al dispositivo (GPU o CPU)
        self.model = model.to(self.device)
        
        # Definir función de pérdida y optimizador
        self.criterion = nn.CrossEntropyLoss()
        self.optimizer = optim.Adam(model.fc.parameters(), lr=0.001)
        self.scheduler = optim.lr_scheduler.StepLR(self.optimizer, step_size=7, gamma=0.1)
    
    def train_model(self):
        """Entrena el modelo y guarda el mejor modelo basado en la precisión de validación"""
        print(f"Iniciando entrenamiento por {self.num_epochs} épocas...")
        
        best_acc = 0.0
        history = {'train_loss': [], 'val_loss': [], 'train_acc': [], 'val_acc': []}
        
        for epoch in range(self.num_epochs):
            print(f'Época {epoch+1}/{self.num_epochs}')
            print('-' * 10)
            
            # Cada época tiene una fase de entrenamiento y validación
            for phase in ['train', 'val']:
                if phase == 'train':
                    self.model.train()  # Modo de entrenamiento
                else:
                    self.model.eval()   # Modo de evaluación
                
                running_loss = 0.0
                running_corrects = 0
                
                # Iterar sobre los datos
                for inputs, labels in self.dataloaders[phase]:
                    inputs = inputs.to(self.device)
                    labels = labels.to(self.device)
                    
                    # Poner a cero los gradientes del parámetro
                    self.optimizer.zero_grad()
                    
                    # Forward
                    with torch.set_grad_enabled(phase == 'train'):
                        outputs = self.model(inputs)
                        _, preds = torch.max(outputs, 1)
                        loss = self.criterion(outputs, labels)
                        
                        # Backward + optimize solo si es fase de entrenamiento
                        if phase == 'train':
                            loss.backward()
                            self.optimizer.step()
                    
                    # Estadísticas
                    running_loss += loss.item() * inputs.size(0)
                    running_corrects += torch.sum(preds == labels.data)
                
                if phase == 'train':
                    self.scheduler.step()
                
                epoch_loss = running_loss / self.dataset_sizes[phase]
                epoch_acc = running_corrects.double() / self.dataset_sizes[phase]
                
                # Guardar historial
                if phase == 'train':
                    history['train_loss'].append(epoch_loss)
                    history['train_acc'].append(epoch_acc.item())
                else:
                    history['val_loss'].append(epoch_loss)
                    history['val_acc'].append(epoch_acc.item())
                
                print(f'{phase} Loss: {epoch_loss:.4f} Acc: {epoch_acc:.4f}')
                
                # Guardar el mejor modelo
                if phase == 'val' and epoch_acc > best_acc:
                    best_acc = epoch_acc
                    torch.save(self.model, self.model_save_path)
                    print(f"Mejor modelo guardado con precisión: {best_acc:.4f}")
            
            print()
        
        print(f'Entrenamiento completado. Mejor precisión de validación: {best_acc:.4f}')
        return history
    
    def plot_training_history(self, history):
        """Grafica el historial de entrenamiento"""
        plt.figure(figsize=(12, 4))
        
        # Graficar pérdida
        plt.subplot(1, 2, 1)
        plt.plot(history['train_loss'], label='Entrenamiento')
        plt.plot(history['val_loss'], label='Validación')
        plt.title('Pérdida del modelo')
        plt.ylabel('Pérdida')
        plt.xlabel('Época')
        plt.legend()
        
        # Graficar precisión
        plt.subplot(1, 2, 2)
        plt.plot(history['train_acc'], label='Entrenamiento')
        plt.plot(history['val_acc'], label='Validación')
        plt.title('Precisión del modelo')
        plt.ylabel('Precisión')
        plt.xlabel('Época')
        plt.legend()
        
        plt.tight_layout()
        
        # Guardar la gráfica
        plot_path = os.path.join(os.path.dirname(self.model_save_path), 'training_history.png')
        plt.savefig(plot_path)
        print(f"Gráfica de entrenamiento guardada en {plot_path}")
        plt.close()

if __name__ == "__main__":
    # Configuración
    processed_data_dir = "data/processed_plant_images"
    model_save_path = "models/plant_disease_model.pth"
    class_names_path = "models/class_names.json"
    
    # Crear y entrenar el modelo
    trainer = ModelTrainer(
        data_dir=processed_data_dir,
        model_save_path=model_save_path,
        class_names_path=class_names_path,
        batch_size=32,
        num_epochs=20
    )
    
    # Cargar datos
    trainer.load_data()
    
    # Crear modelo
    trainer.create_model()
    
    # Entrenar modelo
    history = trainer.train_model()
    
    # Graficar historial de entrenamiento
    trainer.plot_training_history(history)
