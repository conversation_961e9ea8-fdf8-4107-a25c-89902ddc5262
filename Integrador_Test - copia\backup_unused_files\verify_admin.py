#!/usr/bin/env python3
"""
Script para verificar y corregir las credenciales del administrador
"""

import json
from werkzeug.security import check_password_hash, generate_password_hash
from datetime import datetime

def verify_admin_credentials():
    """Verificar las credenciales del administrador"""
    
    print("🔍 Verificando credenciales del administrador...")
    
    try:
        # Cargar usuarios
        with open('data/json/users.json', 'r', encoding='utf-8') as f:
            users = json.load(f)
        
        print(f"\n👥 Usuarios encontrados: {len(users)}")
        
        for i, user in enumerate(users):
            print(f"\n📋 Usuario {i+1}:")
            print(f"   ID: {user.get('UsuarioID')}")
            print(f"   Usuario: {user.get('Username')}")
            print(f"   Email: {user.get('Email')}")
            print(f"   Es Admin: {user.get('IsAdmin', False)}")
            print(f"   Activo: {user.get('Activo', True)}")
            
            # Verificar contraseña si es admin
            if user.get('IsAdmin', False) or user.get('Username') == 'admin':
                test_password = 'PlantCare2025!'
                stored_hash = user.get('PasswordHash', '')
                
                print(f"   🔐 Probando contraseña '{test_password}'...")
                
                try:
                    is_valid = check_password_hash(stored_hash, test_password)
                    print(f"   ✅ Contraseña válida: {is_valid}")
                    
                    if not is_valid:
                        print(f"   ⚠️  Hash almacenado: {stored_hash[:50]}...")
                        
                except Exception as e:
                    print(f"   ❌ Error verificando contraseña: {e}")
                    print(f"   🔧 Regenerando hash de contraseña...")
                    
                    # Regenerar hash
                    new_hash = generate_password_hash(test_password)
                    user['PasswordHash'] = new_hash
                    print(f"   ✅ Nuevo hash generado")
        
        return users
        
    except Exception as e:
        print(f"❌ Error cargando usuarios: {e}")
        return None

def fix_admin_user():
    """Corregir el usuario administrador"""
    
    print("\n🔧 Corrigiendo usuario administrador...")
    
    # Crear usuario admin correcto
    admin_password = 'PlantCare2025!'
    admin_hash = generate_password_hash(admin_password)
    
    users_data = [
        {
            "UsuarioID": 1,
            "Nombre": "Administrador",
            "Apellido": "PlantCare",
            "Email": "<EMAIL>",
            "Username": "admin",
            "PasswordHash": admin_hash,
            "FechaRegistro": datetime.now().isoformat(),
            "UltimoAcceso": None,
            "Activo": True,
            "RolID": 1,
            "IsAdmin": True
        },
        {
            "UsuarioID": 2,
            "Nombre": "Usuario",
            "Apellido": "Ejemplo",
            "Email": "<EMAIL>",
            "Username": "usuario",
            "PasswordHash": generate_password_hash("usuario123"),
            "FechaRegistro": datetime.now().isoformat(),
            "UltimoAcceso": None,
            "Activo": True,
            "RolID": 2,
            "IsAdmin": False
        }
    ]
    
    try:
        # Guardar usuarios corregidos
        with open('data/json/users.json', 'w', encoding='utf-8') as f:
            json.dump(users_data, f, indent=2, ensure_ascii=False)
        
        print("✅ Usuario administrador corregido exitosamente!")
        
        # Verificar que funciona
        print("\n🧪 Verificando credenciales corregidas...")
        admin = users_data[0]
        test_result = check_password_hash(admin['PasswordHash'], admin_password)
        print(f"   Contraseña válida: {test_result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error guardando usuarios: {e}")
        return False

def test_login_compatibility():
    """Probar compatibilidad con el sistema de login actual"""
    
    print("\n🔗 Probando compatibilidad con sistema de login...")
    
    try:
        # Simular el proceso de login
        from utils.auth import load_users, verify_password
        
        users = load_users()
        print(f"   Usuarios cargados por auth: {len(users)}")
        
        # Buscar admin
        admin_found = False
        for uid, user_data in users.items():
            if user_data.get('username') == 'admin':
                admin_found = True
                print(f"   ✅ Admin encontrado en sistema auth con ID: {uid}")
                
                # Probar verificación de contraseña
                is_valid = verify_password('PlantCare2025!', user_data['password_hash'])
                print(f"   ✅ Verificación de contraseña: {is_valid}")
                break
        
        if not admin_found:
            print("   ❌ Admin NO encontrado en sistema auth")
            print("   🔧 Necesita sincronización entre sistemas")
            
    except Exception as e:
        print(f"   ⚠️  Error probando compatibilidad: {e}")
        print("   💡 Esto es normal si los sistemas usan formatos diferentes")

def main():
    """Función principal"""
    
    print("🛠️  VERIFICACIÓN Y CORRECCIÓN DE CREDENCIALES ADMIN")
    print("=" * 60)
    
    # Verificar credenciales actuales
    users = verify_admin_credentials()
    
    if not users:
        print("\n❌ No se pudieron cargar los usuarios")
        return
    
    # Preguntar si corregir
    print("\n" + "=" * 60)
    print("🔧 ¿Quieres regenerar las credenciales del administrador?")
    print("   Esto creará un usuario admin con credenciales frescas")
    
    # Corregir automáticamente
    if fix_admin_user():
        print("\n✅ ¡Credenciales corregidas!")
        
        # Probar compatibilidad
        test_login_compatibility()
        
        print("\n📋 CREDENCIALES FINALES:")
        print("   Usuario: admin")
        print("   Contraseña: PlantCare2025!")
        print("   Email: <EMAIL>")
        
        print("\n🚀 Ahora puedes intentar hacer login nuevamente")
    else:
        print("\n❌ No se pudieron corregir las credenciales")

if __name__ == "__main__":
    main()
