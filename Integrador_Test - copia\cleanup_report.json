{"timestamp": "2025-06-16T20:00:47.114441", "files_removed": 63, "directories_removed": 3, "removed_files": ["test_admin_system.py", "test_ai_service.py", "test_calendario.py", "test_diagnosis.py", "test_entrenamiento.py", "test_final.py", "test_frontend_simulation.py", "test_login_admin.py", "test_login_simple.py", "test_mysql_connection.py", "test_recommendations.py", "test_server.py", "debug_login.py", "fix_admin_login.py", "fix_routes.py", "fix_syntax.py", "final_fix.py", "complete_audit.py", "check_hash.py", "verify_admin.py", "sync_admin_user.py", "create_admin.py", "create_admin_simple.py", "init_app.py", "init_db.py", "init_db_mysql.py", "init_mysql.py", "initialize_db.py", "crear_modelo_simple.py", "entrenar_bugambilia.py", "entrenar_modelo.py", "entrenar_modelo_completo.py", "entrenar_modelo_completo_final.py", "entrenar_modelo_corregido.py", "entrenar_modelo_final.py", "entrenar_modelo_personalizado.py", "entrenar_modelo_simple.py", "train_and_integrate.py", "train_model.py", "probar_modelo.py", "monitorear_entrenamiento.py", "data_preparation.py", "auth_middleware.py", "recommendations.py", "minimal_server.py", "simple_app.py", "admin_login_fixed.html", "test_page.html", "entrenamiento_log.txt", "routes/admin.py", "routes/auth.py", "routes/auth_middleware.py", "routes/diagnosis.py", "routes/forum.py", "routes/plants.py", "routes/profile.py", "routes/recomendaciones.py", "routes/reminders.py", "routes/settings.py", "routes/views.py", "templates/detalle.html", "templates/diagnosis_result.html", "templates/tema-detalle.html"], "removed_directories": ["routes/__pycache__", "utils/__pycache__", "models/__pycache__"], "backup_location": "backup_unused_files"}