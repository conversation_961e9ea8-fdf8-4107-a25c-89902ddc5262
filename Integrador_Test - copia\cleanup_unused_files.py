#!/usr/bin/env python3
"""
Script para limpiar archivos no utilizados y mantener solo los necesarios
"""

import os
import shutil
import json
from pathlib import Path

def analyze_used_files():
    """Analizar qué archivos están siendo utilizados realmente"""
    
    print("🔍 ANALIZANDO ARCHIVOS UTILIZADOS")
    print("=" * 50)
    
    # Archivos CORE que SIEMPRE se mantienen
    core_files = {
        'app.py',
        'config.py', 
        'database.py',
        'requirements.txt'
    }
    
    # Directorios CORE que SIEMPRE se mantienen
    core_directories = {
        'templates',
        'static', 
        'routes',
        'data',
        'instance'
    }
    
    # Templates que están siendo utilizados (basado en app.py)
    used_templates = {
        'home.html',
        'login.html', 
        'register.html',
        'biblioteca.html',
        'calendario.html',
        'recomendaciones.html',
        'foro.html',
        'perfil.html',
        'ajustes.html',
        'scanner.html',
        'plant_details.html',
        'planta-detalle.html',
        'base.html',
        'navbar.html',
        'index.html',
        'admin_login_test.html',
        'simple_login.html'
    }
    
    # Subdirectorios de templates utilizados
    used_template_dirs = {
        'admin',
        'auth', 
        'errors',
        'diagnosis'
    }
    
    # Routes que están siendo utilizados (basado en imports en app.py)
    used_routes = {
        'admin_simple.py',
        'api.py',
        'ai_diagnosis.py',
        '__init__.py'
    }
    
    # Archivos de CSS/JS utilizados
    used_static_files = {
        'css/styles.css',
        'css/home.css',
        'css/user-menu.css',
        'js/main.js'
    }
    
    return {
        'core_files': core_files,
        'core_directories': core_directories,
        'used_templates': used_templates,
        'used_template_dirs': used_template_dirs,
        'used_routes': used_routes,
        'used_static_files': used_static_files
    }

def identify_unused_files():
    """Identificar archivos no utilizados"""
    
    used = analyze_used_files()
    unused_files = []
    
    print("\n📋 IDENTIFICANDO ARCHIVOS NO UTILIZADOS")
    print("-" * 40)
    
    # Verificar archivos en raíz
    for file in os.listdir('.'):
        if os.path.isfile(file) and file.endswith('.py'):
            if file not in used['core_files']:
                # Verificar si es un archivo de prueba o temporal
                if any(keyword in file.lower() for keyword in ['test_', 'debug_', 'fix_', 'create_', 'entrenar_', 'check_', 'verify_', 'sync_', 'complete_', 'final_', 'simple_', 'minimal_', 'init_', 'probar_', 'monitorear_', 'train_']):
                    unused_files.append(file)
                    print(f"   🗑️  {file} (archivo de prueba/temporal)")
    
    # Verificar routes no utilizados
    routes_dir = 'routes'
    if os.path.exists(routes_dir):
        for file in os.listdir(routes_dir):
            if file.endswith('.py') and file not in used['used_routes']:
                unused_files.append(f"{routes_dir}/{file}")
                print(f"   🗑️  routes/{file} (ruta no utilizada)")
    
    # Verificar templates no utilizados
    templates_dir = 'templates'
    if os.path.exists(templates_dir):
        for file in os.listdir(templates_dir):
            if file.endswith('.html') and file not in used['used_templates']:
                unused_files.append(f"{templates_dir}/{file}")
                print(f"   🗑️  templates/{file} (template no utilizado)")
    
    return unused_files

def backup_unused_files(unused_files):
    """Crear backup de archivos no utilizados antes de eliminar"""
    
    backup_dir = 'backup_unused_files'
    os.makedirs(backup_dir, exist_ok=True)
    
    print(f"\n💾 CREANDO BACKUP EN: {backup_dir}")
    print("-" * 40)
    
    for file_path in unused_files:
        if os.path.exists(file_path):
            # Crear estructura de directorios en backup
            backup_path = os.path.join(backup_dir, file_path)
            os.makedirs(os.path.dirname(backup_path), exist_ok=True)
            
            # Copiar archivo
            shutil.copy2(file_path, backup_path)
            print(f"   📦 {file_path} -> {backup_path}")

def remove_unused_files(unused_files):
    """Eliminar archivos no utilizados"""
    
    print(f"\n🗑️  ELIMINANDO ARCHIVOS NO UTILIZADOS")
    print("-" * 40)
    
    removed_count = 0
    for file_path in unused_files:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"   ✅ Eliminado: {file_path}")
                removed_count += 1
            except Exception as e:
                print(f"   ❌ Error eliminando {file_path}: {e}")
    
    return removed_count

def clean_empty_directories():
    """Limpiar directorios vacíos"""
    
    print(f"\n📁 LIMPIANDO DIRECTORIOS VACÍOS")
    print("-" * 40)
    
    removed_dirs = []
    
    # Verificar directorios que podrían estar vacíos
    check_dirs = ['routes/__pycache__', 'utils/__pycache__', 'models/__pycache__']
    
    for dir_path in check_dirs:
        if os.path.exists(dir_path) and os.path.isdir(dir_path):
            try:
                # Verificar si está vacío o solo tiene archivos .pyc
                files = os.listdir(dir_path)
                if not files or all(f.endswith('.pyc') for f in files):
                    shutil.rmtree(dir_path)
                    removed_dirs.append(dir_path)
                    print(f"   ✅ Directorio eliminado: {dir_path}")
            except Exception as e:
                print(f"   ❌ Error eliminando directorio {dir_path}: {e}")
    
    return removed_dirs

def create_clean_requirements():
    """Crear requirements.txt limpio con solo las dependencias necesarias"""
    
    print(f"\n📦 CREANDO REQUIREMENTS.TXT LIMPIO")
    print("-" * 40)
    
    # Dependencias CORE necesarias para la aplicación
    core_requirements = [
        "Flask==2.3.3",
        "Flask-Login==0.6.3", 
        "Flask-WTF==1.1.1",
        "Werkzeug==2.3.7",
        "Jinja2==3.1.2",
        "WTForms==3.0.1",
        "Pillow==10.0.0",
        "requests==2.31.0"
    ]
    
    # Crear nuevo requirements.txt
    with open('requirements_clean.txt', 'w') as f:
        f.write("# Dependencias CORE para PlantCare\n")
        f.write("# Generado automáticamente - solo dependencias utilizadas\n\n")
        for req in core_requirements:
            f.write(f"{req}\n")
    
    print("   ✅ requirements_clean.txt creado")
    print("   📋 Dependencias incluidas:")
    for req in core_requirements:
        print(f"      - {req}")

def generate_cleanup_report(unused_files, removed_count, removed_dirs):
    """Generar reporte de limpieza"""
    
    report = {
        'timestamp': str(datetime.now()),
        'files_analyzed': len(unused_files),
        'files_removed': removed_count,
        'directories_removed': len(removed_dirs),
        'unused_files': unused_files,
        'removed_directories': removed_dirs,
        'backup_location': 'backup_unused_files'
    }
    
    with open('cleanup_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📊 REPORTE DE LIMPIEZA GENERADO")
    print("-" * 40)
    print(f"   📁 Archivos analizados: {len(unused_files)}")
    print(f"   🗑️  Archivos eliminados: {removed_count}")
    print(f"   📂 Directorios eliminados: {len(removed_dirs)}")
    print(f"   💾 Backup creado en: backup_unused_files")
    print(f"   📋 Reporte guardado en: cleanup_report.json")

def main():
    """Función principal"""
    
    print("🧹 LIMPIEZA DE ARCHIVOS NO UTILIZADOS - PLANTCARE")
    print("=" * 60)
    
    # Verificar que estamos en el directorio correcto
    if not os.path.exists('app.py'):
        print("❌ Error: No se encontró app.py. Ejecuta este script desde el directorio raíz del proyecto.")
        return
    
    # Identificar archivos no utilizados
    unused_files = identify_unused_files()
    
    if not unused_files:
        print("\n✅ ¡No se encontraron archivos no utilizados!")
        return
    
    print(f"\n📊 RESUMEN:")
    print(f"   🗑️  Archivos no utilizados encontrados: {len(unused_files)}")
    
    # Confirmar limpieza
    print(f"\n⚠️  ¿Proceder con la limpieza? (y/n): ", end="")
    confirm = input().lower()
    
    if confirm not in ['y', 'yes', 'sí', 's']:
        print("❌ Limpieza cancelada")
        return
    
    # Crear backup
    backup_unused_files(unused_files)
    
    # Eliminar archivos
    removed_count = remove_unused_files(unused_files)
    
    # Limpiar directorios vacíos
    removed_dirs = clean_empty_directories()
    
    # Crear requirements limpio
    create_clean_requirements()
    
    # Generar reporte
    from datetime import datetime
    generate_cleanup_report(unused_files, removed_count, removed_dirs)
    
    print(f"\n🎉 ¡LIMPIEZA COMPLETADA!")
    print(f"   ✅ {removed_count} archivos eliminados")
    print(f"   ✅ {len(removed_dirs)} directorios eliminados")
    print(f"   ✅ Backup creado en backup_unused_files/")
    print(f"   ✅ requirements_clean.txt generado")
    
    print(f"\n🚀 PRÓXIMOS PASOS:")
    print(f"   1. Reinicia el servidor: python app.py")
    print(f"   2. Verifica que todo funcione correctamente")
    print(f"   3. Si hay problemas, restaura desde backup_unused_files/")

if __name__ == "__main__":
    main()
