import os
from datetime import timedelta

class Config:
    # Application secret key - for session management and JWT token signing
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'plantcare_secret_key_with_high_entropy_for_security'

    # Activar el uso de JSON como base de datos temporal
    USE_JSON_DB = True

    # Database configuration - usando MySQL (desactivado temporalmente)
    # MySQL está desactivado mientras usamos JSON
    USE_MYSQL = False  # Cambiado a False para usar JSON/ Para usar la base de datos MySQL, cambia a True

    # Configuración MySQL con tus credenciales (se mantiene para cuando quieras volver a activar MySQL)
    MYSQL_HOST = 'localhost'  # Host de MySQL
    MYSQL_USER = 'root'       # Usuario por defecto
    MYSQL_PASSWORD = '12345678'  # Tu contraseña
    MYSQL_DB = 'PlantcareDB'  # Nombre correcto de la base de datos

    # Configurar URI de base de datos según el modo
    if USE_MYSQL:
        SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or f'mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}/{MYSQL_DB}'
        DATABASE_URI = SQLALCHEMY_DATABASE_URI
    else:
        SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///plantcare.db'
        DATABASE_URI = os.environ.get('DATABASE_URI') or os.path.join(os.getcwd(), 'instance', 'plantcare.db')

    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # Session configuration
    SESSION_TYPE = 'filesystem'
    PERMANENT_SESSION_LIFETIME = timedelta(days=7)

    # File upload settings
    UPLOAD_FOLDER = os.path.join(os.getcwd(), 'uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16 MB max upload size

    # JWT configuration
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=1)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)
    JWT_ALGORITHM = 'HS256'

    # Application mode
    DEBUG = os.environ.get('DEBUG') or True
    TESTING = os.environ.get('TESTING') or False

    # Modo sin SQL Server
    USE_SQL_SERVER = False
