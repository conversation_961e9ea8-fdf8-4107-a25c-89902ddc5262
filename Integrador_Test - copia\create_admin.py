#!/usr/bin/env python3
"""
Script para crear el usuario administrador con credenciales específicas
"""

from werkzeug.security import generate_password_hash
import json
from datetime import datetime

def create_admin_user():
    """Crear usuario administrador con credenciales específicas"""
    
    # Credenciales del administrador
    admin_username = "admin"
    admin_password = "PlantCare2025!"  # Contraseña segura para el administrador
    admin_email = "<EMAIL>"
    
    # Generar hash de la contraseña
    password_hash = generate_password_hash(admin_password)
    
    print(f"🔐 Generando usuario administrador...")
    print(f"   Usuario: {admin_username}")
    print(f"   Email: {admin_email}")
    print(f"   Contraseña: {admin_password}")
    print(f"   Hash: {password_hash}")
    
    # Crear estructura de usuarios
    users_data = [
        {
            "UsuarioID": 1,
            "Nombre": "Administrador",
            "Apellido": "PlantCare",
            "Email": admin_email,
            "Username": admin_username,
            "PasswordHash": password_hash,
            "FechaRegistro": datetime.now().isoformat(),
            "UltimoAcceso": None,
            "Activo": True,
            "RolID": 1,
            "IsAdmin": True
        },
        {
            "UsuarioID": 2,
            "Nombre": "Usuario",
            "Apellido": "Ejemplo",
            "Email": "<EMAIL>",
            "Username": "usuario",
            "PasswordHash": generate_password_hash("usuario123"),
            "FechaRegistro": datetime.now().isoformat(),
            "UltimoAcceso": None,
            "Activo": True,
            "RolID": 2,
            "IsAdmin": False
        }
    ]
    
    # Guardar en archivo JSON
    try:
        with open('data/json/users.json', 'w', encoding='utf-8') as f:
            json.dump(users_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Usuario administrador creado exitosamente!")
        print(f"\n📋 Credenciales de acceso:")
        print(f"   Usuario: {admin_username}")
        print(f"   Contraseña: {admin_password}")
        print(f"   Email: {admin_email}")
        print(f"\n⚠️  IMPORTANTE: Guarda estas credenciales en un lugar seguro!")
        
    except Exception as e:
        print(f"❌ Error al crear usuario administrador: {e}")

if __name__ == "__main__":
    create_admin_user()
