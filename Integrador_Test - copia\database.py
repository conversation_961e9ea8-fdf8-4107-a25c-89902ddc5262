
import os
import sqlite3
from flask import g, current_app
from flask_sqlalchemy import SQLAlchemy

# Intentar importar pymysql
try:
    import pymysql
    PYMYSQL_AVAILABLE = True
except ImportError:
    PYMYSQL_AVAILABLE = False

# Crear la instancia de SQLAlchemy
db = SQLAlchemy()

# Variable global para controlar el uso de JSON
using_json_db = False

def get_db_connection():
    """Obtener una conexión a la base de datos (SQLite, MySQL o JSON según configuración)"""
    # Si estamos usando JSON, devolver None
    if current_app.config.get('USE_JSON_DB', False):
        return None
    
    if 'db_connection' not in g:
        # Verificar si estamos usando MySQL
        if current_app.config.get('USE_MYSQL', False) and PYMYSQL_AVAILABLE:
            # Conexión MySQL
            g.db_connection = pymysql.connect(
                host=current_app.config['MYSQL_HOST'],
                user=current_app.config['MYSQL_USER'],
                password=current_app.config['MYSQL_PASSWORD'],
                database=current_app.config['MYSQL_DB'],
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
        else:
            # Conexión SQLite
            g.db_connection = sqlite3.connect(current_app.config['DATABASE_URI'])
            g.db_connection.row_factory = sqlite3.Row

    return g.db_connection

def close_db_connection(e=None):
    """Cerrar la conexión a la base de datos"""
    db_connection = g.pop('db_connection', None)
    if db_connection is not None:
        db_connection.close()

def init_app(app):
    """Inicializar la aplicación con la extensión SQLAlchemy"""
    global using_json_db
    
    # Verificar si debemos usar JSON
    using_json_db = app.config.get('USE_JSON_DB', False)
    
    if using_json_db:
        # Si usamos JSON, inicializar el sistema de almacenamiento JSON
        try:
            from database_json import initialize_sample_data
            initialize_sample_data()
            print("Base de datos JSON inicializada correctamente")
        except Exception as e:
            print(f"Error al inicializar la base de datos JSON: {str(e)}")
    else:
        # Si usamos SQL, inicializar SQLAlchemy
        db.init_app(app)

        # Registrar función para cerrar la conexión
        app.teardown_appcontext(close_db_connection)

        # Crear todas las tablas si no existen
        with app.app_context():
            try:
                db.create_all()
                print("Base de datos SQL inicializada correctamente")
            except Exception as e:
                print(f"Error al inicializar la base de datos SQL: {str(e)}")
