"""
Módulo para manejar el almacenamiento de datos en archivos JSON.
Este módulo proporciona una alternativa temporal a la base de datos SQL.
"""

import os
import json
import logging
from datetime import datetime
from flask import g

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Directorio para almacenar los archivos JSON
JSON_DATA_DIR = os.path.join(os.getcwd(), 'data', 'json')

# Asegurarse de que el directorio exista
os.makedirs(JSON_DATA_DIR, exist_ok=True)

# Mapeo de tablas a archivos JSON
TABLE_FILES = {
    'usuarios': 'users.json',
    'plantas': 'plants.json',
    'tipos_plantas': 'plant_types.json',
    'familias_plantas': 'plant_families.json',
    'estados_salud': 'health_status.json',
    'cuidados_plantas': 'plant_care.json',
    'tipos_suelo': 'soil_types.json',
    'condiciones_luz': 'light_conditions.json',
    'recordatorios': 'reminders.json',
    'tipos_recordatorio': 'reminder_types.json',
    'diagnosticos': 'diagnosis.json',
    'enfermedades': 'diseases.json',
    'temas_foro': 'forum_topics.json',
    'respuestas_foro': 'forum_replies.json',
    'eventos': 'events.json',
    'registros_eventos': 'event_registrations.json',
    'tiendas': 'stores.json'
}

def get_json_path(table_name):
    """Obtener la ruta al archivo JSON para una tabla específica"""
    filename = TABLE_FILES.get(table_name.lower(), f"{table_name.lower()}.json")
    return os.path.join(JSON_DATA_DIR, filename)

def load_json_data(table_name):
    """Cargar datos desde un archivo JSON"""
    json_path = get_json_path(table_name)
    try:
        if os.path.exists(json_path):
            with open(json_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return []
    except Exception as e:
        logger.error(f"Error al cargar datos JSON para {table_name}: {str(e)}")
        return []

def save_json_data(table_name, data):
    """Guardar datos en un archivo JSON"""
    json_path = get_json_path(table_name)
    try:
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2, default=json_serial)
        return True
    except Exception as e:
        logger.error(f"Error al guardar datos JSON para {table_name}: {str(e)}")
        return False

def json_serial(obj):
    """Serializar objetos especiales a JSON"""
    if isinstance(obj, datetime):
        return obj.isoformat()
    raise TypeError(f"Type {type(obj)} not serializable")

# Clase para simular un modelo de base de datos
class JsonModel:
    """Clase base para modelos que usan almacenamiento JSON"""
    
    # Nombre de la tabla (archivo JSON)
    __tablename__ = None
    
    # Campo de ID primario
    id_field = 'id'
    
    @classmethod
    def query_all(cls):
        """Obtener todos los registros"""
        return load_json_data(cls.__tablename__)
    
    @classmethod
    def query_by_id(cls, id_value):
        """Obtener un registro por su ID"""
        data = load_json_data(cls.__tablename__)
        for item in data:
            if item.get(cls.id_field) == id_value:
                return item
        return None
    
    @classmethod
    def query_filter(cls, **kwargs):
        """Filtrar registros por criterios"""
        data = load_json_data(cls.__tablename__)
        results = []
        
        for item in data:
            match = True
            for key, value in kwargs.items():
                if item.get(key) != value:
                    match = False
                    break
            
            if match:
                results.append(item)
        
        return results
    
    @classmethod
    def add(cls, data):
        """Añadir un nuevo registro"""
        all_data = load_json_data(cls.__tablename__)
        
        # Generar un nuevo ID si no se proporciona
        if cls.id_field not in data:
            max_id = 0
            for item in all_data:
                if item.get(cls.id_field, 0) > max_id:
                    max_id = item.get(cls.id_field)
            data[cls.id_field] = max_id + 1
        
        all_data.append(data)
        save_json_data(cls.__tablename__, all_data)
        return data
    
    @classmethod
    def update(cls, id_value, data):
        """Actualizar un registro existente"""
        all_data = load_json_data(cls.__tablename__)
        updated = False
        
        for i, item in enumerate(all_data):
            if item.get(cls.id_field) == id_value:
                # Mantener el ID original
                data[cls.id_field] = id_value
                all_data[i] = data
                updated = True
                break
        
        if updated:
            save_json_data(cls.__tablename__, all_data)
            return data
        return None
    
    @classmethod
    def delete(cls, id_value):
        """Eliminar un registro"""
        all_data = load_json_data(cls.__tablename__)
        initial_count = len(all_data)
        
        all_data = [item for item in all_data if item.get(cls.id_field) != id_value]
        
        if len(all_data) < initial_count:
            save_json_data(cls.__tablename__, all_data)
            return True
        return False

# Modelos específicos para cada tabla
class UserJson(JsonModel):
    __tablename__ = 'usuarios'
    id_field = 'UsuarioID'

class PlantJson(JsonModel):
    __tablename__ = 'plantas'
    id_field = 'PlantaID'

class PlantTypeJson(JsonModel):
    __tablename__ = 'tipos_plantas'
    id_field = 'TipoPlantaID'

class PlantFamilyJson(JsonModel):
    __tablename__ = 'familias_plantas'
    id_field = 'FamiliaID'

class HealthStatusJson(JsonModel):
    __tablename__ = 'estados_salud'
    id_field = 'EstadoSaludID'

class PlantCareJson(JsonModel):
    __tablename__ = 'cuidados_plantas'
    id_field = 'CuidadoID'

class SoilTypeJson(JsonModel):
    __tablename__ = 'tipos_suelo'
    id_field = 'TipoSueloID'

class LightConditionJson(JsonModel):
    __tablename__ = 'condiciones_luz'
    id_field = 'CondicionLuzID'

class ReminderJson(JsonModel):
    __tablename__ = 'recordatorios'
    id_field = 'RecordatorioID'

class ReminderTypeJson(JsonModel):
    __tablename__ = 'tipos_recordatorio'
    id_field = 'TipoRecordatorioID'

class DiagnosisJson(JsonModel):
    __tablename__ = 'diagnosticos'
    id_field = 'DiagnosticoID'

class DiseaseJson(JsonModel):
    __tablename__ = 'enfermedades'
    id_field = 'EnfermedadID'

class ForumTopicJson(JsonModel):
    __tablename__ = 'temas_foro'
    id_field = 'TemaID'

class ForumReplyJson(JsonModel):
    __tablename__ = 'respuestas_foro'
    id_field = 'RespuestaID'

class EventJson(JsonModel):
    __tablename__ = 'eventos'
    id_field = 'EventoID'

class EventRegistrationJson(JsonModel):
    __tablename__ = 'registros_eventos'
    id_field = 'RegistroID'

class StoreJson(JsonModel):
    __tablename__ = 'tiendas'
    id_field = 'TiendaID'

# Función para inicializar datos de ejemplo
def initialize_sample_data():
    """Inicializar datos de ejemplo en los archivos JSON"""
    # Usuarios
    if not load_json_data('usuarios'):
        users = [
            {
                "UsuarioID": 1,
                "Nombre": "Admin",
                "Apellido": "Sistema",
                "Email": "<EMAIL>",
                "Username": "admin",
                "PasswordHash": "pbkdf2:sha256:150000$abc123",
                "FechaRegistro": datetime.now().isoformat(),
                "UltimoAcceso": None,
                "Activo": True,
                "RolID": 1
            },
            {
                "UsuarioID": 2,
                "Nombre": "Usuario",
                "Apellido": "Ejemplo",
                "Email": "<EMAIL>",
                "Username": "usuario",
                "PasswordHash": "pbkdf2:sha256:150000$def456",
                "FechaRegistro": datetime.now().isoformat(),
                "UltimoAcceso": None,
                "Activo": True,
                "RolID": 2
            }
        ]
        save_json_data('usuarios', users)
        logger.info("Datos de ejemplo para usuarios inicializados")
    
    # Tipos de plantas
    if not load_json_data('tipos_plantas'):
        plant_types = [
            {
                "TipoPlantaID": 1,
                "Nombre": "Cactus",
                "NombreCientifico": "Cactaceae",
                "Descripcion": "Plantas suculentas adaptadas a climas áridos",
                "RequerimientosSol": "Sol directo",
                "RequerimientosAgua": "Poco frecuente",
                "RequerimientosSuelo": "Bien drenado",
                "FamiliaBotanicaID": 1
            },
            {
                "TipoPlantaID": 2,
                "Nombre": "Suculenta",
                "NombreCientifico": "Crassulaceae",
                "Descripcion": "Plantas que almacenan agua en hojas y tallos",
                "RequerimientosSol": "Sol indirecto",
                "RequerimientosAgua": "Moderado",
                "RequerimientosSuelo": "Arenoso",
                "FamiliaBotanicaID": 2
            }
        ]
        save_json_data('tipos_plantas', plant_types)
        logger.info("Datos de ejemplo para tipos de plantas inicializados")
    
    # Continuar con otras tablas según sea necesario
    logger.info("Inicialización de datos de ejemplo completada")