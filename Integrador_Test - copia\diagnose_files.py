#!/usr/bin/env python3
"""
Diagnóstico rápido de archivos de datos
"""

import os
import json

def diagnose_files():
    """Diagnosticar archivos de datos"""
    
    print("🔍 DIAGNÓSTICO DE ARCHIVOS DE DATOS")
    print("=" * 50)
    
    # Verificar directorio actual
    current_dir = os.getcwd()
    print(f"📁 Directorio actual: {current_dir}")
    
    # Archivos a verificar
    files_to_check = [
        'data/json/plants.json',
        'data/json/plant_recommendations.json',
        'data/json/users.json',
        'data/json/light_conditions.json',
        'data/json/plant_types.json'
    ]
    
    print(f"\n📋 Verificando archivos:")
    for file_path in files_to_check:
        full_path = os.path.join(current_dir, file_path)
        exists = os.path.exists(file_path)
        size = os.path.getsize(file_path) if exists else 0
        
        status = "✅" if exists else "❌"
        print(f"   {status} {file_path}")
        print(f"      Ruta completa: {full_path}")
        print(f"      Existe: {exists}")
        print(f"      Tamaño: {size} bytes")
        
        if exists and file_path.endswith('.json'):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                print(f"      Contenido: {len(data)} elementos")
                if data and isinstance(data, list) and len(data) > 0:
                    first_item = data[0]
                    if isinstance(first_item, dict):
                        print(f"      Primer elemento: {list(first_item.keys())[:3]}...")
            except Exception as e:
                print(f"      ❌ Error leyendo JSON: {e}")
        print()
    
    # Verificar estructura de directorios
    print(f"📂 Estructura de directorios:")
    if os.path.exists('data'):
        print(f"   ✅ data/")
        if os.path.exists('data/json'):
            print(f"   ✅ data/json/")
            json_files = os.listdir('data/json')
            for file in json_files:
                print(f"      📄 {file}")
        else:
            print(f"   ❌ data/json/ NO EXISTE")
    else:
        print(f"   ❌ data/ NO EXISTE")
    
    # Probar carga desde API
    print(f"\n🧪 Probando carga desde código:")
    try:
        plants_file = os.path.join('data', 'json', 'plant_recommendations.json')
        print(f"   Ruta construida: {plants_file}")
        print(f"   Existe: {os.path.exists(plants_file)}")
        
        if os.path.exists(plants_file):
            with open(plants_file, 'r', encoding='utf-8') as f:
                plants_data = json.load(f)
            print(f"   ✅ Cargado exitosamente: {len(plants_data)} plantas")
        else:
            print(f"   ❌ No se pudo cargar el archivo")
            
    except Exception as e:
        print(f"   💥 Error: {e}")

if __name__ == "__main__":
    diagnose_files()
