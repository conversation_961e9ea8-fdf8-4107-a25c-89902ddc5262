"""
Script para entrenar el modelo de IA para reconocimiento de plantas y enfermedades.

Este script:
1. Verifica las dependencias necesarias
2. Prepara los datos de entrenamiento
3. Entrena el modelo
4. Guarda el modelo entrenado

Uso:
    python entrenar_modelo.py --data_dir ruta/a/imagenes_plantas --epochs 20
"""

import os
import sys
import argparse
import shutil
from pathlib import Path

def check_dependencies():
    """Verifica que todas las dependencias necesarias estén instaladas"""
    missing_deps = []
    
    try:
        import cv2
    except ImportError:
        missing_deps.append("opencv-python")
    
    try:
        import torch
        import torchvision
    except ImportError:
        missing_deps.append("torch torchvision")
    
    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy")
    
    try:
        import matplotlib
    except ImportError:
        missing_deps.append("matplotlib")
    
    try:
        from sklearn.model_selection import train_test_split
    except ImportError:
        missing_deps.append("scikit-learn")
    
    if missing_deps:
        print("Faltan las siguientes dependencias:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\nPor favor, instálelas con pip:")
        print(f"pip install {' '.join(missing_deps)}")
        return False
    
    return True

def main():
    # Configurar argumentos de línea de comandos
    parser = argparse.ArgumentParser(description='Entrenar modelo de IA para reconocimiento de plantas')
    parser.add_argument('--data_dir', type=str, required=True, 
                        help='Directorio que contiene las imágenes de plantas organizadas por clase')
    parser.add_argument('--processed_dir', type=str, default='data/processed_plant_images',
                        help='Directorio donde se guardarán las imágenes procesadas')
    parser.add_argument('--model_path', type=str, default='models/plant_disease_model.pth',
                        help='Ruta donde se guardará el modelo entrenado')
    parser.add_argument('--class_names_path', type=str, default='models/class_names.json',
                        help='Ruta donde se guardarán los nombres de las clases')
    parser.add_argument('--epochs', type=int, default=20,
                        help='Número de épocas para entrenar el modelo')
    parser.add_argument('--batch_size', type=int, default=32,
                        help='Tamaño del lote para entrenamiento')
    parser.add_argument('--skip_data_prep', action='store_true',
                        help='Omitir la preparación de datos si ya están procesados')
    
    args = parser.parse_args()
    
    # Verificar dependencias
    print("Verificando dependencias...")
    if not check_dependencies():
        return 1
    
    # Importar módulos después de verificar dependencias
    from data_preparation import DataPreparation
    from train_model import ModelTrainer
    
    # Crear directorios necesarios
    os.makedirs(os.path.dirname(args.model_path), exist_ok=True)
    os.makedirs(args.processed_dir, exist_ok=True)
    
    # Paso 1: Preparar los datos
    if not args.skip_data_prep:
        print("\n=== Preparando datos de entrenamiento ===")
        data_prep = DataPreparation(
            raw_data_dir=args.data_dir,
            processed_data_dir=args.processed_dir,
            class_names_file=args.class_names_path
        )
        
        # Crear estructura de directorios
        data_prep.create_directory_structure()
        
        # Preprocesar imágenes
        data_prep.preprocess_images()
        
        # Verificar el conjunto de datos
        data_prep.verify_dataset()
    else:
        print("\n=== Omitiendo preparación de datos ===")
    
    # Paso 2: Entrenar el modelo
    print("\n=== Entrenando modelo ===")
    trainer = ModelTrainer(
        data_dir=args.processed_dir,
        model_save_path=args.model_path,
        class_names_path=args.class_names_path,
        batch_size=args.batch_size,
        num_epochs=args.epochs
    )
    
    # Cargar datos
    trainer.load_data()
    
    # Crear modelo
    trainer.create_model()
    
    # Entrenar modelo
    history = trainer.train_model()
    
    # Graficar historial de entrenamiento
    trainer.plot_training_history(history)
    
    print("\n=== Entrenamiento completado ===")
    print(f"Modelo guardado en: {args.model_path}")
    print(f"Nombres de clases guardados en: {args.class_names_path}")
    print(f"Gráfica de entrenamiento guardada en: {os.path.join(os.path.dirname(args.model_path), 'training_history.png')}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
