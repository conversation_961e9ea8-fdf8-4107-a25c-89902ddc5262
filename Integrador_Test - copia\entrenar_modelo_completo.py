"""
Script para entrenar un modelo con todas las plantas disponibles.
"""

import os
import json
import shutil
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torchvision import datasets, transforms, models
import matplotlib.pyplot as plt
import random
import re

# Configuración
SRC_DIR = "../plantas imagenes/PLANTAS"  # Directorio con las imágenes originales
PROCESSED_DIR = "data/processed_plant_images_completo"  # Directorio para imágenes procesadas
MODEL_PATH = "models/plant_disease_model.pth"  # Ruta para guardar el modelo
CLASS_NAMES_PATH = "models/class_names.json"  # Ruta para guardar nombres de clases
BATCH_SIZE = 16
NUM_EPOCHS = 10

# Asegurarse de que existan los directorios
os.makedirs(os.path.dirname(MODEL_PATH), exist_ok=True)
os.makedirs(PROCESSED_DIR, exist_ok=True)

# Crear directorios para datos procesados
train_dir = os.path.join(PROCESSED_DIR, 'train')
val_dir = os.path.join(PROCESSED_DIR, 'val')
os.makedirs(train_dir, exist_ok=True)
os.makedirs(val_dir, exist_ok=True)

# Detectar todas las plantas disponibles
print("Detectando plantas disponibles...")
plantas = []
plantas_enfermedades = []

for item in os.listdir(SRC_DIR):
    item_path = os.path.join(SRC_DIR, item)
    if os.path.isdir(item_path):
        if "enfermedades" in item.lower():
            plantas_enfermedades.append(item)
        else:
            plantas.append(item)

print(f"Plantas encontradas: {len(plantas)}")
for planta in plantas:
    print(f"  - {planta}")

print(f"Plantas con enfermedades encontradas: {len(plantas_enfermedades)}")
for planta in plantas_enfermedades:
    print(f"  - {planta}")

# Crear directorios para cada clase
for planta in plantas + plantas_enfermedades:
    # Normalizar el nombre para usarlo como directorio
    dir_name = planta.replace(" ", "_").lower()
    
    # Crear directorios
    os.makedirs(os.path.join(train_dir, dir_name), exist_ok=True)
    os.makedirs(os.path.join(val_dir, dir_name), exist_ok=True)

# Copiar imágenes para cada planta
print("\nCopiando imágenes...")
for planta in plantas + plantas_enfermedades:
    planta_src = os.path.join(SRC_DIR, planta)
    dir_name = planta.replace(" ", "_").lower()
    
    # Verificar que la carpeta exista
    if not os.path.exists(planta_src):
        print(f"Error: No se encontró la carpeta {planta_src}")
        continue
    
    # Obtener archivos de imágenes
    planta_files = [f for f in os.listdir(planta_src) 
                   if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    
    if not planta_files:
        print(f"Advertencia: No se encontraron imágenes en {planta_src}")
        continue
    
    # Dividir en conjuntos de entrenamiento y validación
    random.seed(42)
    random.shuffle(planta_files)
    
    train_files = planta_files[:int(len(planta_files)*0.8)]
    val_files = planta_files[int(len(planta_files)*0.8):]
    
    # Copiar archivos
    for f in train_files:
        try:
            shutil.copy(os.path.join(planta_src, f), os.path.join(train_dir, dir_name, f))
        except Exception as e:
            print(f"Error al copiar {f}: {str(e)}")
    
    for f in val_files:
        try:
            shutil.copy(os.path.join(planta_src, f), os.path.join(val_dir, dir_name, f))
        except Exception as e:
            print(f"Error al copiar {f}: {str(e)}")
    
    print(f"Imágenes de {planta}: {len(train_files)} entrenamiento, {len(val_files)} validación")

# Transformaciones para las imágenes
data_transforms = {
    'train': transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.RandomHorizontalFlip(),
        transforms.RandomRotation(15),
        transforms.ColorJitter(brightness=0.1, contrast=0.1, saturation=0.1),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    ]),
    'val': transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    ]),
}

# Cargar los conjuntos de datos
print("\nCargando conjuntos de datos...")
try:
    # Cargar los conjuntos de datos
    image_datasets = {
        'train': datasets.ImageFolder(train_dir, data_transforms['train']),
        'val': datasets.ImageFolder(val_dir, data_transforms['val'])
    }
    
    # Crear dataloaders
    dataloaders = {
        'train': DataLoader(image_datasets['train'], batch_size=BATCH_SIZE, 
                           shuffle=True, num_workers=2),
        'val': DataLoader(image_datasets['val'], batch_size=BATCH_SIZE, 
                         shuffle=False, num_workers=2)
    }
    
    # Obtener tamaños de los conjuntos de datos
    dataset_sizes = {x: len(image_datasets[x]) for x in ['train', 'val']}
    
    # Obtener nombres de clases y guardarlos
    class_names = image_datasets['train'].classes
    class_to_idx = image_datasets['train'].class_to_idx
    
    print(f"Clases encontradas: {len(class_names)}")
    for idx, name in enumerate(class_names):
        print(f"  {idx}: {name}")
    
    print(f"Imágenes de entrenamiento: {dataset_sizes['train']}")
    print(f"Imágenes de validación: {dataset_sizes['val']}")
    
    # Guardar los nombres de las clases en un archivo JSON
    with open(CLASS_NAMES_PATH, 'w', encoding='utf-8') as f:
        json.dump(class_names, f, ensure_ascii=False, indent=2)
    
    print(f"Nombres de clases guardados en {CLASS_NAMES_PATH}")
    
    # Configurar el dispositivo (GPU o CPU)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Usando dispositivo: {device}")
    
    # Crear un modelo preentrenado
    print("Creando modelo...")
    model = models.resnet50(weights='IMAGENET1K_V2')
    
    # Congelar todos los parámetros para que no se actualicen durante el entrenamiento
    for param in model.parameters():
        param.requires_grad = False
    
    # Reemplazar la capa final para nuestra tarea de clasificación
    num_ftrs = model.fc.in_features
    model.fc = nn.Sequential(
        nn.Linear(num_ftrs, 512),
        nn.ReLU(),
        nn.Dropout(0.3),
        nn.Linear(512, 256),
        nn.ReLU(),
        nn.Dropout(0.2),
        nn.Linear(256, len(class_names))
    )
    
    # Mover el modelo al dispositivo (GPU o CPU)
    model = model.to(device)
    
    # Definir función de pérdida y optimizador
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.fc.parameters(), lr=0.001)
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)
    
    # Entrenar el modelo
    print(f"Iniciando entrenamiento por {NUM_EPOCHS} épocas...")
    
    best_acc = 0.0
    history = {'train_loss': [], 'val_loss': [], 'train_acc': [], 'val_acc': []}
    
    for epoch in range(NUM_EPOCHS):
        print(f'Época {epoch+1}/{NUM_EPOCHS}')
        print('-' * 10)
        
        # Cada época tiene una fase de entrenamiento y validación
        for phase in ['train', 'val']:
            if phase == 'train':
                model.train()  # Modo de entrenamiento
            else:
                model.eval()   # Modo de evaluación
            
            running_loss = 0.0
            running_corrects = 0
            
            # Iterar sobre los datos
            for inputs, labels in dataloaders[phase]:
                inputs = inputs.to(device)
                labels = labels.to(device)
                
                # Poner a cero los gradientes del parámetro
                optimizer.zero_grad()
                
                # Forward
                with torch.set_grad_enabled(phase == 'train'):
                    outputs = model(inputs)
                    _, preds = torch.max(outputs, 1)
                    loss = criterion(outputs, labels)
                    
                    # Backward + optimize solo si es fase de entrenamiento
                    if phase == 'train':
                        loss.backward()
                        optimizer.step()
                
                # Estadísticas
                running_loss += loss.item() * inputs.size(0)
                running_corrects += torch.sum(preds == labels.data)
            
            if phase == 'train':
                scheduler.step()
            
            epoch_loss = running_loss / dataset_sizes[phase]
            epoch_acc = running_corrects.double() / dataset_sizes[phase]
            
            # Guardar historial
            if phase == 'train':
                history['train_loss'].append(epoch_loss)
                history['train_acc'].append(epoch_acc.item())
            else:
                history['val_loss'].append(epoch_loss)
                history['val_acc'].append(epoch_acc.item())
            
            print(f'{phase} Loss: {epoch_loss:.4f} Acc: {epoch_acc:.4f}')
            
            # Guardar el mejor modelo
            if phase == 'val' and epoch_acc > best_acc:
                best_acc = epoch_acc
                torch.save(model, MODEL_PATH)
                print(f"Mejor modelo guardado con precisión: {best_acc:.4f}")
        
        print()
    
    print(f'Entrenamiento completado. Mejor precisión de validación: {best_acc:.4f}')
    
    # Graficar el historial de entrenamiento
    plt.figure(figsize=(12, 4))
    
    # Graficar pérdida
    plt.subplot(1, 2, 1)
    plt.plot(history['train_loss'], label='Entrenamiento')
    plt.plot(history['val_loss'], label='Validación')
    plt.title('Pérdida del modelo')
    plt.ylabel('Pérdida')
    plt.xlabel('Época')
    plt.legend()
    
    # Graficar precisión
    plt.subplot(1, 2, 2)
    plt.plot(history['train_acc'], label='Entrenamiento')
    plt.plot(history['val_acc'], label='Validación')
    plt.title('Precisión del modelo')
    plt.ylabel('Precisión')
    plt.xlabel('Época')
    plt.legend()
    
    plt.tight_layout()
    
    # Guardar la gráfica
    plot_path = os.path.join(os.path.dirname(MODEL_PATH), 'training_history.png')
    plt.savefig(plot_path)
    print(f"Gráfica de entrenamiento guardada en {plot_path}")
    plt.close()
    
    print("\n=== Entrenamiento completado ===")
    print(f"Modelo guardado en: {MODEL_PATH}")
    print(f"Nombres de clases guardados en: {CLASS_NAMES_PATH}")
    print(f"Gráfica de entrenamiento guardada en: {plot_path}")
    
except Exception as e:
    print(f"Error durante el entrenamiento: {str(e)}")
    import traceback
    traceback.print_exc()
