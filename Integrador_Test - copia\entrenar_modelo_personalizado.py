"""
Script personalizado para entrenar el modelo de IA usando las carpetas existentes.
Este script:
1. Detecta automáticamente las carpetas de plantas disponibles
2. Prepara los datos de entrenamiento
3. Entrena el modelo
4. Guarda el modelo entrenado
"""

import os
import sys
import argparse
import shutil
from pathlib import Path
import json

def check_dependencies():
    """Verifica que todas las dependencias necesarias estén instaladas"""
    missing_deps = []
    
    try:
        import cv2
    except ImportError:
        missing_deps.append("opencv-python")
    
    try:
        import torch
        import torchvision
    except ImportError:
        missing_deps.append("torch torchvision")
    
    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy")
    
    try:
        import matplotlib
    except ImportError:
        missing_deps.append("matplotlib")
    
    try:
        from sklearn.model_selection import train_test_split
    except ImportError:
        missing_deps.append("scikit-learn")
    
    if missing_deps:
        print("Faltan las siguientes dependencias:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\nPor favor, instálelas con pip:")
        print(f"pip install {' '.join(missing_deps)}")
        return False
    
    return True

def detect_classes(data_dir):
    """Detecta automáticamente las clases disponibles en el directorio de datos"""
    classes = []
    
    # Listar todas las carpetas en el directorio de datos
    for item in os.listdir(data_dir):
        item_path = os.path.join(data_dir, item)
        if os.path.isdir(item_path):
            # Verificar si la carpeta contiene imágenes
            has_images = False
            for ext in ['.jpg', '.jpeg', '.png']:
                if any(f.lower().endswith(ext) for f in os.listdir(item_path)):
                    has_images = True
                    break
            
            if has_images:
                classes.append(item)
    
    return classes

def prepare_data(data_dir, processed_dir, classes):
    """Prepara los datos para el entrenamiento"""
    import cv2
    import numpy as np
    from sklearn.model_selection import train_test_split
    
    # Crear directorios para datos procesados
    train_dir = os.path.join(processed_dir, 'train')
    val_dir = os.path.join(processed_dir, 'val')
    
    os.makedirs(train_dir, exist_ok=True)
    os.makedirs(val_dir, exist_ok=True)
    
    # Crear subdirectorios para cada clase
    for class_name in classes:
        os.makedirs(os.path.join(train_dir, class_name), exist_ok=True)
        os.makedirs(os.path.join(val_dir, class_name), exist_ok=True)
    
    # Procesar cada clase
    for class_name in classes:
        print(f"Procesando clase: {class_name}")
        
        # Obtener todas las imágenes de esta clase
        class_dir = os.path.join(data_dir, class_name)
        image_files = [f for f in os.listdir(class_dir) 
                      if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        
        if not image_files:
            print(f"  Advertencia: No se encontraron imágenes para {class_name}. Omitiendo.")
            continue
        
        # Dividir en conjuntos de entrenamiento y validación
        train_files, val_files = train_test_split(
            image_files, test_size=0.2, random_state=42
        )
        
        print(f"  Imágenes de entrenamiento: {len(train_files)}")
        print(f"  Imágenes de validación: {len(val_files)}")
        
        # Procesar imágenes de entrenamiento
        for img_file in train_files:
            process_image(
                os.path.join(class_dir, img_file),
                os.path.join(train_dir, class_name),
                target_size=(224, 224),
                augment=True
            )
        
        # Procesar imágenes de validación
        for img_file in val_files:
            process_image(
                os.path.join(class_dir, img_file),
                os.path.join(val_dir, class_name),
                target_size=(224, 224),
                augment=False
            )
    
    # Verificar el conjunto de datos
    stats = verify_dataset(processed_dir, classes)
    return stats

def process_image(src_path, dst_dir, target_size, augment):
    """Procesa una imagen: redimensiona, normaliza y opcionalmente aumenta"""
    import cv2
    import numpy as np
    
    try:
        # Cargar y redimensionar la imagen
        img = cv2.imread(src_path)
        if img is None:
            print(f"  Advertencia: No se pudo leer la imagen {src_path}. Omitiendo.")
            return
        
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        img_resized = cv2.resize(img, target_size)
        
        # Guardar la imagen redimensionada
        filename = os.path.basename(src_path)
        base_name, ext = os.path.splitext(filename)
        
        # Guardar la imagen original redimensionada
        out_path = os.path.join(dst_dir, filename)
        cv2.imwrite(out_path, cv2.cvtColor(img_resized, cv2.COLOR_RGB2BGR))
        
        # Realizar aumento si se solicita
        if augment:
            # 1. Volteo horizontal
            img_h_flip = cv2.flip(img_resized, 1)
            cv2.imwrite(
                os.path.join(dst_dir, f"{base_name}_h_flip{ext}"), 
                cv2.cvtColor(img_h_flip, cv2.COLOR_RGB2BGR)
            )
            
            # 2. Volteo vertical
            img_v_flip = cv2.flip(img_resized, 0)
            cv2.imwrite(
                os.path.join(dst_dir, f"{base_name}_v_flip{ext}"), 
                cv2.cvtColor(img_v_flip, cv2.COLOR_RGB2BGR)
            )
            
            # 3. Rotación (90 grados)
            img_rot = cv2.rotate(img_resized, cv2.ROTATE_90_CLOCKWISE)
            cv2.imwrite(
                os.path.join(dst_dir, f"{base_name}_rot90{ext}"), 
                cv2.cvtColor(img_rot, cv2.COLOR_RGB2BGR)
            )
            
            # 4. Ajuste de brillo
            brightness = np.ones(img_resized.shape, dtype="uint8") * 30
            img_bright = cv2.add(img_resized, brightness)
            cv2.imwrite(
                os.path.join(dst_dir, f"{base_name}_bright{ext}"), 
                cv2.cvtColor(img_bright, cv2.COLOR_RGB2BGR)
            )
            
    except Exception as e:
        print(f"  Error al procesar la imagen {src_path}: {str(e)}")

def verify_dataset(processed_dir, classes):
    """Verifica la estructura del conjunto de datos procesado y muestra estadísticas"""
    train_dir = os.path.join(processed_dir, 'train')
    val_dir = os.path.join(processed_dir, 'val')
    
    stats = {
        'train': {},
        'val': {},
        'total_train': 0,
        'total_val': 0
    }
    
    # Verificar datos de entrenamiento
    for class_name in classes:
        class_dir = os.path.join(train_dir, class_name)
        if os.path.exists(class_dir):
            count = len([f for f in os.listdir(class_dir) 
                        if f.lower().endswith(('.png', '.jpg', '.jpeg'))])
            stats['train'][class_name] = count
            stats['total_train'] += count
        else:
            stats['train'][class_name] = 0
    
    # Verificar datos de validación
    for class_name in classes:
        class_dir = os.path.join(val_dir, class_name)
        if os.path.exists(class_dir):
            count = len([f for f in os.listdir(class_dir) 
                        if f.lower().endswith(('.png', '.jpg', '.jpeg'))])
            stats['val'][class_name] = count
            stats['total_val'] += count
        else:
            stats['val'][class_name] = 0
    
    # Mostrar estadísticas
    print("\nEstadísticas del conjunto de datos:")
    print(f"- Total de imágenes de entrenamiento: {stats['total_train']}")
    print(f"- Total de imágenes de validación: {stats['total_val']}")
    print(f"- Distribución de clases:")
    
    for class_name in classes:
        train_count = stats['train'].get(class_name, 0)
        val_count = stats['val'].get(class_name, 0)
        print(f"  - {class_name}: {train_count} entrenamiento, {val_count} validación")
    
    return stats

def main():
    # Configurar argumentos de línea de comandos
    parser = argparse.ArgumentParser(description='Entrenar modelo de IA para reconocimiento de plantas')
    parser.add_argument('--data_dir', type=str, required=True, 
                        help='Directorio que contiene las imágenes de plantas organizadas por clase')
    parser.add_argument('--processed_dir', type=str, default='data/processed_plant_images',
                        help='Directorio donde se guardarán las imágenes procesadas')
    parser.add_argument('--model_path', type=str, default='models/plant_disease_model.pth',
                        help='Ruta donde se guardará el modelo entrenado')
    parser.add_argument('--class_names_path', type=str, default='models/class_names.json',
                        help='Ruta donde se guardarán los nombres de las clases')
    parser.add_argument('--epochs', type=int, default=10,
                        help='Número de épocas para entrenar el modelo')
    parser.add_argument('--batch_size', type=int, default=32,
                        help='Tamaño del lote para entrenamiento')
    parser.add_argument('--skip_data_prep', action='store_true',
                        help='Omitir la preparación de datos si ya están procesados')
    
    args = parser.parse_args()
    
    # Verificar dependencias
    print("Verificando dependencias...")
    if not check_dependencies():
        return 1
    
    # Crear directorios necesarios
    os.makedirs(os.path.dirname(args.model_path), exist_ok=True)
    os.makedirs(args.processed_dir, exist_ok=True)
    
    # Detectar clases disponibles
    print("\nDetectando clases disponibles...")
    classes = detect_classes(args.data_dir)
    
    if not classes:
        print("Error: No se encontraron clases válidas en el directorio de datos.")
        return 1
    
    print(f"Se encontraron {len(classes)} clases:")
    for cls in classes:
        print(f"  - {cls}")
    
    # Guardar los nombres de las clases
    with open(args.class_names_path, 'w', encoding='utf-8') as f:
        json.dump(classes, f, ensure_ascii=False, indent=2)
    
    print(f"Nombres de clases guardados en: {args.class_names_path}")
    
    # Paso 1: Preparar los datos
    if not args.skip_data_prep:
        print("\n=== Preparando datos de entrenamiento ===")
        prepare_data(args.data_dir, args.processed_dir, classes)
    else:
        print("\n=== Omitiendo preparación de datos ===")
    
    # Paso 2: Entrenar el modelo
    print("\n=== Entrenando modelo ===")
    
    # Importar después de verificar dependencias
    from train_model import ModelTrainer
    
    trainer = ModelTrainer(
        data_dir=args.processed_dir,
        model_save_path=args.model_path,
        class_names_path=args.class_names_path,
        batch_size=args.batch_size,
        num_epochs=args.epochs
    )
    
    # Cargar datos
    trainer.load_data()
    
    # Crear modelo
    trainer.create_model()
    
    # Entrenar modelo
    history = trainer.train_model()
    
    # Graficar historial de entrenamiento
    trainer.plot_training_history(history)
    
    print("\n=== Entrenamiento completado ===")
    print(f"Modelo guardado en: {args.model_path}")
    print(f"Nombres de clases guardados en: {args.class_names_path}")
    print(f"Gráfica de entrenamiento guardada en: {os.path.join(os.path.dirname(args.model_path), 'training_history.png')}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
