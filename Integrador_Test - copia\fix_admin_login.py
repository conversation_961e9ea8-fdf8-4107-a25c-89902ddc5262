#!/usr/bin/env python3
"""
Script para diagnosticar y corregir problemas de login del administrador
"""

import json
import hashlib
from datetime import datetime

def hash_password(password):
    """Hash de contraseña usando SHA256"""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password, password_hash):
    """Verificar contraseña - compatible con múltiples formatos"""
    # Si es hash SHA256 simple (nuevo formato)
    if len(password_hash) == 64 and not password_hash.startswith('pbkdf2:'):
        return hash_password(password) == password_hash
    
    # Si es hash pbkdf2 (formato anterior)
    if password_hash.startswith('pbkdf2:'):
        try:
            from werkzeug.security import check_password_hash
            return check_password_hash(password_hash, password)
        except ImportError:
            print("⚠️ werkzeug no disponible para verificar pbkdf2")
            return False
    
    # Fallback para otros formatos
    return hash_password(password) == password_hash

def diagnose_login_issue():
    """Diagnosticar problemas de login"""
    
    print("🔍 DIAGNÓSTICO DEL SISTEMA DE LOGIN")
    print("=" * 50)
    
    # 1. Verificar archivo de usuarios
    try:
        with open('data/users.json', 'r', encoding='utf-8') as f:
            users = json.load(f)
        print("✅ Archivo de usuarios cargado correctamente")
    except Exception as e:
        print(f"❌ Error cargando usuarios: {e}")
        return False
    
    # 2. Verificar usuario admin
    admin_user = users.get("1")
    if not admin_user:
        print("❌ Usuario admin no encontrado")
        return False
    
    print(f"✅ Usuario admin encontrado:")
    print(f"   Username: {admin_user.get('username')}")
    print(f"   Email: {admin_user.get('email')}")
    print(f"   Es admin: {admin_user.get('is_admin')}")
    print(f"   Rol: {admin_user.get('role')}")
    
    # 3. Probar contraseñas comunes
    test_passwords = [
        'PlantCare2025!',
        'admin',
        'admin123',
        'password',
        '123456'
    ]
    
    stored_hash = admin_user.get('password_hash', '')
    print(f"\n🔐 Hash almacenado: {stored_hash[:20]}...")
    
    print("\n🧪 Probando contraseñas:")
    for password in test_passwords:
        is_valid = verify_password(password, stored_hash)
        status = "✅" if is_valid else "❌"
        print(f"   {status} '{password}': {is_valid}")
        
        if is_valid:
            print(f"\n🎉 ¡Contraseña correcta encontrada: '{password}'!")
            return password
    
    print("\n❌ Ninguna contraseña funcionó")
    return None

def fix_admin_credentials():
    """Corregir credenciales del administrador"""
    
    print("\n🔧 CORRIGIENDO CREDENCIALES DEL ADMINISTRADOR")
    print("=" * 50)
    
    # Credenciales correctas
    admin_username = "admin"
    admin_password = "PlantCare2025!"
    admin_email = "<EMAIL>"
    
    # Generar hash correcto
    password_hash = hash_password(admin_password)
    
    # Crear usuario admin correcto
    admin_user = {
        "username": admin_username,
        "display_username": admin_username,
        "email": admin_email,
        "password_hash": password_hash,
        "first_name": "Administrador",
        "last_name": "PlantCare",
        "created_at": datetime.now().isoformat(),
        "is_admin": True,
        "role": "admin"
    }
    
    # Cargar usuarios existentes o crear nuevo archivo
    try:
        with open('data/users.json', 'r', encoding='utf-8') as f:
            users = json.load(f)
    except:
        users = {}
    
    # Actualizar usuario admin
    users["1"] = admin_user
    
    # Guardar archivo
    try:
        with open('data/users.json', 'w', encoding='utf-8') as f:
            json.dump(users, f, indent=2, ensure_ascii=False)
        
        print("✅ Credenciales corregidas exitosamente!")
        
        # Verificar que funciona
        test_result = verify_password(admin_password, password_hash)
        print(f"✅ Verificación: {test_result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error guardando credenciales: {e}")
        return False

def show_credentials():
    """Mostrar credenciales finales"""
    
    print("\n📋 CREDENCIALES FINALES DEL ADMINISTRADOR")
    print("=" * 50)
    print(f"Usuario: admin")
    print(f"Contraseña: PlantCare2025!")
    print(f"Email: <EMAIL>")
    print("\n🔗 URLs para probar:")
    print(f"Login: http://127.0.0.1:5000/login")
    print(f"Panel Admin: http://127.0.0.1:5000/admin")

def main():
    """Función principal"""
    
    print("🛠️ SOLUCIONADOR DE PROBLEMAS DE LOGIN ADMIN")
    print("=" * 60)
    
    # Diagnosticar problema
    working_password = diagnose_login_issue()
    
    if working_password:
        print(f"\n✅ El sistema ya funciona con la contraseña: '{working_password}'")
        show_credentials()
    else:
        print("\n🔧 Corrigiendo credenciales...")
        if fix_admin_credentials():
            show_credentials()
            print("\n🚀 ¡Problema solucionado! Ahora puedes hacer login.")
        else:
            print("\n❌ No se pudo solucionar el problema.")

if __name__ == "__main__":
    main()
