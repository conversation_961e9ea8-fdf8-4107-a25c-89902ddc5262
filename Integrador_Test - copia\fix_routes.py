#!/usr/bin/env python3
"""
Script para corregir automáticamente las rutas problemáticas en las plantillas
"""

import os
import re

def fix_template_routes():
    """Corrige las rutas en todas las plantillas HTML"""
    
    templates_dir = "templates"
    
    # Mapeo de rutas problemáticas a rutas correctas
    route_fixes = {
        r"url_for\('auth\.login'\)": '"/login"',
        r"url_for\('auth\.register'\)": '"/register"',
        r"url_for\('auth\.logout'\)": '"/login"',
        r"url_for\('profile\.index'\)": '"/perfil"',
        r"url_for\('profile\.ajustes'\)": '"/ajustes"',
        r"url_for\('plants\.biblioteca'\)": '"/biblioteca"',
        r"url_for\('plants\.plant_detail',\s*plant_id=\d+\)": '"/biblioteca"',
        r"url_for\('diagnosis\.scanner'\)": '"/diagnosis/scanner"',
        r"url_for\('reminders\.calendario'\)": '"/calendario"',
        r"url_for\('recomendaciones\.index'\)": '"/recomendaciones"',
        r"url_for\('forum\.index'\)": '"/foro"',
        r"url_for\('settings\.index'\)": '"/ajustes"',
        r"url_for\('settings\.update_privacy'\)": '"/ajustes"',
        r"url_for\('views\.login'\)": '"/login"',
        r"url_for\('views\.register'\)": '"/register"',
        r"url_for\('home'\)": '"/"',
        r"url_for\('biblioteca'\)": '"/biblioteca"',
        r"url_for\('scanner'\)": '"/scanner"',
        r"url_for\('calendario'\)": '"/calendario"',
        r"url_for\('recomendaciones'\)": '"/recomendaciones"',
        r"url_for\('foro'\)": '"/foro"',
        r"url_for\('perfil'\)": '"/perfil"',
        r"url_for\('ajustes'\)": '"/ajustes"',
        r"url_for\('index'\)": '"/"',
    }
    
    fixed_files = []
    
    # Procesar todos los archivos HTML en el directorio templates
    for root, dirs, files in os.walk(templates_dir):
        for file in files:
            if file.endswith('.html'):
                file_path = os.path.join(root, file)
                
                try:
                    # Leer el archivo
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    original_content = content
                    changes_made = 0
                    
                    # Aplicar todas las correcciones
                    for pattern, replacement in route_fixes.items():
                        matches = re.findall(pattern, content)
                        if matches:
                            content = re.sub(pattern, replacement, content)
                            changes_made += len(matches)
                    
                    # Si se hicieron cambios, guardar el archivo
                    if changes_made > 0:
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(content)
                        
                        fixed_files.append((file_path, changes_made))
                        print(f"✅ {file_path}: {changes_made} rutas corregidas")
                
                except Exception as e:
                    print(f"❌ Error procesando {file_path}: {e}")
    
    if fixed_files:
        print(f"\n🎉 Se corrigieron rutas en {len(fixed_files)} archivos:")
        for file_path, changes in fixed_files:
            print(f"   - {file_path}: {changes} cambios")
    else:
        print("✅ No se encontraron rutas que necesiten corrección")

if __name__ == "__main__":
    print("🔧 Corrigiendo rutas en plantillas HTML...")
    fix_template_routes()
    print("✅ Proceso completado")
