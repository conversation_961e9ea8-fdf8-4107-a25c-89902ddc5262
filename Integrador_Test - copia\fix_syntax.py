#!/usr/bin/env python3
"""
Script para corregir la sintaxis incorrecta generada por el script anterior
"""

import os
import re

def fix_template_syntax():
    """Corrige la sintaxis incorrecta en las plantillas HTML"""
    
    templates_dir = "templates"
    
    # Patrones para corregir sintaxis incorrecta
    syntax_fixes = [
        # Corregir {{ "/ruta" }} a /ruta
        (r'\{\{\s*"(/[^"]*?)"\s*\}\}', r'\1'),
        # Corregir href="{{ "/ruta" }}" a href="/ruta"
        (r'href="\{\{\s*"(/[^"]*?)"\s*\}\}"', r'href="\1"'),
        # Corregir action="{{ "/ruta" }}" a action="/ruta"
        (r'action="\{\{\s*"(/[^"]*?)"\s*\}\}"', r'action="\1"'),
        # Corregir src="{{ "/ruta" }}" a src="/ruta"
        (r'src="\{\{\s*"(/[^"]*?)"\s*\}\}"', r'src="\1"'),
    ]
    
    fixed_files = []
    
    # Procesar todos los archivos HTML en el directorio templates
    for root, dirs, files in os.walk(templates_dir):
        for file in files:
            if file.endswith('.html'):
                file_path = os.path.join(root, file)
                
                try:
                    # Leer el archivo
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    original_content = content
                    changes_made = 0
                    
                    # Aplicar todas las correcciones de sintaxis
                    for pattern, replacement in syntax_fixes:
                        matches = re.findall(pattern, content)
                        if matches:
                            content = re.sub(pattern, replacement, content)
                            changes_made += len(matches)
                    
                    # Si se hicieron cambios, guardar el archivo
                    if changes_made > 0:
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(content)
                        
                        fixed_files.append((file_path, changes_made))
                        print(f"✅ {file_path}: {changes_made} sintaxis corregidas")
                
                except Exception as e:
                    print(f"❌ Error procesando {file_path}: {e}")
    
    if fixed_files:
        print(f"\n🎉 Se corrigió la sintaxis en {len(fixed_files)} archivos:")
        for file_path, changes in fixed_files:
            print(f"   - {file_path}: {changes} cambios")
    else:
        print("✅ No se encontraron problemas de sintaxis")

if __name__ == "__main__":
    print("🔧 Corrigiendo sintaxis en plantillas HTML...")
    fix_template_syntax()
    print("✅ Proceso completado")
