"""
Script para inicializar la base de datos MySQL para PlantCare.
Este script crea la base de datos y las tablas necesarias.
"""

import pymysql
import os
from config import Config

def create_database():
    """Crear la base de datos si no existe"""
    # Conectar a MySQL sin especificar una base de datos
    connection = pymysql.connect(
        host=Config.MYSQL_HOST,
        user=Config.MYSQL_USER,
        password=Config.MYSQL_PASSWORD,
        charset='utf8mb4',
        cursorclass=pymysql.cursors.DictCursor
    )
    
    try:
        with connection.cursor() as cursor:
            # Crear la base de datos si no existe
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {Config.MYSQL_DB} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print(f"Base de datos '{Config.MYSQL_DB}' creada o ya existente")
            
            # Usar la base de datos
            cursor.execute(f"USE {Config.MYSQL_DB}")
            
            # Crear tablas
            create_tables(cursor)
            
        # Confirmar cambios
        connection.commit()
        print("Todas las tablas han sido creadas correctamente")
        
    except Exception as e:
        print(f"Error al crear la base de datos: {e}")
    finally:
        connection.close()

def create_tables(cursor):
    """Crear las tablas necesarias para la aplicación"""
    
    # Tabla de Usuarios
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS Usuarios (
        UsuarioID INT AUTO_INCREMENT PRIMARY KEY,
        Nombre VARCHAR(100) NOT NULL,
        Apellido VARCHAR(100) NOT NULL,
        Email VARCHAR(100) UNIQUE NOT NULL,
        Username VARCHAR(50) UNIQUE NOT NULL,
        PasswordHash VARCHAR(255) NOT NULL,
        FechaRegistro DATETIME DEFAULT CURRENT_TIMESTAMP,
        UltimoAcceso DATETIME,
        Activo BOOLEAN DEFAULT TRUE,
        RolID INT DEFAULT 1,
        ImagenPerfil LONGBLOB
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """)
    
    # Tabla de Familias Botánicas
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS FamiliasBotanicas (
        FamiliaBotanicaID INT AUTO_INCREMENT PRIMARY KEY,
        Nombre VARCHAR(100) NOT NULL,
        Descripcion TEXT,
        Caracteristicas TEXT
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """)
    
    # Tabla de Condiciones de Luz
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS CondicionesLuz (
        CondicionLuzID INT AUTO_INCREMENT PRIMARY KEY,
        Nombre VARCHAR(50) NOT NULL,
        Descripcion TEXT
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """)
    
    # Tabla de Tipos de Suelo
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS TiposSuelo (
        TipoSueloID INT AUTO_INCREMENT PRIMARY KEY,
        Nombre VARCHAR(50) NOT NULL,
        Descripcion TEXT,
        Composicion TEXT
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """)
    
    # Tabla de Tipos de Plantas
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS TiposPlantas (
        TipoPlantaID INT AUTO_INCREMENT PRIMARY KEY,
        Nombre VARCHAR(100) NOT NULL,
        NombreCientifico VARCHAR(100),
        Descripcion TEXT,
        FamiliaBotanicaID INT,
        Origen VARCHAR(100),
        AlturaMaxima VARCHAR(50),
        TiempoCrecimiento VARCHAR(50),
        CondicionLuzID INT,
        TipoSueloID INT,
        FrecuenciaRiego VARCHAR(50),
        TemperaturaIdeal VARCHAR(50),
        HumedadIdeal VARCHAR(50),
        EsNativa BOOLEAN DEFAULT FALSE,
        EsInterior BOOLEAN DEFAULT FALSE,
        DificultadCuidado VARCHAR(20),
        ImagenURL VARCHAR(255),
        FOREIGN KEY (FamiliaBotanicaID) REFERENCES FamiliasBotanicas(FamiliaBotanicaID),
        FOREIGN KEY (CondicionLuzID) REFERENCES CondicionesLuz(CondicionLuzID),
        FOREIGN KEY (TipoSueloID) REFERENCES TiposSuelo(TipoSueloID)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """)
    
    # Tabla de Plantas (instancias de usuarios)
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS Plantas (
        PlantaID INT AUTO_INCREMENT PRIMARY KEY,
        UsuarioID INT NOT NULL,
        TipoPlantaID INT,
        Nombre VARCHAR(100) NOT NULL,
        FechaAdquisicion DATE,
        Ubicacion VARCHAR(100),
        Notas TEXT,
        EstadoSaludID INT DEFAULT 1,
        FechaActualizacion DATETIME DEFAULT CURRENT_TIMESTAMP,
        ImagenPlanta LONGBLOB,
        FOREIGN KEY (UsuarioID) REFERENCES Usuarios(UsuarioID),
        FOREIGN KEY (TipoPlantaID) REFERENCES TiposPlantas(TipoPlantaID)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """)
    
    # Tabla de Cuidados de Plantas
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS CuidadosPlantas (
        CuidadoID INT AUTO_INCREMENT PRIMARY KEY,
        PlantaID INT NOT NULL,
        TipoSueloID INT,
        CondicionLuzID INT,
        FrecuenciaRiego VARCHAR(50),
        TemperaturaIdeal VARCHAR(50),
        NotasEspeciales TEXT,
        FOREIGN KEY (PlantaID) REFERENCES Plantas(PlantaID),
        FOREIGN KEY (TipoSueloID) REFERENCES TiposSuelo(TipoSueloID),
        FOREIGN KEY (CondicionLuzID) REFERENCES CondicionesLuz(CondicionLuzID)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """)
    
    # Tabla de Tipos de Recordatorios
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS TiposRecordatorios (
        TipoRecordatorioID INT AUTO_INCREMENT PRIMARY KEY,
        Nombre VARCHAR(50) NOT NULL,
        Descripcion TEXT
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """)
    
    # Tabla de Recordatorios
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS Recordatorios (
        RecordatorioID INT AUTO_INCREMENT PRIMARY KEY,
        UsuarioID INT NOT NULL,
        PlantaID INT,
        TipoRecordatorioID INT NOT NULL,
        Titulo VARCHAR(100) NOT NULL,
        Descripcion TEXT,
        FechaHora DATETIME NOT NULL,
        Recurrencia VARCHAR(50),
        Completado BOOLEAN DEFAULT FALSE,
        NotificacionEnviada BOOLEAN DEFAULT FALSE,
        FOREIGN KEY (UsuarioID) REFERENCES Usuarios(UsuarioID),
        FOREIGN KEY (PlantaID) REFERENCES Plantas(PlantaID),
        FOREIGN KEY (TipoRecordatorioID) REFERENCES TiposRecordatorios(TipoRecordatorioID)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """)
    
    print("Tablas creadas correctamente")

if __name__ == "__main__":
    create_database()
    print("Inicialización de la base de datos MySQL completada")
