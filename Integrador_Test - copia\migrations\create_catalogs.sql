-- Crear tabla CatTipoPlanta si no existe
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CatTipoPlanta]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[CatTipoPlanta](
        [TipoPlantaID] [int] IDENTITY(1,1) PRIMARY KEY,
        [Nombre] [nvarchar](100) NOT NULL,
        [Descripcion] [ntext] NULL,
        [RequerimientosSol] [nvarchar](50) NULL,
        [RequerimientosAgua] [nvarchar](50) NULL,
        [RequerimientosSuelo] [nvarchar](50) NULL
    )
END;

-- Crear tabla CatFamiliaBotanica si no existe
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CatFamiliaBotanica]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[CatFamiliaBotanica](
        [FamiliaID] [int] IDENTITY(1,1) PRIMARY KEY,
        [Nombre] [nvarchar](100) NOT NULL,
        [NombreCientifico] [nvarchar](150) NULL,
        [Descripcion] [ntext] NULL,
        [CaracteristicasComunes] [ntext] NULL
    )
END;

-- Crear tabla CatEstadoSalud si no existe
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CatEstadoSalud]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[CatEstadoSalud](
        [EstadoSaludID] [int] IDENTITY(1,1) PRIMARY KEY,
        [Descripcion] [nvarchar](100) NOT NULL,
        [ColorRepresentativo] [nvarchar](10) NULL,
        [NivelPrioridad] [int] NULL
    )
END;

