-- Crear tabla ConfiguracionesUsuario si no existe
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[ConfiguracionesUsuario]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[ConfiguracionesUsuario](
        [ConfiguracionID] [int] IDENTITY(1,1) PRIMARY KEY,
        [UsuarioID] [int] NOT NULL,
        [NotificacionesEmail] [bit] DEFAULT 1,
        [NotificacionesPush] [bit] DEFAULT 1,
        [IdiomaPreferido] [nvarchar](10) DEFAULT 'es-mx',
        [ModoOscuro] [bit] DEFAULT 0,
        [FrecuenciaNotificaciones] [nvarchar](20) DEFAULT 'Diaria',
        CONSTRAINT [FK_ConfiguracionesUsuario_Usuarios] FOREIGN KEY([UsuarioID])
            REFERENCES [dbo].[Usuarios] ([UsuarioID])
    )
END