-- Crear tabla CuidadosPlantas si no existe
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CuidadosPlantas]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[CuidadosPlantas](
        [CuidadoID] [int] IDENTITY(1,1) PRIMARY KEY,
        [PlantaID] [int] NOT NULL,
        [TipoSueloID] [int] NULL,
        [CondicionLuzID] [int] NULL,
        [FrecuenciaRiego] [nvarchar](100) NULL,
        [UltimoRiego] [datetime] NULL,
        [ProximoRiego] [datetime] NULL,
        [TemperaturaIdeal] [nvarchar](50) NULL,
        [NotasEspeciales] [ntext] NULL,
        CONSTRAINT [FK_CuidadosPlantas_PlantasUsuario] FOREIGN KEY([PlantaID])
            REFERENCES [dbo].[PlantasUsuario] ([PlantaID]),
        CONSTRAINT [FK_CuidadosPlantas_CatTipoSuelo] FOREIGN KEY([TipoSueloID])
            REFERENCES [dbo].[CatTipoSuelo] ([TipoSueloID]),
        CONSTRAINT [FK_CuidadosPlantas_CatCondicionLuz] FOREIGN KEY([CondicionLuzID])
            REFERENCES [dbo].[CatCondicionLuz] ([CondicionLuzID])
    )
END