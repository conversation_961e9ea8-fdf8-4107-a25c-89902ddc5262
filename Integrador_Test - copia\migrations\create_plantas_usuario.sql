IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[PlantasUsuario]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[PlantasUsuario](
        [PlantaID] [int] IDENTITY(1,1) PRIMARY KEY,
        [UsuarioID] [int] NOT NULL,
        [NombrePlanta] [nvarchar](100) NOT NULL,
        [TipoPlantaID] [int] NULL,
        [Ubicacion] [nvarchar](100) NULL,
        [EdadAproximada] [nvarchar](50) NULL,
        [TamanoActual] [nvarchar](50) NULL,
        [FechaAdquisicion] [datetime] NULL,
        [EstadoSaludID] [int] DEFAULT 1,
        [FamiliaID] [int] NULL,
        [ImagenPlanta] [varbinary](max) NULL,
        [FechaActualizacion] [datetime] DEFAULT GETDATE(),
        CONSTRAINT [FK_PlantasUsuario_Usuarios] FOREIGN KEY([UsuarioID])
            REFERENCES [dbo].[Usuarios] ([UsuarioID]),
        CONSTRAINT [FK_PlantasUsuario_CatTipoPlanta] FOREIGN KEY([TipoPlantaID])
            REFERENCES [dbo].[CatTipoPlanta] ([TipoPlantaID]),
        CONSTRAINT [FK_PlantasUsuario_CatEstadoSalud] FOREIGN KEY([EstadoSaludID])
            REFERENCES [dbo].[CatEstadoSalud] ([EstadoSaludID]),
        CONSTRAINT [FK_PlantasUsuario_CatFamiliaBotanica] FOREIGN KEY([FamiliaID])
            REFERENCES [dbo].[CatFamiliaBotanica] ([FamiliaID])
    )
END