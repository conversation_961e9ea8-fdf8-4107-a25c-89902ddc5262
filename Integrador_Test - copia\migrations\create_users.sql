-- Crear tabla CatTipoUsuario si no existe
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CatTipoUsuario]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[CatTipoUsuario](
        [TipoUsuarioID] [int] IDENTITY(1,1) PRIMARY KEY,
        [Descripcion] [nvarchar](50) NOT NULL
    )
END

-- Crear tabla Usuarios si no existe
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Usuarios]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Usuarios](
        [UsuarioID] [int] IDENTITY(1,1) PRIMARY KEY,
        [Nombre] [nvarchar](100) NOT NULL,
        [Apellidos] [nvarchar](100) NOT NULL,
        [Email] [nvarchar](100) NOT NULL UNIQUE,
        [<PERSON><PERSON>ena] [nvarchar](255) NOT NULL,
        [FechaRegistro] [datetime] DEFAULT GETDATE(),
        [UltimoAcceso] [datetime] NULL,
        [Municipio] [nvarchar](100) NULL,
        [Direccion] [nvarchar](255) NULL,
        [Telefono] [nvarchar](20) NULL,
        [TipoUsuarioID] [int] NULL,
        CONSTRAINT [FK_Usuarios_CatTipoUsuario] FOREIGN KEY([TipoUsuarioID])
            REFERENCES [dbo].[CatTipoUsuario] ([TipoUsuarioID])
    )
END