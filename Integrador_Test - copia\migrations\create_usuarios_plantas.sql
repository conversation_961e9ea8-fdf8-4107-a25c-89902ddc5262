-- Crear tabla UsuariosPlantasFavoritas si no existe
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[UsuariosPlantasFavoritas]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[UsuariosPlantasFavoritas](
        [id] [int] IDENTITY(1,1) PRIMARY KEY,
        [user_id] [int] NOT NULL,
        [plant_id] [int] NOT NULL,
        [saved] [bit] DEFAULT 1,
        CONSTRAINT [FK_UsuariosPlantasFavoritas_Usuarios] FOREIGN KEY([user_id])
            REFERENCES [dbo].[Us<PERSON><PERSON>s] ([UsuarioID]),
        CONSTRAINT [FK_UsuariosPlantasFavoritas_CatTipoPlanta] FOREIGN KEY([plant_id])
            REFERENCES [dbo].[CatTipoPlanta] ([TipoPlantaID]),
        CONSTRAINT [unique_user_plant] UNIQUE ([user_id], [plant_id])
    )
END