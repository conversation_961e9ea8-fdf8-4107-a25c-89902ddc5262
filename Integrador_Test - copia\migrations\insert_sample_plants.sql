-- Script para insertar plantas de ejemplo para usuarios
-- Este script inserta plantas de ejemplo para el usuario con ID 1 (asumiendo que existe)

-- Verificar si ya existen plantas para el usuario
IF NOT EXISTS (SELECT 1 FROM PlantasUsuario WHERE UsuarioID = 1)
BEGIN
    -- Insertar plantas de ejemplo para el usuario 1
    INSERT INTO PlantasUsuario (
        UsuarioID, 
        NombrePlanta, 
        TipoPlantaID, 
        Ubicacion, 
        EdadAproximada, 
        TamanoActual, 
        FechaAdquisicion, 
        EstadoSaludID, 
        FamiliaID
    )
    VALUES 
        (1, 'Mi Agave', 1, 'Jardín frontal', '2 años', '50 cm', GETDATE()-180, 1, NULL),
        (1, 'Nopal de la terraza', 1, 'Terraza', '1 año', '30 cm', GETDATE()-90, 1, NULL),
        (1, 'Yucca del patio', 1, '<PERSON>io trasero', '3 años', '1.2 m', GETDATE()-365, 1, NULL),
        (1, '<PERSON>iznaga pequeña', 1, '<PERSON><PERSON><PERSON> cocina', '6 meses', '10 cm', GETDATE()-30, 1, NULL),
        (1, 'Gobernadora', 2, 'Jardín lateral', '1 año', '40 cm', GETDATE()-120, 1, NULL);
    
    PRINT 'Plantas de ejemplo insertadas para el usuario 1'
END
ELSE
BEGIN
    PRINT 'El usuario 1 ya tiene plantas registradas'
END
