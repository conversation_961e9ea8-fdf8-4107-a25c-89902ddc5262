-- Actualizar tabla CuidadosPlantas para añadir campos de abono
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CuidadosPlantas]') AND type in (N'U'))
BEGIN
    -- Verificar si la columna UltimoAbono ya existe
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[CuidadosPlantas]') AND name = 'UltimoAbono')
    BEGIN
        -- Añadir columna UltimoAbono
        ALTER TABLE [dbo].[CuidadosPlantas] ADD [UltimoAbono] [datetime] NULL;
        PRINT 'Columna UltimoAbono añadida a la tabla CuidadosPlantas';
    END
    ELSE
    BEGIN
        PRINT 'La columna UltimoAbono ya existe en la tabla CuidadosPlantas';
    END

    -- Verificar si la columna FrecuenciaAbono ya existe
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[CuidadosPlantas]') AND name = 'FrecuenciaAbono')
    BEGIN
        -- Añadir columna FrecuenciaAbono
        ALTER TABLE [dbo].[CuidadosPlantas] ADD [FrecuenciaAbono] [nvarchar](100) NULL;
        PRINT 'Columna FrecuenciaAbono añadida a la tabla CuidadosPlantas';
    END
    ELSE
    BEGIN
        PRINT 'La columna FrecuenciaAbono ya existe en la tabla CuidadosPlantas';
    END
END
ELSE
BEGIN
    PRINT 'La tabla CuidadosPlantas no existe';
END
