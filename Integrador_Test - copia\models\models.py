from database import db, using_json_db

# Si estamos usando JSON, importar los modelos JSON
if using_json_db:
    try:
        from database_json import (
            <PERSON><PERSON><PERSON><PERSON> as User,
            PlantJson as Plant,
            PlantTypeJson as PlantType,
            PlantFamilyJson as PlantFamily,
            HealthStatusJson as HealthStatus,
            PlantCareJson as PlantCare,
            SoilTypeJson as SoilType,
            LightConditionJson as LightCondition,
            <PERSON><PERSON><PERSON><PERSON><PERSON> as Reminder,
            <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as ReminderType,
            <PERSON>ag<PERSON><PERSON><PERSON> as Diagnosis,
            DiseaseJson as Disease,
            ForumTopicJson as ForumTopic,
            ForumReplyJson as ForumReply,
            EventJson as Event,
            EventRegistrationJson as EventRegistration,
            StoreJson as Store
        )
    except ImportError as e:
        print(f"Error al importar modelos JSON: {str(e)}")
else:
    # Definir modelos SQLAlchemy normales
    class User(db.Model):
        __tablename__ = 'Usuarios'
        
        UsuarioID = db.Column(db.Integer, primary_key=True)
        Nombre = db.Column(db.String(100), nullable=False)
        Apellido = db.Column(db.String(100), nullable=False)
        Email = db.Column(db.String(100), unique=True, nullable=False)
        Username = db.Column(db.String(50), unique=True, nullable=False)
        PasswordHash = db.Column(db.String(255), nullable=False)
        FechaRegistro = db.Column(db.DateTime, default=db.func.current_timestamp())
        UltimoAcceso = db.Column(db.DateTime)
        Activo = db.Column(db.Boolean, default=True)
        RolID = db.Column(db.Integer, default=1)
        ImagenPerfil = db.Column(db.LargeBinary)
        
        # Relaciones
        plantas = db.relationship('Plant', backref='usuario', lazy=True)
        
        def __repr__(self):
            return f'<Usuario {self.Username}>'
    
    eventos = relationship("Event", back_populates="tienda")
