import os
import json
import sqlite3
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PlantRecommendationSystem:
    """Comprehensive plant disease recommendation system"""
    
    def __init__(self, storage_type="json", db_path="models/recommendations.db", json_path="models/recommendations.json"):
        """
        Initialize the recommendation system with configurable storage options
        
        Args:
            storage_type: Type of storage to use ("json" or "sqlite")
            db_path: Path to SQLite database file if using sqlite storage
            json_path: Path to JSON file if using json storage
        """
        self.storage_type = storage_type.lower()
        self.db_path = db_path
        self.json_path = json_path
        
        # Initialize the appropriate storage system
        if self.storage_type == "sqlite":
            self._initialize_database()
        elif self.storage_type == "json":
            self._ensure_json_exists()
        else:
            raise ValueError(f"Unsupported storage type: {storage_type}. Use 'json' or 'sqlite'.")
    
    def _initialize_database(self):
        """Create and initialize the SQLite database if it doesn't exist"""
        try:
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create the recommendations table if it doesn't exist
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS recommendations (
                    plant_disease TEXT PRIMARY KEY,
                    description TEXT,
                    symptoms TEXT,
                    care_instructions TEXT,
                    treatment_protocol TEXT,
                    regional_considerations TEXT,
                    prevention_tips TEXT,
                    severity INTEGER,
                    recovery_timeline TEXT,
                    additional_resources TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            logger.info(f"Database initialized at {self.db_path}")
            
        except Exception as e:
            logger.error(f"Failed to initialize database: {str(e)}")
            raise
    
    def _ensure_json_exists(self):
        """Ensure that the JSON recommendations file exists"""
        try:
            os.makedirs(os.path.dirname(self.json_path), exist_ok=True)
            
            if not os.path.exists(self.json_path):
                # Create an empty recommendations file
                with open(self.json_path, 'w', encoding='utf-8') as f:
                    json.dump({}, f, ensure_ascii=False, indent=2)
                logger.info(f"Created empty recommendations file at {self.json_path}")
            else:
                logger.info(f"Using existing recommendations file at {self.json_path}")
                
        except Exception as e:
            logger.error(f"Failed to ensure JSON file exists: {str(e)}")
            raise
    
    def get_recommendations(self, plant_disease_name):
        """
        Retrieve comprehensive recommendations for a specific plant disease
        
        Args:
            plant_disease_name: Name of the plant disease combination
            
        Returns:
            dict: Complete recommendation data for the plant disease
        """
        try:
            if self.storage_type == "sqlite":
                return self._get_from_sqlite(plant_disease_name)
            else:  # json storage
                return self._get_from_json(plant_disease_name)
                
        except Exception as e:
            logger.error(f"Error retrieving recommendations for {plant_disease_name}: {str(e)}")
            return self._get_default_recommendation(plant_disease_name)
    
    def _get_from_sqlite(self, plant_disease_name):
        """Retrieve recommendations from SQLite database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(
            "SELECT * FROM recommendations WHERE plant_disease = ?", 
            (plant_disease_name,)
        )
        
        row = cursor.fetchone()
        conn.close()
        
        if row:
            columns = [
                'plant_disease', 'description', 'symptoms', 'care_instructions',
                'treatment_protocol', 'regional_considerations', 'prevention_tips',
                'severity', 'recovery_timeline', 'additional_resources'
            ]
            return dict(zip(columns, row))
        else:
            return self._get_default_recommendation(plant_disease_name)
    
    def _get_from_json(self, plant_disease_name):
        """Retrieve recommendations from JSON file"""
        with open(self.json_path, 'r', encoding='utf-8') as f:
            recommendations = json.load(f)
        
        if plant_disease_name in recommendations:
            return recommendations[plant_disease_name]
        else:
            return self._get_default_recommendation(plant_disease_name)
    
    def _get_default_recommendation(self, plant_disease_name):
        """Generate a default recommendation when specific data is not available"""
        parts = plant_disease_name.split()
        
        if len(parts) > 1:
            plant_name = parts[0]
            condition = ' '.join(parts[1:])
        else:
            plant_name = plant_disease_name
            condition = "desconocida"
        
        is_healthy = "Sana" in plant_disease_name or "Sano" in plant_disease_name
        
        if is_healthy:
            return {
                "plant_disease": plant_disease_name,
                "description": f"Planta {plant_name} en estado saludable.",
                "symptoms": "No presenta síntomas de enfermedad. Hojas, tallos y raíces en buen estado.",
                "care_instructions": f"Continúe con el régimen normal de cuidado para {plant_name}. Riegue adecuadamente según las necesidades específicas de la planta.",
                "treatment_protocol": "No se requiere tratamiento específico.",
                "regional_considerations": "Mantenga prácticas de cuidado apropiadas para el clima regional y la temporada actual.",
                "prevention_tips": "Mantenga buena circulación de aire, evite exceso de humedad y controle plagas regularmente.",
                "severity": 0,
                "recovery_timeline": "N/A",
                "additional_resources": "Consulte manuales de cuidado específicos para esta planta."
            }
        else:
            return {
                "plant_disease": plant_disease_name,
                "description": f"Condición identificada: {condition} en planta {plant_name}.",
                "symptoms": f"La planta muestra síntomas consistentes con {condition}.",
                "care_instructions": "Aisle la planta de otros ejemplares para prevenir propagación. Evite riego excesivo y mejore la ventilación.",
                "treatment_protocol": "Se recomienda consultar con un especialista en fitopatología para un diagnóstico preciso y tratamiento específico.",
                "regional_considerations": "Las condiciones regionales pueden afectar la progresión y tratamiento de esta enfermedad.",
                "prevention_tips": "Inspeccione regularmente las plantas, mantenga herramientas de jardinería limpias y desinfectadas.",
                "severity": 3,
                "recovery_timeline": "Variable dependiendo de la gravedad y el tratamiento oportuno.",
                "additional_resources": "Contacte a su servicio de extensión agrícola local para información específica."
            }
    
    def add_recommendation(self, recommendation_data):
        """
        Add or update a plant disease recommendation
        
        Args:
            recommendation_data: Dictionary containing recommendation information
            
        Returns:
            bool: Success or failure
        """
        try:
            if "plant_disease" not in recommendation_data:
                logger.error("Missing required 'plant_disease' field in recommendation data")
                return False
            
            plant_disease_name = recommendation_data["plant_disease"]
            
            if self.storage_type == "sqlite":
                return self._add_to_sqlite(plant_disease_name, recommendation_data)
            else:  # json storage
                return self._add_to_json(plant_disease_name, recommendation_data)
                
        except Exception as e:
            logger.error(f"Error adding recommendation: {str(e)}")
            return False
    
    def _add_to_sqlite(self, plant_disease_name, data):
        """Add or update recommendation in SQLite database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Check if entry already exists
        cursor.execute(
            "SELECT COUNT(*) FROM recommendations WHERE plant_disease = ?", 
            (plant_disease_name,)
        )
        
        exists = cursor.fetchone()[0] > 0
        
        if exists:
            # Update existing record
            columns = []
            values = []
            
            for key, value in data.items():
                if key != "plant_disease":  # Skip primary key in SET clause
                    columns.append(f"{key} = ?")
                    values.append(value)
            
            values.append(plant_disease_name)  # For WHERE clause
            
            query = f"UPDATE recommendations SET {', '.join(columns)} WHERE plant_disease = ?"
            cursor.execute(query, values)
        else:
            # Insert new record
            columns = []
            placeholders = []
            values = []
            
            for key, value in data.items():
                columns.append(key)
                placeholders.append('?')
                values.append(value)
            
            query = f"INSERT INTO recommendations ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
            cursor.execute(query, values)
        
        conn.commit()
        conn.close()
        return True
    
    def _add_to_json(self, plant_disease_name, data):
        """Add or update recommendation in JSON file"""
        with open(self.json_path, 'r', encoding='utf-8') as f:
            recommendations = json.load(f)
        
        recommendations[plant_disease_name] = data
        
        with open(self.json_path, 'w', encoding='utf-8') as f:
            json.dump(recommendations, f, ensure_ascii=False, indent=2)
        
        return True
    
    def bulk_import_recommendations(self, recommendations_data):
        """
        Import multiple recommendations at once
        
        Args:
            recommendations_data: Dictionary of plant disease recommendations
            
        Returns:
            int: Number of recommendations successfully imported
        """
        success_count = 0
        
        for plant_disease_name, data in recommendations_data.items():
            if isinstance(data, dict):
                # Ensure plant_disease field is set
                data["plant_disease"] = plant_disease_name
                
                if self.add_recommendation(data):
                    success_count += 1
                else:
                    logger.warning(f"Failed to import recommendation for {plant_disease_name}")
            else:
                logger.warning(f"Invalid data format for {plant_disease_name}")
        
        return success_count
    
    def get_all_recommendations(self):
        """
        Retrieve all available recommendations
        
        Returns:
            dict: All recommendation data
        """
        try:
            if self.storage_type == "sqlite":
                conn = sqlite3.connect(self.db_path)
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute("SELECT * FROM recommendations")
                rows = cursor.fetchall()
                conn.close()
                
                recommendations = {}
                for row in rows:
                    row_dict = dict(row)
                    plant_disease = row_dict.pop("plant_disease")
                    recommendations[plant_disease] = row_dict
                
                return recommendations
            else:  # json storage
                with open(self.json_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
                    
        except Exception as e:
            logger.error(f"Error retrieving all recommendations: {str(e)}")
            return {}


# Function to use in the AI service
def get_recommendations(plant_disease_name, storage_type="json", db_path="models/recommendations.db", json_path="models/recommendations.json"):
    """
    Get detailed recommendations for plant disease diagnosis
    
    Args:
        plant_disease_name: The name of the plant disease combination
        storage_type: Type of storage ("json" or "sqlite")
        db_path: Path to SQLite database if using sqlite storage
        json_path: Path to JSON file if using json storage
        
    Returns:
        str: Formatted recommendation text
    """
    # Initialize the recommendation system
    recommendation_system = PlantRecommendationSystem(
        storage_type=storage_type,
        db_path=db_path,
        json_path=json_path
    )
    
    # Get recommendation data
    recommendation = recommendation_system.get_recommendations(plant_disease_name)
    
    # Format the recommendation text
    formatted_text = f"""
DIAGNÓSTICO: {recommendation['plant_disease']}

DESCRIPCIÓN:
{recommendation['description']}

SÍNTOMAS:
{recommendation['symptoms']}

INSTRUCCIONES DE CUIDADO:
{recommendation['care_instructions']}

PROTOCOLO DE TRATAMIENTO:
{recommendation['treatment_protocol']}

CONSIDERACIONES REGIONALES:
{recommendation['regional_considerations']}

PREVENCIÓN:
{recommendation['prevention_tips']}

SEVERIDAD: {recommendation['severity']}/5

TIEMPO DE RECUPERACIÓN ESPERADO:
{recommendation['recovery_timeline']}

RECURSOS ADICIONALES:
{recommendation['additional_resources']}
"""
    
    return formatted_text.strip()


# Example of creating a recommendations database with sample data
def create_sample_recommendations_database():
    """Create a sample recommendations database with data for example plant diseases"""
    recommendation_system = PlantRecommendationSystem(storage_type="json")
    
    # Sample recommendations for demonstration
    sample_data = {
        "Yuca Sana": {
            "plant_disease": "Yuca Sana",
            "description": "Planta de yuca en condición saludable sin signos de enfermedad o estrés.",
            "symptoms": "Follaje verde brillante, tallos firmes y erguidos, crecimiento consistente. Ausencia de manchas, decoloraciones o malformaciones.",
            "care_instructions": "Riego moderado cada 7-10 días, permitiendo que el suelo se seque entre riegos. Fertilización mensual con fórmula baja en nitrógeno durante la temporada de crecimiento.",
            "treatment_protocol": "No requiere tratamiento. Mantenga el régimen regular de cuidados.",
            "regional_considerations": "En regiones con alta radiación solar, proporcione sombra parcial durante las horas más calurosas. En zonas áridas, incremente la frecuencia de riego durante los meses de verano.",
            "prevention_tips": "Inspección regular para detectar plagas. Rotación de cultivos cada 2-3 años. Mantener buena circulación de aire entre plantas.",
            "severity": 0,
            "recovery_timeline": "N/A",
            "additional_resources": "Manual de Cultivo de Yuca en Zonas Áridas (INIFAP, 2022)"
        },
        "Yuca Pudrición Radicular": {
            "plant_disease": "Yuca Pudrición Radicular",
            "description": "Enfermedad fúngica que afecta el sistema radicular de la planta de yuca, causada principalmente por Phytophthora spp., Pythium spp. o Fusarium spp.",
            "symptoms": "Amarillamiento progresivo del follaje, marchitez a pesar de riego adecuado, crecimiento atrofiado, raíces con áreas necróticas marrones o negras, olor a descomposición en raíces afectadas.",
            "care_instructions": "Retire la planta del suelo y elimine todas las raíces afectadas con herramientas desinfectadas. Reduzca el riego para evitar exceso de humedad. Trasplante a un nuevo sustrato estéril si es posible recuperar la planta.",
            "treatment_protocol": "Aplique fungicida sistémico específico para oomicetos (en caso de Phytophthora o Pythium) o benomilo (para Fusarium). Realice tratamiento al suelo con caldo bordelés. En casos avanzados, considere eliminar la planta para prevenir propagación.",
            "regional_considerations": "En zonas con temporada de lluvias intensa, plante en camas elevadas con buen drenaje. En zonas áridas, evite ciclos de sequía-sobreriego que estresan la planta y la hacen susceptible.",
            "prevention_tips": "Use sustrato bien drenado. Evite el exceso de riego. Desinfecte herramientas de jardinería. Rote cultivos. Use variedades resistentes como 'Valencia' o 'MCOL 1684'.",
            "severity": 4,
            "recovery_timeline": "2-3 meses con tratamiento oportuno. Casos avanzados pueden ser irrecuperables.",
            "additional_resources": "Contacte al servicio de extensión agrícola de SAGARPA para análisis de suelo y recomendaciones específicas."
        },
        "Nopal Cochinilla": {
            "plant_disease": "Nopal Cochinilla",
            "description": "Infestación por insectos escama (Dactylopius coccus o D. opuntiae) que se alimentan de la savia del nopal.",
            "symptoms": "Presencia de masas algodonosas blancas en las pencas. Secreciones cerosas blancas. Clorosis y debilitamiento de pencas afectadas. En casos severos, deformación y caída de cladodios.",
            "care_instructions": "Aísle las plantas afectadas. Limpie manualmente las cochinillas con un cepillo suave y agua jabonosa. Pode y destruya las partes severamente infestadas.",
            "treatment_protocol": "Para infestaciones leves: Aplique solución de agua jabonosa (5ml jabón biodegradable/litro) cada 7 días. Para infestaciones moderadas: Aplique aceite hortícola (20ml/litro) cubriendo completamente las colonias. Para infestaciones severas: Utilice insecticida sistémico específico como imidacloprid, respetando tiempos de seguridad si es para consumo.",
            "regional_considerations": "En zonas secas del norte de México, las infestaciones son más comunes durante la primavera y el otoño. En zonas con mayor humedad, monitoree después de lluvias cuando aumenta la población.",
            "prevention_tips": "Inspeccione regularmente las plantas. Mantenga buena ventilación entre ejemplares. Introduzca depredadores naturales como mariquitas o crisopas. Evite el exceso de fertilización nitrogenada.",
            "severity": 3,
            "recovery_timeline": "2-4 semanas con tratamiento adecuado. Monitoree continuamente para prevenir reinfestaciones.",
            "additional_resources": "Guía MIP para Nopal (INIFAP, 2023). Contacte al Comité Estatal de Sanidad Vegetal más cercano."
        },
        "Mezquite Roya": {
            "plant_disease": "Mezquite Roya",
            "description": "Enfermedad fúngica causada por Ravenelia spp. que afecta principalmente al follaje del mezquite.",
            "symptoms": "Pústulas de color rojizo-anaranjado en el envés de las hojas. Amarillamiento y caída prematura del follaje. Reducción del vigor general del árbol. En casos severos, defoliación significativa.",
            "care_instructions": "Mejore la ventilación podando ramas interiores para reducir la humedad. Recoja y destruya hojas caídas para reducir el inóculo. Aumente el espacio entre árboles si es posible.",
            "treatment_protocol": "Aplique fungicida a base de cobre (oxicloruro de cobre 2g/litro) en etapas tempranas. Para casos avanzados, alterne con fungicidas sistémicos como triazoles, aplicando en intervalos de 14 días durante la temporada de lluvias.",
            "regional_considerations": "En zonas del desierto de Sonora y Chihuahua, la enfermedad es más prevalente durante la temporada de monzones (julio-septiembre). Planifique tratamientos preventivos antes de las lluvias.",
            "prevention_tips": "Evite riego por aspersión. Mantenga una nutrición balanceada. Pode y destruya ramas infectadas antes de la temporada de lluvias. Seleccione variedades resistentes como Prosopis glandulosa var. torreyana.",
            "severity": 3,
            "recovery_timeline": "El árbol generalmente se recupera en el siguiente ciclo vegetativo con manejo adecuado. La eliminación de la roya activa requiere 3-4 semanas de tratamiento.",
            "additional_resources": "Manual de Manejo Fitosanitario de Especies Forestales en Zonas Áridas (CONAFOR, 2021)."
        },
        "Agave Antracnosis": {
            "plant_disease": "Agave Antracnosis",
            "description": "Enfermedad fúngica causada por especies de Colletotrichum que afecta principalmente a las hojas de agave.",
            "symptoms": "Lesiones circulares hundidas de color marrón a negro con bordes amarillentos. Manchas necróticas que se expanden y fusionan. En el centro de las lesiones pueden aparecer puntos negros (acérvulos). En casos avanzados, pudrición de las hojas afectadas.",
            "care_instructions": "Retire y destruya todas las hojas severamente afectadas. Reduzca el riego aéreo. Mejore la circulación de aire entre plantas. Evite regar el follaje, especialmente en la tarde.",
            "treatment_protocol": "Aplique fungicida a base de cobre (oxicloruro de cobre 3g/litro) como tratamiento inicial. Alterne con fungicidas sistémicos como azoxystrobin en casos moderados a severos. Realice 3-4 aplicaciones en intervalos de 10-14 días.",
            "regional_considerations": "En regiones con alta humedad como Veracruz o Tabasco, la enfermedad es más agresiva y requiere monitoreo constante durante la temporada de lluvia. En zonas áridas, es menos frecuente pero puede aparecer con irrigación excesiva o lluvias ocasionales.",
            "prevention_tips": "Plante en suelo bien drenado. Evite lesiones mecánicas en las hojas. Utilice espaciamiento adecuado entre plantas. Aplique fungicidas preventivos antes de la temporada de lluvias. Seleccione variedades menos susceptibles como A. tequilana 'Weber Azul'.",
            "severity": 3,
            "recovery_timeline": "Con tratamiento oportuno, se controla en 3-4 semanas. Las hojas dañadas no se recuperarán, pero la planta producirá nuevo crecimiento sano.",
            "additional_resources": "Guía Técnica para el Manejo de Agaves (INIFAP, 2020). Consulte al Consejo Regulador del Tequila para variedades resistentes."
        }
    }
    
    # Import the sample data
    recommendation_system.bulk_import_recommendations(sample_data)
    print(f"Created sample recommendations database with {len(sample_data)} entries")

# If running this file directly, create a sample database
if __name__ == "__main__":
    create_sample_recommendations_database()