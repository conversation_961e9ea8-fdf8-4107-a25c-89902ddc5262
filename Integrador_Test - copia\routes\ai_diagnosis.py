"""
Rutas para el sistema de diagnóstico de plantas con IA
"""

from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify
import os
import requests
import base64
from werkzeug.utils import secure_filename
from PIL import Image
import io
import tempfile

diagnosis_bp = Blueprint('diagnosis', __name__, url_prefix='/diagnosis')

# Configuración
UPLOAD_FOLDER = 'static/uploads/diagnosis'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
MAX_FILE_SIZE = 16 * 1024 * 1024  # 16MB
DIAGNOSIS_API_URL = "http://127.0.0.1:8000"  # URL de la API de diagnóstico

def allowed_file(filename):
    """Verificar si el archivo es permitido"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def resize_image(image_path, max_size=(800, 800)):
    """Redimensionar imagen para optimizar el procesamiento"""
    try:
        with Image.open(image_path) as img:
            img.thumbnail(max_size, Image.Resampling.LANCZOS)
            img.save(image_path, optimize=True, quality=85)
        return True
    except Exception as e:
        print(f"Error redimensionando imagen: {e}")
        return False

def call_diagnosis_api(image_path):
    """Llamar a la API de diagnóstico"""
    try:
        # Verificar si la API está disponible
        health_response = requests.get(f"{DIAGNOSIS_API_URL}/health", timeout=5)
        if health_response.status_code != 200:
            return {"error": "API de diagnóstico no disponible"}
        
        # Enviar imagen para diagnóstico
        with open(image_path, 'rb') as f:
            files = {'file': f}
            response = requests.post(f"{DIAGNOSIS_API_URL}/diagnose", files=files, timeout=30)
        
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"Error en API: {response.status_code}"}
            
    except requests.exceptions.RequestException as e:
        # Si la API no está disponible, usar diagnóstico local
        return call_local_diagnosis(image_path)
    except Exception as e:
        return {"error": f"Error llamando API: {str(e)}"}

def call_local_diagnosis(image_path):
    """Diagnóstico local como fallback"""
    try:
        # Importar el sistema local
        import sys
        sys.path.append('ai_diagnosis')
        from plant_disease_model import diagnose_plant_image
        
        return diagnose_plant_image(image_path)
    except Exception as e:
        return {
            "error": "Sistema de diagnóstico no disponible",
            "plant_name": "Desconocida",
            "condition": "No se pudo analizar",
            "confidence": 0.0,
            "diagnosis": "El sistema de IA no está disponible en este momento. Por favor, consulta con un especialista.",
            "recommendations": [
                "Verifica que la imagen sea clara y bien iluminada",
                "Asegúrate de que la planta sea visible en la imagen",
                "Consulta con un especialista en plantas"
            ]
        }

@diagnosis_bp.route('/')
def index():
    """Página principal de diagnóstico"""
    return render_template('diagnosis/index.html')

@diagnosis_bp.route('/upload', methods=['GET', 'POST'])
def upload_image():
    """Subir imagen para diagnóstico"""
    if request.method == 'POST':
        # Verificar si se subió un archivo
        if 'file' not in request.files:
            flash('No se seleccionó ningún archivo', 'danger')
            return redirect(request.url)
        
        file = request.files['file']
        
        if file.filename == '':
            flash('No se seleccionó ningún archivo', 'danger')
            return redirect(request.url)
        
        if file and allowed_file(file.filename):
            try:
                # Crear directorio si no existe
                os.makedirs(UPLOAD_FOLDER, exist_ok=True)
                
                # Guardar archivo
                filename = secure_filename(file.filename)
                file_path = os.path.join(UPLOAD_FOLDER, filename)
                file.save(file_path)
                
                # Verificar tamaño del archivo
                if os.path.getsize(file_path) > MAX_FILE_SIZE:
                    os.remove(file_path)
                    flash('El archivo es demasiado grande (máximo 16MB)', 'danger')
                    return redirect(request.url)
                
                # Redimensionar imagen
                if not resize_image(file_path):
                    flash('Error procesando la imagen', 'danger')
                    return redirect(request.url)
                
                # Realizar diagnóstico
                diagnosis_result = call_diagnosis_api(file_path)
                
                # Limpiar archivo temporal (opcional)
                # os.remove(file_path)
                
                return render_template('diagnosis/result.html', 
                                     result=diagnosis_result, 
                                     image_path=file_path.replace('static/', ''))
                
            except Exception as e:
                flash(f'Error procesando archivo: {str(e)}', 'danger')
                return redirect(request.url)
        else:
            flash('Tipo de archivo no permitido. Use PNG, JPG, JPEG o GIF', 'danger')
            return redirect(request.url)
    
    return render_template('diagnosis/upload.html')

@diagnosis_bp.route('/api/diagnose', methods=['POST'])
def api_diagnose():
    """API endpoint para diagnóstico"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No se proporcionó archivo'}), 400
        
        file = request.files['file']
        
        if file.filename == '':
            return jsonify({'error': 'Nombre de archivo vacío'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': 'Tipo de archivo no permitido'}), 400
        
        # Crear archivo temporal
        with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as temp_file:
            file.save(temp_file.name)
            temp_path = temp_file.name
        
        try:
            # Redimensionar imagen
            resize_image(temp_path)
            
            # Realizar diagnóstico
            result = call_diagnosis_api(temp_path)
            
            return jsonify(result)
            
        finally:
            # Limpiar archivo temporal
            if os.path.exists(temp_path):
                os.remove(temp_path)
                
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@diagnosis_bp.route('/api/status')
def api_status():
    """Estado del sistema de diagnóstico"""
    try:
        # Verificar API externa
        response = requests.get(f"{DIAGNOSIS_API_URL}/health", timeout=5)
        api_available = response.status_code == 200
        api_info = response.json() if api_available else {}
    except:
        api_available = False
        api_info = {}
    
    # Verificar sistema local
    local_available = True
    try:
        import sys
        sys.path.append('ai_diagnosis')
        from plant_disease_model import PlantDiagnosisSystem
        system = PlantDiagnosisSystem()
        local_info = {
            "model_loaded": system.model is not None,
            "device": str(system.device),
            "classes_count": len(system.class_names)
        }
    except:
        local_available = False
        local_info = {}
    
    return jsonify({
        "api_available": api_available,
        "api_info": api_info,
        "local_available": local_available,
        "local_info": local_info,
        "status": "operational" if (api_available or local_available) else "unavailable"
    })
