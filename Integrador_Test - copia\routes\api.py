from flask import request, jsonify, session, Blueprint, render_template
from database import get_db_connection
import datetime
import json
from functools import wraps
# from flask_wtf.csrf import exempt

api_bp = Blueprint('api', __name__, url_prefix='/api')

# Authentication middleware
def require_login(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': 'Acceso no autorizado'}), 401
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

# Helper functions
def row_to_dict(row):
    """Convert a pyodbc row to a dictionary."""
    return {key: value for key, value in zip(
        [column[0] for column in row.cursor_description],
        row
    )}

# Add missing database helper functions
def execute_query(query, params=None, fetch=True):
    """Execute a SQL query and return the results."""
    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)

        results = cursor.fetchall() if fetch else None

        if not fetch:
            conn.commit()

        return results
    except Exception as e:
        conn.rollback()
        raise e
    finally:
        cursor.close()
        conn.close()

def call_stored_procedure(procedure_name, params=None, fetch=True):
    """Call a stored procedure and optionally return the results."""
    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        if params:
            cursor.execute(f"EXEC {procedure_name} {', '.join(['?' for _ in params])}", params)
        else:
            cursor.execute(f"EXEC {procedure_name}")

        results = cursor.fetchall() if fetch else None

        if not fetch:
            conn.commit()

        return results
    except Exception as e:
        conn.rollback()
        raise e
    finally:
        cursor.close()
        conn.close()

# Plant-related endpoints
@api_bp.route('/plantas', methods=['GET'])
@require_login
def get_plantas():
    try:
        user_id = session.get('user_id')

        query = """
        SELECT p.PlantaID, p.NombrePlanta, tp.Nombre AS TipoPlanta,
               es.Descripcion AS EstadoSalud, es.ColorRepresentativo AS ColorEstado,
               fb.Nombre AS FamiliaBotanica, p.Ubicacion, p.FechaAdquisicion
        FROM PlantasUsuario p
        JOIN CatTipoPlanta tp ON p.TipoPlantaID = tp.TipoPlantaID
        JOIN CatEstadoSalud es ON p.EstadoSaludID = es.EstadoSaludID
        LEFT JOIN CatFamiliaBotanica fb ON p.FamiliaID = fb.FamiliaID
        WHERE p.UsuarioID = ?
        ORDER BY p.FechaAdquisicion DESC
        """

        plantas = execute_query(query, (user_id,))

        result = []
        for planta in plantas:
            result.append({
                'id': planta.PlantaID,
                'nombre': planta.NombrePlanta,
                'tipo': planta.TipoPlanta,
                'estadoSalud': planta.EstadoSalud,
                'colorEstado': planta.ColorEstado,
                'familia': planta.FamiliaBotanica,
                'ubicacion': planta.Ubicacion,
                'fechaAdquisicion': planta.FechaAdquisicion.isoformat() if planta.FechaAdquisicion else None
            })

        return jsonify({'success': True, 'plantas': result})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@api_bp.route('/plantas/<int:planta_id>', methods=['GET'])
@require_login
def get_planta(planta_id):
    try:
        query = """
        SELECT p.PlantaID, p.NombrePlanta, p.TipoPlantaID, tp.Nombre AS TipoPlanta,
               p.Ubicacion, p.EdadAproximada, p.TamanoActual, p.FechaAdquisicion,
               p.EstadoSaludID, es.Descripcion AS EstadoSalud, es.ColorRepresentativo AS ColorEstado,
               p.FamiliaID, fb.Nombre AS FamiliaBotanica, fb.NombreCientifico AS NombreCientificoFamilia
        FROM PlantasUsuario p
        JOIN CatTipoPlanta tp ON p.TipoPlantaID = tp.TipoPlantaID
        JOIN CatEstadoSalud es ON p.EstadoSaludID = es.EstadoSaludID
        LEFT JOIN CatFamiliaBotanica fb ON p.FamiliaID = fb.FamiliaID
        WHERE p.PlantaID = ? AND p.UsuarioID = ?
        """

        plantas = execute_query(query, (planta_id, session.get('user_id')))

        if not plantas or len(plantas) == 0:
            return jsonify({'success': False, 'message': 'Planta no encontrada'}), 404

        planta = plantas[0]

        # Get care information
        care_query = """
        SELECT FrecuenciaRiego, UltimoRiego, ProximoRiego, TipoSueloID,
               ts.Nombre AS TipoSuelo, CondicionLuzID, cl.Nombre AS CondicionLuz,
               TemperaturaIdeal, NotasEspeciales
        FROM CuidadosPlantas cp
        LEFT JOIN CatTipoSuelo ts ON cp.TipoSueloID = ts.TipoSueloID
        LEFT JOIN CatCondicionLuz cl ON cp.CondicionLuzID = cl.CondicionLuzID
        WHERE PlantaID = ?
        """

        cuidados = execute_query(care_query, (planta_id,))
        cuidado_info = row_to_dict(cuidados[0]) if cuidados and len(cuidados) > 0 else None

        # Get diagnostics
        diag_query = """
        SELECT TOP 5 d.DiagnosticoID, d.FechaDiagnostico, d.ResultadoIA,
               d.EnfermedadID, e.Nombre AS Enfermedad,
               d.EstadoSaludID, es.Descripcion AS EstadoSalud,
               d.PorcentajeConfianza, d.Observaciones
        FROM Diagnosticos d
        LEFT JOIN CatEnfermedad e ON d.EnfermedadID = e.EnfermedadID
        LEFT JOIN CatEstadoSalud es ON d.EstadoSaludID = es.EstadoSaludID
        WHERE d.PlantaID = ?
        ORDER BY d.FechaDiagnostico DESC
        """

        diagnosticos_results = execute_query(diag_query, (planta_id,))
        diagnosticos = []

        for diag in diagnosticos_results:
            diagnosticos.append({
                'id': diag.DiagnosticoID,
                'fecha': diag.FechaDiagnostico.isoformat() if diag.FechaDiagnostico else None,
                'resultado': diag.ResultadoIA,
                'enfermedadId': diag.EnfermedadID,
                'enfermedad': diag.Enfermedad,
                'estadoSaludId': diag.EstadoSaludID,
                'estadoSalud': diag.EstadoSalud,
                'confianza': diag.PorcentajeConfianza,
                'observaciones': diag.Observaciones
            })

        # Get reminder data
        reminder_query = """
        SELECT r.RecordatorioID, r.TipoRecordatorioID, tr.Nombre AS TipoRecordatorio,
               r.Descripcion, r.FechaRecordatorio, r.EstadoRecordatorioID,
               er.Nombre AS EstadoRecordatorio, r.Recurrencia, tr.ColorAsociado
        FROM Recordatorios r
        JOIN CatTipoRecordatorio tr ON r.TipoRecordatorioID = tr.TipoRecordatorioID
        JOIN CatEstadoRecordatorio er ON r.EstadoRecordatorioID = er.EstadoRecordatorioID
        WHERE r.PlantaID = ? AND r.UsuarioID = ?
        ORDER BY r.FechaRecordatorio
        """

        recordatorios_results = execute_query(reminder_query, (planta_id, session.get('user_id')))
        recordatorios = []

        for rec in recordatorios_results:
            recordatorios.append({
                'id': rec.RecordatorioID,
                'tipoId': rec.TipoRecordatorioID,
                'tipo': rec.TipoRecordatorio,
                'descripcion': rec.Descripcion,
                'fecha': rec.FechaRecordatorio.isoformat() if rec.FechaRecordatorio else None,
                'estadoId': rec.EstadoRecordatorioID,
                'estado': rec.EstadoRecordatorio,
                'recurrencia': rec.Recurrencia,
                'color': rec.ColorAsociado
            })

        # Build the complete response
        planta_info = {
            'id': planta.PlantaID,
            'nombre': planta.NombrePlanta,
            'tipoId': planta.TipoPlantaID,
            'tipo': planta.TipoPlanta,
            'ubicacion': planta.Ubicacion,
            'edad': planta.EdadAproximada,
            'tamano': planta.TamanoActual,
            'fechaAdquisicion': planta.FechaAdquisicion.isoformat() if planta.FechaAdquisicion else None,
            'estadoSaludId': planta.EstadoSaludID,
            'estadoSalud': planta.EstadoSalud,
            'colorEstado': planta.ColorEstado,
            'familiaId': planta.FamiliaID,
            'familia': planta.FamiliaBotanica,
            'familiaCientifica': planta.NombreCientificoFamilia,
            'cuidados': cuidado_info,
            'diagnosticos': diagnosticos,
            'recordatorios': recordatorios
        }

        return jsonify({
            'success': True,
            'planta': planta_info
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@api_bp.route('/plantas', methods=['POST'])
@require_login
def create_planta():
    try:
        data = request.get_json()

        # Extract data
        nombre_planta = data.get('nombrePlanta')
        tipo_planta_id = data.get('tipoPlantaID')
        ubicacion = data.get('ubicacion')
        edad_aproximada = data.get('edadAproximada')
        tamano_actual = data.get('tamanoActual')
        familia_id = data.get('familiaID')

        if not nombre_planta or not tipo_planta_id:
            return jsonify({'success': False, 'message': 'Nombre y tipo de planta son requeridos'}), 400

        # Call the stored procedure
        user_id = session['user_id']

        conn = get_db_connection()
        cursor = conn.cursor()

        # Execute the stored procedure
        cursor.execute(
            "EXEC RegistrarPlantaUsuario @UsuarioID=?, @NombrePlanta=?, @TipoPlantaID=?, @Ubicacion=?, @EdadAproximada=?, @TamanoActual=?, @FamiliaID=?",
            (user_id, nombre_planta, tipo_planta_id, ubicacion or '',
             edad_aproximada or 0, tamano_actual or 0, familia_id)
        )

        # Get the new plant ID
        planta_id = cursor.fetchval()
        conn.commit()
        cursor.close()
        conn.close()

        return jsonify({
            'success': True,
            'message': 'Planta registrada correctamente',
            'plantaID': planta_id
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

# Catalog endpoints
@api_bp.route('/catalogos/tipos-planta', methods=['GET'])
def get_tipos_planta():
    try:
        query = "SELECT TipoPlantaID, Nombre, Descripcion, RequerimientosSol, RequerimientosAgua, RequerimientosSuelo FROM CatTipoPlanta"
        tipos = execute_query(query)

        result = []
        for t in tipos:
            result.append({
                'id': t.TipoPlantaID,
                'nombre': t.Nombre,
                'descripcion': t.Descripcion,
                'requerimientosSol': t.RequerimientosSol,
                'requerimientosAgua': t.RequerimientosAgua,
                'requerimientosSuelo': t.RequerimientosSuelo
            })

        return jsonify({'success': True, 'tipos': result})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@api_bp.route('/catalogos/familias-botanicas', methods=['GET'])
def get_familias_botanicas():
    try:
        query = "SELECT FamiliaID, Nombre, NombreCientifico, Descripcion, CaracteristicasComunes FROM CatFamiliaBotanica"
        familias = execute_query(query)

        result = []
        for f in familias:
            result.append({
                'id': f.FamiliaID,
                'nombre': f.Nombre,
                'nombreCientifico': f.NombreCientifico,
                'descripcion': f.Descripcion,
                'caracteristicas': f.CaracteristicasComunes
            })

        return jsonify({'success': True, 'familias': result})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@api_bp.route('/catalogos/tipos-recordatorio', methods=['GET'])
def get_tipos_recordatorio():
    try:
        query = "SELECT TipoRecordatorioID, Nombre, Descripcion, ColorAsociado, IconoAsociado, EsRecurrente FROM CatTipoRecordatorio"
        tipos = execute_query(query)

        result = []
        for t in tipos:
            result.append({
                'id': t.TipoRecordatorioID,
                'nombre': t.Nombre,
                'descripcion': t.Descripcion,
                'color': t.ColorAsociado,
                'icono': t.IconoAsociado,
                'esRecurrente': bool(t.EsRecurrente)
            })

        return jsonify({'success': True, 'tipos': result})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

# Calendar/Reminder endpoints
@api_bp.route('/recordatorios', methods=['GET'])
def get_recordatorios():
    try:
        # Simulación de datos para la vista de recomendaciones
        return render_template('recomendaciones.html')
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@api_bp.route('/recordatorios', methods=['POST'])
@require_login
def create_recordatorio():
    try:
        data = request.get_json()

        user_id = session['user_id']
        planta_id = data.get('plantaId')
        tipo_recordatorio_id = data.get('tipoRecordatorioId')
        descripcion = data.get('descripcion')
        fecha_recordatorio = data.get('fechaRecordatorio')
        recurrencia = data.get('recurrencia')

        if not planta_id or not tipo_recordatorio_id or not fecha_recordatorio:
            return jsonify({'success': False, 'message': 'Planta, tipo y fecha de recordatorio son requeridos'}), 400

        try:
            # Call the stored procedure
            recordatorio_id = call_stored_procedure(
                "CrearRecordatorio",
                (user_id, planta_id, tipo_recordatorio_id, descripcion or '', fecha_recordatorio, recurrencia),
                fetch=True
            )

            return jsonify({
                'success': True,
                'message': 'Recordatorio creado correctamente',
                'recordatorioId': recordatorio_id[0][0] if recordatorio_id and len(recordatorio_id) > 0 else None
            })
        except Exception as e:
            return jsonify({'success': False, 'message': f'Error al crear recordatorio: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

# Plant care endpoints
@api_bp.route('/plants/register-care', methods=['POST'])
@require_login
def register_plant_care():
    try:
        data = request.get_json()

        plant_id = data.get('plant_id')
        care_type = data.get('care_type')
        care_date = data.get('care_date')
        care_notes = data.get('care_notes', '')

        if not plant_id or not care_type or not care_date:
            return jsonify({'success': False, 'message': 'ID de planta, tipo de cuidado y fecha son requeridos'}), 400

        # Verificar que la planta pertenece al usuario
        user_id = session['user_id']
        check_query = "SELECT COUNT(*) AS count FROM PlantasUsuario WHERE PlantaID = ? AND UsuarioID = ?"
        result = execute_query(check_query, (plant_id, user_id))

        if result[0].count == 0:
            return jsonify({'success': False, 'message': 'No tienes permiso para registrar cuidados para esta planta'}), 403

        # Verificar si ya existe un registro de cuidados para esta planta
        check_care_query = "SELECT COUNT(*) AS count FROM CuidadosPlantas WHERE PlantaID = ?"
        care_result = execute_query(check_care_query, (plant_id,))

        # Convertir la fecha de string a objeto datetime
        try:
            care_datetime = datetime.datetime.fromisoformat(care_date.replace('Z', '+00:00'))
        except ValueError:
            return jsonify({'success': False, 'message': 'Formato de fecha inválido'}), 400

        if care_result[0].count == 0:
            # Crear un nuevo registro de cuidados
            if care_type == 'watering':
                insert_query = """
                INSERT INTO CuidadosPlantas (PlantaID, UltimoRiego, NotasEspeciales)
                VALUES (?, ?, ?)
                """
                execute_query(insert_query, (plant_id, care_datetime, care_notes), fetch=False)
            elif care_type == 'fertilizing':
                insert_query = """
                INSERT INTO CuidadosPlantas (PlantaID, UltimoAbono, NotasEspeciales)
                VALUES (?, ?, ?)
                """
                execute_query(insert_query, (plant_id, care_datetime, care_notes), fetch=False)
        else:
            # Actualizar el registro existente
            if care_type == 'watering':
                update_query = """
                UPDATE CuidadosPlantas
                SET UltimoRiego = ?, NotasEspeciales = ?
                WHERE PlantaID = ?
                """
                execute_query(update_query, (care_datetime, care_notes, plant_id), fetch=False)
            elif care_type == 'fertilizing':
                update_query = """
                UPDATE CuidadosPlantas
                SET UltimoAbono = ?, NotasEspeciales = ?
                WHERE PlantaID = ?
                """
                execute_query(update_query, (care_datetime, care_notes, plant_id), fetch=False)

        return jsonify({
            'success': True,
            'message': 'Cuidado registrado correctamente',
            'care_type': care_type,
            'care_date': care_date
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

# Diagnostic endpoints
@api_bp.route('/diagnosticos', methods=['POST'])
@require_login
def create_diagnostico():
    try:
        data = request.get_json()

        planta_id = data.get('plantaId')
        imagen_diagnostico = data.get('imagen')  # This would need to be handled differently for a real image
        enfermedad_id = data.get('enfermedadId')
        estado_salud_id = data.get('estadoSaludId')
        resultado_ia = data.get('resultadoIA')
        malezas_detectadas = data.get('malezasDetectadas')
        porcentaje_confianza = data.get('porcentajeConfianza')
        observaciones = data.get('observaciones')

        if not planta_id:
            return jsonify({'success': False, 'message': 'ID de planta es requerido'}), 400

        # Call the stored procedure
        user_id = session['user_id']

        try:
            # This is simplified as we're not handling actual image binary data
            # In a real app, we'd save the image to a filesystem or blob storage
            diagnostico_id = call_stored_procedure(
                "DiagnosticarPlanta",
                (planta_id, None, enfermedad_id, estado_salud_id, resultado_ia,
                 malezas_detectadas, porcentaje_confianza, observaciones, user_id),
                fetch=False
            )

            return jsonify({
                'success': True,
                'message': 'Diagnóstico registrado correctamente'
            })
        except Exception as e:
            return jsonify({'success': False, 'message': f'Error al registrar diagnóstico: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

# User settings endpoints
@api_bp.route('/configuraciones', methods=['GET'])
@require_login
def get_configuraciones():
    try:
        user_id = session['user_id']

        query = """
        SELECT NotificacionesEmail, NotificacionesPush, IdiomaPreferido, ModoOscuro, FrecuenciaNotificaciones, NivelExperiencia
        FROM ConfiguracionesUsuario
        WHERE UsuarioID = ?
        """

        configs = execute_query(query, (user_id,))

        if not configs or len(configs) == 0:
            return jsonify({
                'success': True,
                'configuraciones': {
                    'notificacionesEmail': True,
                    'notificacionesPush': True,
                    'idiomaPreferido': 'es-mx',
                    'modoOscuro': False,
                    'frecuenciaNotificaciones': 'Diaria',
                    'nivelExperiencia': 'principiante'
                }
            })

        config = configs[0]

        return jsonify({
            'success': True,
            'configuraciones': {
                'notificacionesEmail': bool(config.NotificacionesEmail),
                'notificacionesPush': bool(config.NotificacionesPush),
                'idiomaPreferido': config.IdiomaPreferido,
                'modoOscuro': bool(config.ModoOscuro),
                'frecuenciaNotificaciones': config.FrecuenciaNotificaciones,
                'nivelExperiencia': config.NivelExperiencia if hasattr(config, 'NivelExperiencia') else 'principiante'
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@api_bp.route('/configuraciones', methods=['POST'])
@require_login
def update_configuraciones():
    try:
        data = request.get_json()
        user_id = session['user_id']

        notificaciones_email = data.get('notificacionesEmail', True)
        notificaciones_push = data.get('notificacionesPush', True)
        idioma_preferido = data.get('idiomaPreferido', 'es-mx')
        modo_oscuro = data.get('modoOscuro', False)
        frecuencia_notificaciones = data.get('frecuenciaNotificaciones', 'Diaria')

        # Check if config exists
        check_query = "SELECT COUNT(*) AS count FROM ConfiguracionesUsuario WHERE UsuarioID = ?"
        result = execute_query(check_query, (user_id,))

        if result[0].count > 0:
            # Update existing config
            update_query = """
            UPDATE ConfiguracionesUsuario
            SET NotificacionesEmail = ?,
                NotificacionesPush = ?,
                IdiomaPreferido = ?,
                ModoOscuro = ?,
                FrecuenciaNotificaciones = ?
            WHERE UsuarioID = ?
            """

            execute_query(
                update_query,
                (notificaciones_email, notificaciones_push, idioma_preferido,
                 modo_oscuro, frecuencia_notificaciones, user_id),
                fetch=False
            )
        else:
            # Insert new config
            insert_query = """
            INSERT INTO ConfiguracionesUsuario
                (UsuarioID, NotificacionesEmail, NotificacionesPush, IdiomaPreferido, ModoOscuro, FrecuenciaNotificaciones)
            VALUES (?, ?, ?, ?, ?, ?)
            """

            execute_query(
                insert_query,
                (user_id, notificaciones_email, notificaciones_push,
                 idioma_preferido, modo_oscuro, frecuencia_notificaciones),
                fetch=False
            )

        return jsonify({
            'success': True,
            'message': 'Configuraciones actualizadas correctamente'
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

# Alias for 'configuraciones' to match the Spanish naming
@api_bp.route('/ajustes', methods=['GET'])
@require_login
def get_ajustes():
    """Alias for get_configuraciones to support Spanish naming convention."""
    return get_configuraciones()

@api_bp.route('/ajustes', methods=['POST'])
@require_login
def update_ajustes():
    """Alias for update_configuraciones to support Spanish naming convention."""
    return update_configuraciones()

@api_bp.route('/plants/recommendations', methods=['GET', 'POST'])
def get_plant_recommendations():
    try:
        import os
        print(f"🌱 Endpoint de recomendaciones llamado - Método: {request.method}")

        # Obtener parámetros de la solicitud
        page = request.args.get('page', 1, type=int)
        sort = request.args.get('sort', 'match')
        items_per_page = 12

        print(f"🌱 Parámetros: page={page}, sort={sort}")

        # Usar nivel de experiencia del usuario si está autenticado, o usar un valor por defecto
        nivel_experiencia = 'principiante'  # Valor por defecto

        # Obtener filtros del cuerpo de la solicitud si es POST
        filters = {}
        if request.method == 'POST':
            filters = request.json or {}
            print(f"🌱 Filtros recibidos: {filters}")

        # Cargar datos de plantas desde JSON
        plants_file = os.path.join('data', 'json', 'plant_recommendations.json')
        light_conditions_file = os.path.join('data', 'json', 'light_conditions.json')

        print(f"🌱 Buscando archivo: {plants_file}")
        print(f"🌱 Archivo existe: {os.path.exists(plants_file)}")

        if not os.path.exists(plants_file):
            return jsonify({'success': False, 'message': 'Archivo de datos de plantas no encontrado'}), 500

        with open(plants_file, 'r', encoding='utf-8') as f:
            plants_data = json.load(f)

        print(f"🌱 Plantas cargadas: {len(plants_data)}")

        # Cargar condiciones de luz
        light_conditions = {}
        if os.path.exists(light_conditions_file):
            with open(light_conditions_file, 'r', encoding='utf-8') as f:
                light_data = json.load(f)
                for light in light_data:
                    light_conditions[light['CondicionLuzID']] = light['Descripcion']

        # Procesar datos de plantas
        processed_plants = []
        for plant in plants_data:
            # Calcular porcentaje de coincidencia basado en nivel de experiencia
            match_percentage = 60  # Valor por defecto
            difficulty = plant.get('DificultadCuidado', 'Moderado')

            if difficulty == 'Fácil' and nivel_experiencia == 'principiante':
                match_percentage = 95
            elif difficulty == 'Moderado' and nivel_experiencia == 'intermedio':
                match_percentage = 90
            elif difficulty == 'Difícil' and nivel_experiencia == 'avanzado':
                match_percentage = 95
            elif difficulty == 'Fácil' and nivel_experiencia == 'intermedio':
                match_percentage = 80
            elif difficulty == 'Moderado' and nivel_experiencia == 'avanzado':
                match_percentage = 85
            elif difficulty == 'Moderado' and nivel_experiencia == 'principiante':
                match_percentage = 70
            elif difficulty == 'Difícil' and nivel_experiencia == 'intermedio':
                match_percentage = 75
            elif difficulty == 'Difícil' and nivel_experiencia == 'principiante':
                match_percentage = 50

            # Obtener descripción de condición de luz
            sunlight_requirement = light_conditions.get(plant.get('CondicionLuzID', 1), plant.get('RequerimientosSol', 'Sol directo'))

            # Calcular nivel de mantenimiento
            maintenance_level = 'Medio'
            if difficulty == 'Fácil':
                maintenance_level = 'Bajo'
            elif difficulty == 'Difícil':
                maintenance_level = 'Alto'

            # Mapear frecuencia de riego a requerimiento de agua
            water_requirement = 'Medio'
            water_freq = plant.get('FrecuenciaRiego', 'Moderado')
            if water_freq == 'Poco frecuente':
                water_requirement = 'Bajo'
            elif water_freq == 'Frecuente':
                water_requirement = 'Alto'

            processed_plant = {
                'id': plant['TipoPlantaID'],
                'name': plant['Nombre'],
                'scientificName': plant['NombreCientifico'],
                'description': plant['Descripcion'],
                'difficulty': difficulty,
                'waterFrequency': water_freq,
                'waterRequirement': water_requirement,
                'sunlightRequirement': sunlight_requirement,
                'maintenanceLevel': maintenance_level,
                'matchPercentage': match_percentage,
                'imageUrl': plant.get('imageUrl', '/static/assets/default-plant.jpg'),
                'isSaved': False,  # En una implementación real, esto se verificaría en la base de datos
                'isNative': plant.get('isNative', False),
                'isPurifying': plant.get('isPurifying', False),
                'isEdible': plant.get('isEdible', False)
            }

            processed_plants.append(processed_plant)

        # Aplicar filtros
        filtered_plants = processed_plants

        # Filtrar por nivel de experiencia (ya se aplica en el cálculo de matchPercentage)
        experience_level = filters.get('experienceLevel', 'beginner')
        if experience_level == 'beginner':
            # Los principiantes prefieren plantas fáciles
            filtered_plants = [p for p in filtered_plants if p['difficulty'] in ['Fácil', 'Moderado']]
        elif experience_level == 'intermediate':
            # Los intermedios pueden manejar cualquier dificultad
            pass  # No filtrar por dificultad
        elif experience_level == 'advanced':
            # Los avanzados prefieren desafíos
            pass  # No filtrar por dificultad

        # Filtrar por luz solar disponible
        sunlight_filter = filters.get('sunlight')
        if sunlight_filter == 'low':
            # Poca luz - filtrar plantas que necesitan sol directo
            filtered_plants = [p for p in filtered_plants if 'Sol directo' not in p['sunlightRequirement']]
        elif sunlight_filter == 'high':
            # Mucha luz - incluir todas las plantas
            pass  # No filtrar
        elif sunlight_filter == 'medium':
            # Luz media - excluir plantas que necesitan sol muy directo
            filtered_plants = [p for p in filtered_plants if p['sunlightRequirement'] != 'Sol directo']

        # Filtrar por frecuencia de riego
        watering_filter = filters.get('wateringFrequency')
        if watering_filter == 'low':
            # Poco riego - plantas que requieren poco agua
            filtered_plants = [p for p in filtered_plants if p['waterRequirement'] in ['Bajo', 'Medio']]
        elif watering_filter == 'high':
            # Mucho riego - incluir todas las plantas
            pass  # No filtrar
        elif watering_filter == 'medium':
            # Riego moderado
            filtered_plants = [p for p in filtered_plants if p['waterRequirement'] in ['Medio', 'Alto']]

        # Filtros adicionales para compatibilidad con el frontend existente
        if filters.get('difficulty'):
            difficulties = filters.get('difficulty')
            filtered_plants = [p for p in filtered_plants if p['difficulty'] in difficulties]

        if filters.get('sunlight') and filters.get('sunlight') not in ['low', 'medium', 'high']:
            sunlights = filters.get('sunlight')
            filtered_plants = [p for p in filtered_plants if p['sunlightRequirement'] in sunlights]

        # Ordenar según el parámetro sort
        if sort == 'match':
            filtered_plants.sort(key=lambda x: x['matchPercentage'], reverse=True)
        elif sort == 'name-asc':
            filtered_plants.sort(key=lambda x: x['name'])
        elif sort == 'name-desc':
            filtered_plants.sort(key=lambda x: x['name'], reverse=True)
        elif sort == 'difficulty-asc':
            difficulty_order = {'Fácil': 1, 'Moderado': 2, 'Difícil': 3}
            filtered_plants.sort(key=lambda x: difficulty_order.get(x['difficulty'], 4))
        elif sort == 'difficulty-desc':
            difficulty_order = {'Fácil': 1, 'Moderado': 2, 'Difícil': 3}
            filtered_plants.sort(key=lambda x: difficulty_order.get(x['difficulty'], 4), reverse=True)
        else:
            filtered_plants.sort(key=lambda x: x['matchPercentage'], reverse=True)

        # Calcular paginación
        total_plants = len(filtered_plants)
        start_index = (page - 1) * items_per_page
        end_index = start_index + items_per_page
        paginated_plants = filtered_plants[start_index:end_index]

        # Calcular información de paginación
        total_pages = (total_plants + items_per_page - 1) // items_per_page
        pagination = {
            'currentPage': page,
            'totalPages': total_pages,
            'totalItems': total_plants,
            'itemsPerPage': items_per_page,
            'hasNextPage': page < total_pages,
            'hasPrevPage': page > 1
        }

        return jsonify({
            'success': True,
            'plants': paginated_plants,
            'pagination': pagination,
            'userExperienceLevel': nivel_experiencia
        })

    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'message': str(e)}), 500
