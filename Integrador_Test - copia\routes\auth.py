from flask import Blueprint, render_template, redirect, url_for, flash, request, session
from flask_login import login_user, logout_user, current_user
from werkzeug.security import check_password_hash, generate_password_hash
import uuid
from app import User, load_users, save_users

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('views.home'))
    
    if request.method == 'POST':
        email = request.form.get('email')
        password = request.form.get('password')
        
        # Cargar usuarios desde JSON
        users = load_users()
        
        # Buscar usuario por email
        user_id = None
        user_data = None
        
        for uid, udata in users.items():
            if udata.get('email') == email:
                user_id = uid
                user_data = udata
                break
        
        if user_id and check_password_hash(user_data['password_hash'], password):
            user = User(
                id=user_id,
                username=user_data['username'],
                password_hash=user_data['password_hash']
            )
            login_user(user)
            flash('Has iniciado sesión correctamente', 'success')
            
            # Redirigir a la página solicitada o a home
            next_page = request.args.get('next')
            return redirect(next_page or url_for('views.home'))
        
        flash('Email o contraseña incorrectos', 'danger')
    
    return render_template('auth/login.html')

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    if current_user.is_authenticated:
        return redirect(url_for('views.home'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        
        # Validaciones básicas
        if not username or not email or not password:
            flash('Todos los campos son obligatorios', 'danger')
            return render_template('auth/register.html')
        
        # Cargar usuarios existentes
        users = load_users()
        
        # Verificar si el email ya está registrado
        for _, user_data in users.items():
            if user_data.get('email') == email:
                flash('El email ya está registrado', 'danger')
                return render_template('auth/register.html')
        
        # Generar ID para el nuevo usuario
        user_id = str(uuid.uuid4())
        
        # Crear nuevo usuario
        users[user_id] = {
            'username': username,
            'email': email,
            'password_hash': generate_password_hash(password)
        }
        
        # Guardar usuarios
        save_users(users)
        
        flash('Registro exitoso. Ahora puedes iniciar sesión', 'success')
        return redirect(url_for('auth.login'))
    
    return render_template('auth/register.html')

@auth_bp.route('/logout')
def logout():
    logout_user()
    flash('Has cerrado sesión correctamente', 'info')
    return redirect(url_for('views.home'))  # Corregido para usar views.home
