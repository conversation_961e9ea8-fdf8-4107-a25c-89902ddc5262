from flask import Blueprint, render_template, redirect, url_for
from flask_login import login_required

diagnosis_bp = Blueprint('diagnosis', __name__)

@diagnosis_bp.route('/')
def index():
    return redirect(url_for('diagnosis.scanner'))

@diagnosis_bp.route('/scanner')
def scanner():
    return render_template('scanner.html')

@diagnosis_bp.route('/resultado/<int:diagnostico_id>')
def resultado(diagnostico_id):
    # Código para mostrar resultado de diagnóstico
    return render_template('resultado-diagnostico.html', diagnostico_id=diagnostico_id)
