from flask import Blueprint, render_template, redirect, url_for
from flask_login import login_required

forum_bp = Blueprint('forum', __name__)

@forum_bp.route('/')
def index():
    return render_template('foro.html')

@forum_bp.route('/tema/<int:tema_id>')
def tema(tema_id):
    # Código para mostrar un tema específico
    return render_template('tema.html', tema_id=tema_id)

@forum_bp.route('/nuevo-tema')
@login_required
def nuevo_tema():
    # Código para crear nuevo tema
    return render_template('nuevo-tema.html')
