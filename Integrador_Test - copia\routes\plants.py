from flask import Blueprint, render_template, redirect, url_for
from flask_login import login_required, current_user

plants_bp = Blueprint('plants', __name__)

@plants_bp.route('/')
def index():
    return redirect(url_for('plants.biblioteca'))

@plants_bp.route('/biblioteca')
def biblioteca():
    return render_template('biblioteca.html')

@plants_bp.route('/mis-plantas')
@login_required
def mis_plantas():
    # Código para mostrar las plantas del usuario
    return render_template('mis-plantas.html')

@plants_bp.route('/detalle/<int:planta_id>')
def detalle(planta_id):
    # Código para mostrar detalles de una planta
    return render_template('detalle-planta.html', planta_id=planta_id)
