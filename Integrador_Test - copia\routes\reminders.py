from flask import Blueprint, render_template, redirect, url_for
from flask_login import login_required

reminders_bp = Blueprint('reminders', __name__)

@reminders_bp.route('/')
def index():
    return redirect(url_for('reminders.calendario'))

@reminders_bp.route('/calendario')
def calendario():
    return render_template('calendario.html')

@reminders_bp.route('/recordatorio/nuevo')
@login_required
def nuevo_recordatorio():
    # Código para crear nuevo recordatorio
    return render_template('nuevo-recordatorio.html')
