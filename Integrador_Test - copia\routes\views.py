from flask import Blueprint, render_template, redirect, url_for, flash
from flask_login import current_user, login_required

views_bp = Blueprint('views', __name__)

@views_bp.route('/')
def index():
    return redirect(url_for('views.home'))

@views_bp.route('/home')
def home():
    return render_template('home.html')

# Otras rutas principales que no encajan en otros blueprints
@views_bp.route('/about')
def about():
    return render_template('about.html')

@views_bp.route('/contact')
def contact():
    return render_template('contact.html')

# Asegúrate de que no haya una ruta login aquí que pueda causar conflictos
