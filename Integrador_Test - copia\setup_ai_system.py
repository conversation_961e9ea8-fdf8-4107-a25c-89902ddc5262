#!/usr/bin/env python3
"""
Script de configuración automática para el sistema de IA de diagnóstico de plantas
"""

import os
import subprocess
import sys
import json
import urllib.request
import zipfile
import shutil
from pathlib import Path

def print_step(step, message):
    """Imprimir paso con formato"""
    print(f"\n{'='*60}")
    print(f"PASO {step}: {message}")
    print(f"{'='*60}")

def run_command(command, description):
    """Ejecutar comando con manejo de errores"""
    print(f"\n🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completado")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error en {description}: {e}")
        print(f"Salida: {e.stdout}")
        print(f"Error: {e.stderr}")
        return False

def check_python_version():
    """Verificar versión de Python"""
    print_step(1, "VERIFICANDO ENTORNO")
    
    version = sys.version_info
    print(f"🐍 Python {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Se requiere Python 3.8 o superior")
        return False
    
    print("✅ Versión de Python compatible")
    return True

def install_dependencies():
    """Instalar dependencias de IA"""
    print_step(2, "INSTALANDO DEPENDENCIAS")
    
    # Verificar si pip está disponible
    if not run_command("pip --version", "Verificando pip"):
        print("❌ pip no está disponible")
        return False
    
    # Instalar dependencias básicas
    basic_deps = [
        "torch",
        "torchvision", 
        "torchaudio",
        "Pillow",
        "numpy",
        "requests",
        "fastapi",
        "uvicorn",
        "python-multipart"
    ]
    
    for dep in basic_deps:
        if not run_command(f"pip install {dep}", f"Instalando {dep}"):
            print(f"⚠️ Error instalando {dep}, continuando...")
    
    # Instalar dependencias opcionales
    optional_deps = [
        "opencv-python",
        "scikit-image", 
        "matplotlib",
        "seaborn",
        "tqdm"
    ]
    
    print("\n📦 Instalando dependencias opcionales...")
    for dep in optional_deps:
        run_command(f"pip install {dep}", f"Instalando {dep} (opcional)")
    
    return True

def setup_directories():
    """Crear estructura de directorios"""
    print_step(3, "CONFIGURANDO DIRECTORIOS")
    
    directories = [
        "ai_diagnosis/models",
        "ai_diagnosis/data/train",
        "ai_diagnosis/data/validation", 
        "ai_diagnosis/data/test",
        "static/uploads/diagnosis",
        "templates/diagnosis"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"📁 Creado: {directory}")
    
    print("✅ Estructura de directorios creada")
    return True

def download_sample_model():
    """Descargar modelo de ejemplo"""
    print_step(4, "CONFIGURANDO MODELO DE EJEMPLO")
    
    # Crear modelo de ejemplo básico
    model_code = '''
import torch
import torch.nn as nn
from torchvision import models

# Crear modelo básico para pruebas
model = models.efficientnet_b0(pretrained=True)
model.classifier = nn.Sequential(
    nn.Dropout(0.2),
    nn.Linear(model.classifier[1].in_features, 38)
)

# Guardar modelo
torch.save(model.state_dict(), 'ai_diagnosis/models/sample_model.pth')
print("Modelo de ejemplo creado")
'''
    
    try:
        with open('create_sample_model.py', 'w') as f:
            f.write(model_code)
        
        if run_command("python create_sample_model.py", "Creando modelo de ejemplo"):
            os.remove('create_sample_model.py')
            print("✅ Modelo de ejemplo creado")
        else:
            print("⚠️ No se pudo crear modelo de ejemplo")
    except Exception as e:
        print(f"⚠️ Error creando modelo: {e}")
    
    return True

def test_ai_system():
    """Probar sistema de IA"""
    print_step(5, "PROBANDO SISTEMA DE IA")
    
    test_code = '''
import sys
sys.path.append("ai_diagnosis")

try:
    from plant_disease_model import PlantDiagnosisSystem
    
    # Crear sistema
    system = PlantDiagnosisSystem()
    
    # Probar predicción mock
    result = system._mock_prediction()
    
    print("✅ Sistema de IA funcionando")
    print(f"Resultado de prueba: {result['plant_name']}")
    
except Exception as e:
    print(f"❌ Error en sistema de IA: {e}")
    sys.exit(1)
'''
    
    with open('test_ai.py', 'w') as f:
        f.write(test_code)
    
    success = run_command("python test_ai.py", "Probando sistema de IA")
    os.remove('test_ai.py')
    
    return success

def create_startup_scripts():
    """Crear scripts de inicio"""
    print_step(6, "CREANDO SCRIPTS DE INICIO")
    
    # Script para iniciar API
    api_script = '''#!/usr/bin/env python3
"""Script para iniciar la API de diagnóstico"""

import sys
import os
sys.path.append("ai_diagnosis")

from diagnosis_api import run_api

if __name__ == "__main__":
    print("🚀 Iniciando API de Diagnóstico de Plantas...")
    print("📡 API disponible en: http://127.0.0.1:8000")
    print("📚 Documentación en: http://127.0.0.1:8000/docs")
    print("🛑 Presiona CTRL+C para detener")
    
    try:
        run_api()
    except KeyboardInterrupt:
        print("\\n🛑 API detenida")
'''
    
    with open('start_ai_api.py', 'w') as f:
        f.write(api_script)
    
    # Script para probar diagnóstico
    test_script = '''#!/usr/bin/env python3
"""Script para probar el diagnóstico"""

import sys
sys.path.append("ai_diagnosis")

from plant_disease_model import PlantDiagnosisSystem

def test_diagnosis():
    print("🧪 Probando sistema de diagnóstico...")
    
    system = PlantDiagnosisSystem()
    result = system._mock_prediction()
    
    print("\\n📋 Resultado de prueba:")
    print(f"Planta: {result['plant_name']}")
    print(f"Condición: {result['condition']}")
    print(f"Confianza: {result['confidence']}%")
    print(f"Diagnóstico: {result['diagnosis']}")
    
    print("\\n✅ Sistema funcionando correctamente")

if __name__ == "__main__":
    test_diagnosis()
'''
    
    with open('test_diagnosis.py', 'w') as f:
        f.write(test_script)
    
    print("✅ Scripts de inicio creados")
    return True

def create_config_file():
    """Crear archivo de configuración"""
    print_step(7, "CREANDO CONFIGURACIÓN")
    
    config = {
        "ai_system": {
            "model_path": "ai_diagnosis/models/plant_disease_model.pth",
            "api_url": "http://127.0.0.1:8000",
            "max_image_size": "16MB",
            "supported_formats": ["jpg", "jpeg", "png", "gif"],
            "confidence_threshold": 0.7
        },
        "training": {
            "batch_size": 32,
            "learning_rate": 0.001,
            "num_epochs": 50,
            "data_dir": "ai_diagnosis/data/train"
        },
        "deployment": {
            "api_host": "127.0.0.1",
            "api_port": 8000,
            "debug": True
        }
    }
    
    with open('ai_config.json', 'w') as f:
        json.dump(config, f, indent=2)
    
    print("✅ Archivo de configuración creado")
    return True

def show_next_steps():
    """Mostrar próximos pasos"""
    print_step(8, "CONFIGURACIÓN COMPLETADA")
    
    print("""
🎉 ¡Sistema de IA configurado exitosamente!

📋 PRÓXIMOS PASOS:

1. 🧪 PROBAR EL SISTEMA:
   python test_diagnosis.py

2. 🚀 INICIAR API (opcional):
   python start_ai_api.py

3. 🌐 USAR EN LA WEB:
   - El sistema ya está integrado en la aplicación web
   - Ve a: http://127.0.0.1:5000/diagnosis

4. 📚 ENTRENAR CON DATOS REALES:
   - Lee la guía: ai_diagnosis/TRAINING_GUIDE.md
   - Descarga dataset PlantVillage
   - Ejecuta entrenamiento personalizado

5. 🔧 CONFIGURACIÓN AVANZADA:
   - Edita: ai_config.json
   - Ajusta parámetros según tus necesidades

📖 DOCUMENTACIÓN:
   - Guía de entrenamiento: ai_diagnosis/TRAINING_GUIDE.md
   - API docs: http://127.0.0.1:8000/docs (cuando API esté activa)

⚠️ NOTA IMPORTANTE:
   El sistema actual usa predicciones simuladas para desarrollo.
   Para diagnósticos reales, necesitas entrenar el modelo con datos reales.

🆘 SOPORTE:
   Si tienes problemas, revisa los logs y la documentación.
""")

def main():
    """Función principal"""
    print("""
🌱 CONFIGURADOR DEL SISTEMA DE IA PARA DIAGNÓSTICO DE PLANTAS
============================================================

Este script configurará automáticamente el sistema de IA para
diagnóstico de enfermedades en plantas.

⚠️ REQUISITOS:
- Python 3.8 o superior
- Conexión a internet para descargar dependencias
- Al menos 2GB de espacio libre

¿Continuar? (y/n): """, end="")
    
    if input().lower() not in ['y', 'yes', 'sí', 's']:
        print("❌ Configuración cancelada")
        return
    
    # Ejecutar pasos de configuración
    steps = [
        check_python_version,
        install_dependencies,
        setup_directories,
        download_sample_model,
        test_ai_system,
        create_startup_scripts,
        create_config_file
    ]
    
    for i, step in enumerate(steps, 1):
        if not step():
            print(f"\n❌ Error en paso {i}. Configuración incompleta.")
            return
    
    show_next_steps()

if __name__ == "__main__":
    main()
