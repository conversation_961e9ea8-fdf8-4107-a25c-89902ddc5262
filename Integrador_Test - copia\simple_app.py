#!/usr/bin/env python3
"""
Aplicación PlantCare simplificada para pruebas
"""

import os
import json
import random
import time
import base64
from datetime import datetime, timezone
from flask import Flask, render_template, redirect, url_for, request, jsonify
from flask_wtf.csrf import CSRFProtect, generate_csrf
from werkzeug.utils import secure_filename

# Crear la aplicación Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = 'plantcare_secret_key_for_testing'
app.config['UPLOAD_FOLDER'] = 'uploads'

# Inicializar CSRF
csrf = CSRFProtect(app)

# Crear directorios necesarios
os.makedirs('uploads/diagnosis', exist_ok=True)

# Función para verificar archivos permitidos
def allowed_file(filename):
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# Context processor para CSRF token
@app.context_processor
def inject_csrf_token():
    return {
        'csrf_token': generate_csrf(),
        'now': datetime.now(timezone.utc)
    }

# Rutas principales
@app.route('/')
def index():
    return redirect(url_for('scanner'))

@app.route('/diagnosis/scanner')
def scanner():
    return render_template('scanner.html')

@app.route('/diagnosis/upload', methods=['POST'])
def upload_image():
    """Procesa la imagen subida y simula el análisis de IA"""
    try:
        print("📤 Recibida solicitud de upload")
        
        if 'file' not in request.files:
            print("❌ No se encontró archivo en la solicitud")
            return jsonify({'error': 'No se encontró ningún archivo'}), 400
        
        file = request.files['file']
        
        if file.filename == '':
            print("❌ Nombre de archivo vacío")
            return jsonify({'error': 'No se seleccionó ningún archivo'}), 400
        
        if not allowed_file(file.filename):
            print("❌ Tipo de archivo no permitido")
            return jsonify({'error': 'Tipo de archivo no permitido'}), 400
        
        print(f"📁 Procesando archivo: {file.filename}")
        
        # Generar nombre único para el archivo
        import uuid
        filename = f"plant_{uuid.uuid4().hex[:8]}.jpg"
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], 'diagnosis', filename)
        
        # Guardar el archivo
        file.save(file_path)
        print(f"💾 Archivo guardado en: {file_path}")
        
        # Simular procesamiento de IA
        print("🤖 Iniciando análisis de IA...")
        time.sleep(2)  # Simular tiempo de procesamiento
        
        print("✅ Análisis completado")
        return jsonify({
            'success': True,
            'filename': filename
        })
        
    except Exception as e:
        print(f"❌ Error en upload: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/diagnosis/result/<filename>')
def result(filename):
    """Muestra el resultado del análisis de IA"""
    try:
        print(f"📊 Mostrando resultado para: {filename}")
        
        # Plantas disponibles para simulación
        plantas_disponibles = [
            {
                'name': 'Pata de Elefante',
                'scientific': 'Beaucarnea recurvata',
                'disease': 'pata_de_elefante_sana',
                'confidence': random.uniform(75, 95),
                'recommendations': 'Esta planta está saludable. Mantén un riego moderado cada 2-3 semanas y proporciona luz brillante indirecta. Es una planta muy resistente perfecta para principiantes.'
            },
            {
                'name': 'Bugambilia',
                'scientific': 'Bougainvillea spectabilis',
                'disease': 'bugambilia_sana',
                'confidence': random.uniform(70, 90),
                'recommendations': 'Planta en excelente estado. Riega cuando el suelo esté seco y proporciona pleno sol para una floración abundante. Poda regularmente para mantener la forma.'
            },
            {
                'name': 'Agave',
                'scientific': 'Agave americana',
                'disease': 'agave_sana',
                'confidence': random.uniform(80, 95),
                'recommendations': 'Agave saludable. Requiere muy poco riego (una vez al mes) y pleno sol. Perfecto para jardines de bajo mantenimiento en clima árido.'
            },
            {
                'name': 'Cactus',
                'scientific': 'Opuntia ficus-indica',
                'disease': 'cactus_sana',
                'confidence': random.uniform(85, 98),
                'recommendations': 'Cactus en perfecto estado. Riego muy esporádico (cada 3-4 semanas en verano). Necesita mucha luz solar directa.'
            }
        ]
        
        # Seleccionar una planta aleatoria
        planta_detectada = random.choice(plantas_disponibles)
        
        # Intentar leer la imagen
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], 'diagnosis', filename)
        image_base64 = None
        
        if os.path.exists(file_path):
            with open(file_path, 'rb') as f:
                image_data = f.read()
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            print("🖼️ Imagen cargada para mostrar")
        else:
            print("⚠️ Imagen no encontrada, usando placeholder")
        
        print(f"🌱 Planta detectada: {planta_detectada['name']} ({planta_detectada['confidence']:.1f}% confianza)")
        
        return render_template(
            'diagnosis_result.html',
            image_base64=image_base64,
            disease_name=planta_detectada['disease'],
            plant_name=planta_detectada['name'],
            scientific_name=planta_detectada['scientific'],
            confidence=planta_detectada['confidence'],
            recommendations=planta_detectada['recommendations']
        )
        
    except Exception as e:
        print(f"❌ Error mostrando resultado: {str(e)}")
        return f"Error al mostrar resultado: {str(e)}", 500

# Manejadores de errores
@app.errorhandler(404)
def not_found(error):
    return "Página no encontrada", 404

@app.errorhandler(500)
def internal_error(error):
    return "Error interno del servidor", 500

if __name__ == '__main__':
    print("\n🌱 PlantCare - Servidor de Diagnóstico IA")
    print("=" * 50)
    print("🌐 Servidor ejecutándose en: http://127.0.0.1:5000")
    print("🔬 Modo de prueba con IA simulada")
    print("📱 Accede al scanner en: http://127.0.0.1:5000/diagnosis/scanner")
    print("🛑 Presiona CTRL+C para detener")
    print("=" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)
