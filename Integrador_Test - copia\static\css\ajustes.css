/* Base Styles */
:root {
    --primary-color: #4caf50;
    --primary-light: #80e27e;
    --primary-dark: #087f23;
    --secondary-color: #2196f3;
    --text-color: #333333;
    --text-light: #757575;
    --background-color: #ffffff;
    --background-light: #f5f5f5;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --border-radius: 8px;
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--background-light);
    transition: var(--transition);
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    border: none;
    outline: none;
    text-decoration: none;
    gap: 8px;
    font-size: 1rem;
}

.btn .material-icons {
    font-size: 18px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2);
    transform: translateY(-2px);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.1);
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--primary-dark);
    transform: translateY(-2px);
}

.btn-outline:active {
    transform: translateY(0);
}

.btn-danger {
    background-color: #f44336;
    color: white;
}

.btn-danger:hover {
    background-color: #d32f2f;
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.2);
    transform: translateY(-2px);
}

.btn-danger:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(244, 67, 54, 0.1);
}

.btn-text {
    background-color: transparent;
    color: var(--primary-color);
    padding: 4px 8px;
}

.btn-text:hover {
    background-color: rgba(76, 175, 80, 0.1);
}

.btn-sm {
    padding: 4px 12px;
    font-size: 0.875rem;
}

.icon-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: transparent;
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.icon-button:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.dark-theme .icon-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Header */
.app-header {
    background-color: var(--background-color);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
    padding: 12px 0;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--primary-color);
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 500;
}

.main-nav ul {
    display: flex;
    list-style: none;
    gap: 24px;
}

.main-nav a {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
    transition: var(--transition);
    padding: 8px 0;
    position: relative;
}

.main-nav a:hover,
.main-nav a.active {
    color: var(--primary-color);
}

.main-nav a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: var(--transition);
}

.main-nav a:hover::after,
.main-nav a.active::after {
    width: 100%;
}

.user-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
    border: none;
    background: none;
    padding: 0;
}

.avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-menu {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--background-color);
    box-shadow: var(--shadow);
    border-radius: var(--border-radius);
    width: 200px;
    padding: 8px 0;
    margin-top: 8px;
    display: none;
    z-index: 1000;
}

.dropdown-menu.active {
    display: block;
}

.dropdown-menu a {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    text-decoration: none;
    color: var(--text-color);
    transition: var(--transition);
}

.dropdown-menu a:hover,
.dropdown-menu a.active {
    background-color: var(--background-light);
    color: var(--primary-color);
}

/* Main Content */
main {
    padding: 40px 0;
    min-height: calc(100vh - 64px - 300px); /* Viewport height minus header and footer */
}

.page-header {
    margin-bottom: 32px;
    text-align: center;
}

.page-header h1 {
    font-size: 2.5rem;
    margin-bottom: 8px;
    color: var(--primary-color);
}

.subtitle {
    font-size: 1.25rem;
    color: var(--text-light);
}

/* Settings Container */
.settings-container {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 32px;
    margin-bottom: 40px;
}

@media (max-width: 768px) {
    .settings-container {
        grid-template-columns: 1fr;
    }
}

/* Settings Sidebar */
.settings-sidebar {
    position: relative;
}

.settings-nav {
    background-color: var(--background-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    position: sticky;
    top: 100px;
}

.settings-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.settings-nav li {
    border-bottom: 1px solid var(--border-color);
}

.settings-nav li:last-child {
    border-bottom: none;
}

.settings-nav a {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 20px;
    text-decoration: none;
    color: var(--text-color);
    transition: var(--transition);
    font-weight: 500;
}

.settings-nav a .material-icons {
    font-size: 20px;
    color: var(--primary-color);
    transition: var(--transition);
}

.settings-nav a:hover {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--primary-color);
    transform: translateX(5px);
}

.settings-nav a:hover .material-icons {
    transform: scale(1.1);
}

.settings-nav a.active {
    background-color: var(--primary-color);
    color: white;
}

.settings-nav a.active .material-icons {
    color: white;
}

/* Settings Content */
.settings-content {
    background-color: var(--background-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 30px;
    transition: var(--transition);
}

.settings-section {
    display: none;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.settings-section.active {
    display: block;
}

.settings-section h2 {
    font-size: 1.8rem;
    margin-bottom: 30px;
    color: var(--primary-color);
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
    position: relative;
}

.settings-section h2::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 60px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 1.5px;
}

/* Forms */
.form-group {
    margin-bottom: 28px;
}

.form-group label {
    display: block;
    margin-bottom: 10px;
    font-weight: 500;
    color: var(--text-color);
    position: relative;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"],
.form-group select {
    width: 100%;
    padding: 14px 16px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--background-light);
    color: var(--text-color);
    font-size: 1rem;
    transition: var(--transition);
}

.form-group input[type="text"]:focus,
.form-group input[type="email"]:focus,
.form-group input[type="password"]:focus,
.form-group select:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

.form-group input[type="text"]:hover,
.form-group input[type="email"]:hover,
.form-group input[type="password"]:hover,
.form-group select:hover {
    border-color: var(--primary-light);
}

.form-group .form-help {
    margin-top: 6px;
    font-size: 0.875rem;
    color: var(--text-light);
    line-height: 1.4;
}

.form-group input[type="checkbox"] {
    margin-right: 8px;
}

/* About Section */
.about-content {
    line-height: 1.8;
}

.about-content p {
    margin-bottom: 16px;
}

.legal-links {
    margin-top: 24px;
    display: flex;
    gap: 16px;
}

.legal-links a {
    color: var(--primary-color);
    text-decoration: none;
}

.legal-links a:hover {
    text-decoration: underline;
}

/* Footer */
.app-footer {
    background-color: var(--background-color);
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    padding: 40px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 32px;
    margin-bottom: 32px;
}

.footer-section h3 {
    color: var(--primary-color);
    margin-bottom: 16px;
    font-size: 1.125rem;
}

.footer-section p {
    color: var(--text-light);
    margin-bottom: 16px;
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    margin-bottom: 8px;
}

.footer-section a {
    text-decoration: none;
    color: var(--text-light);
    transition: var(--transition);
}

.footer-section a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
    color: var(--text-light);
    font-size: 0.875rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 16px;
    }

    .main-nav ul {
        flex-wrap: wrap;
        justify-content: center;
        gap: 16px;
    }

    .user-actions {
        margin-top: 16px;
    }

    .settings-container {
        grid-template-columns: 1fr;
    }

    .settings-nav ul {
        display: flex;
        overflow-x: auto;
        padding-bottom: 16px;
    }

    .settings-nav a {
        white-space: nowrap;
        padding: 8px 16px;
    }

    .footer-content {
        grid-template-columns: 1fr;
    }
}
