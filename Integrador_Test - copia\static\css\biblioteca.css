/* ===== ESTILOS BASE ===== */
:root {
    --primary: #4CAF50;
    --primary-light: #81C784;
    --primary-dark: #388E3C;
    --accent: #FF9800;
    --text-dark: #263238;
    --text-light: #ECEFF1;
    --background: #FFFFFF;
    --background-alt: #F5F8F5;
    --card-bg: #FFFFFF;
    --shadow: 0 8px 30px rgba(0,0,0,0.05);
    --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

body {
    font-family: 'Roboto', sans-serif;
    color: var(--text-dark);
    background-color: var(--background);
    margin: 0;
    padding: 0;
    line-height: 1.6;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* ===== HEADER & NAVEGACIÓN ===== */
.app-header {
    background-color: var(--background);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 100;
    transition: var(--transition);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo .material-icons {
    color: var(--primary);
    font-size: 28px;
}

.logo h1 {
    font-size: 24px;
    font-weight: 300;
    color: var(--primary-dark);
    margin: 0;
    letter-spacing: 1px;
}

.main-nav ul {
    display: flex;
    list-style: none;
    gap: 30px;
    margin: 0;
    padding: 0;
}

.main-nav a {
    color: var(--text-dark);
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    padding: 8px 2px;
    position: relative;
    transition: var(--transition);
}

.main-nav a:hover,
.main-nav a.active {
    color: var(--primary);
}

.main-nav a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.main-nav a:hover::after,
.main-nav a.active::after {
    transform: scaleX(1);
}

/* User actions & menu */
.user-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-menu {
    position: relative;
}

.avatar {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    border: 2px solid var(--primary-light);
    overflow: hidden;
    padding: 0;
    cursor: pointer;
    background: none;
    transition: var(--transition);
}

.avatar:hover {
    border-color: var(--primary);
    transform: scale(1.05);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.dropdown-menu {
    position: absolute;
    top: calc(100% + 10px);
    right: 0;
    background-color: var(--card-bg);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    width: 220px;
    z-index: 1000;
    padding: 8px 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
}

.dropdown-menu.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 16px;
    width: 12px;
    height: 12px;
    background-color: var(--card-bg);
    transform: rotate(45deg);
    box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.04);
}

.dropdown-menu a {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    color: var(--text-dark);
    text-decoration: none;
    transition: var(--transition);
}

.dropdown-menu a:hover {
    background-color: rgba(76, 175, 80, 0.08);
    color: var(--primary);
}

.dropdown-menu a .material-icons {
    color: var(--primary);
    font-size: 20px;
}

/* Auth buttons */
.auth-buttons {
    display: flex;
    gap: 12px;
    align-items: center;
}

.auth-buttons .btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border-radius: 8px;
    font-weight: 500;
    font-size: 14px;
    transition: var(--transition);
    cursor: pointer;
}

.auth-buttons .login-btn {
    color: var(--primary-dark);
    border: 1px solid var(--primary-light);
    background: transparent;
}

.auth-buttons .login-btn:hover {
    background-color: rgba(76, 175, 80, 0.08);
}

.auth-buttons .register-btn {
    color: white;
    background-color: var(--primary);
    border: 1px solid var(--primary);
}

.auth-buttons .register-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2);
}

/* ===== PAGE HEADER ===== */
.page-header {
    text-align: center;
    padding: 60px 0 40px;
    max-width: 800px;
    margin: 0 auto;
}

.page-header h1 {
    font-size: 2.5rem;
    font-weight: 300;
    color: var(--primary-dark);
    margin-bottom: 15px;
    animation: fadeInDown 0.8s ease forwards;
}

.page-header .subtitle {
    font-size: 1.2rem;
    color: #546E7A;
    margin: 0;
    animation: fadeInUp 0.8s ease 0.2s forwards;
    opacity: 0;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== SEARCH AND FILTERS ===== */
.search-filters {
    background-color: var(--background-alt);
    border-radius: 16px;
    padding: 25px;
    margin-bottom: 30px;
    animation: fadeIn 0.8s ease forwards 0.4s;
    opacity: 0;
    box-shadow: var(--shadow);
}

.search-bar {
    position: relative;
    margin-bottom: 20px;
}

.search-bar .material-icons {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #78909C;
}

.search-bar input {
    width: 100%;
    padding: 14px 16px 14px 50px;
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: 10px;
    font-size: 16px;
    background-color: white;
    transition: var(--transition);
}

.search-bar input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

.filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-group label {
    font-size: 14px;
    font-weight: 500;
    color: #546E7A;
}

.filter-group select {
    padding: 12px 16px;
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: 10px;
    background-color: white;
    font-size: 15px;
    transition: var(--transition);
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%2378909C' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 20px;
}

.filter-group select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 20px;
    border-radius: 10px;
    font-weight: 500;
    font-size: 15px;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.btn .material-icons {
    font-size: 20px;
}

.btn-primary {
    background-color: var(--primary);
    color: white;
    box-shadow: 0 4px 10px rgba(76, 175, 80, 0.2);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(76, 175, 80, 0.3);
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-dark);
    border: 1px solid var(--primary-light);
}

.btn-outline:hover {
    background-color: rgba(76, 175, 80, 0.08);
    transform: translateY(-2px);
}

.btn-outline:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.btn-sm {
    padding: 8px 15px;
    font-size: 14px;
}

/* ===== PLANT GRID ===== */
.plants-container {
    margin-bottom: 50px;
}

.plants-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.plant-card {
    background-color: var(--card-bg);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
    height: 100%;
    display: flex;
    flex-direction: column;
    animation: cardFadeIn 0.6s forwards;
    opacity: 0;
    transform: translateY(20px);
}

.plant-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

@keyframes cardFadeIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.plant-image {
    height: 220px;
    position: relative;
    overflow: hidden;
}

.plant-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.plant-card:hover .plant-image img {
    transform: scale(1.05);
}

.plant-badges {
    position: absolute;
    top: 15px;
    left: 15px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.badge {
    padding: 6px 12px;
    border-radius: 50px;
    font-size: 12px;
    font-weight: 500;
}

.native-badge {
    background-color: var(--primary);
    color: white;
}

.plant-info {
    padding: 25px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

.plant-name {
    font-size: 20px;
    font-weight: 500;
    color: var(--text-dark);
    margin: 0 0 5px;
}

.plant-scientific-name {
    font-style: italic;
    color: #78909C;
    margin: 0 0 15px;
    font-size: 14px;
}

.plant-attributes {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.attribute {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    color: #546E7A;
}

.attribute .material-icons {
    color: var(--primary);
    font-size: 18px;
}

.view-details {
    margin-top: auto;
    text-align: center;
}

/* ===== PAGINATION ===== */
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-top: 40px;
}

.page-info {
    font-size: 16px;
    color: #546E7A;
    font-weight: 500;
}

/* ===== NO RESULTS ===== */
.no-results {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    color: #546E7A;
}

.no-results .material-icons {
    font-size: 60px;
    color: #B0BEC5;
    margin-bottom: 20px;
}

.no-results p {
    font-size: 18px;
    margin: 0;
}

/* ===== FOOTER ===== */
.app-footer {
    background-color: var(--background-alt);
    padding: 80px 0 30px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h3 {
    color: var(--primary-dark);
    margin-bottom: 20px;
    font-weight: 500;
    font-size: 18px;
}

.footer-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul a {
    color: #546E7A;
    text-decoration: none;
    transition: var(--transition);
}

.footer-section ul a:hover {
    color: var(--primary);
    padding-left: 5px;
}

.footer-bottom {
    padding-top: 30px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    text-align: center;
    color: #78909C;
}

/* ===== DARK THEME ===== */
body.dark-theme {
    --background: #121212;
    --background-alt: #1E1E1E;
    --card-bg: #242424;
    --text-dark: #ECEFF1;
    --text-light: #ECEFF1;
    --shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
}

body.dark-theme .search-bar input,
body.dark-theme .filter-group select {
    background-color: #333;
    border-color: #444;
    color: #ECEFF1;
}

body.dark-theme .dropdown-menu::before,
body.dark-theme .dropdown-menu {
    background-color: var(--card-bg);
}

body.dark-theme .footer-section ul a {
    color: #B0BEC5;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.8s forwards;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 992px) {
    .main-nav ul {
        gap: 20px;
    }
    
    .page-header h1 {
        font-size: 2.2rem;
    }
    
    .filters {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }
}

@media (max-width: 768px) {
    .main-nav {
        display: none;
    }
    
    .header-content {
        flex-wrap: wrap;
    }
    
    .plants-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    }
    
    .pagination {
        flex-wrap: wrap;
    }
}

@media (max-width: 576px) {
    .page-header h1 {
        font-size: 1.8rem;
    }
    
    .page-header {
        padding: 40px 0 30px;
    }
    
    .filters {
        grid-template-columns: 1fr;
    }
    
    .plants-grid {
        grid-template-columns: 1fr;
    }
}