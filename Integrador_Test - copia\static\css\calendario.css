/* ===== ESTILOS BASE ===== */
:root {
    --primary: #4CAF50;
    --primary-light: #81C784;
    --primary-dark: #388E3C;
    --accent: #FF9800;
    --text-dark: #263238;
    --text-light: #ECEFF1;
    --background: #FFFFFF;
    --background-alt: #F5F8F5;
    --shadow: 0 8px 30px rgba(0,0,0,0.05);
    --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    --border-radius: 15px;
    --task-type-1: #2196F3; /* Riego - Azul */
    --task-type-2: #8BC34A; /* Fertilizar - Verde claro */
    --task-type-3: #FF5722; /* Podar - Naranja */
    --task-type-4: #9C27B0; /* Trasplantar - Púrpura */
}

/* Dark Theme Variables */
body.dark-theme {
    --primary: #66BB6A;
    --primary-light: #81C784;
    --primary-dark: #388E3C;
    --text-dark: #ECEFF1;
    --text-light: #263238;
    --background: #121212;
    --background-alt: #1E1E1E;
    --shadow: 0 8px 30px rgba(0,0,0,0.2);
    --task-type-1: #42A5F5; /* Riego - Azul */
    --task-type-2: #9CCC65; /* Fertilizar - Verde claro */
    --task-type-3: #FF7043; /* Podar - Naranja */
    --task-type-4: #AB47BC; /* Trasplantar - Púrpura */
}

body {
    font-family: 'Roboto', sans-serif;
    color: var(--text-dark);
    background-color: var(--background);
    margin: 0;
    padding: 0;
    line-height: 1.6;
    overflow-x: hidden;
    transition: background-color 0.3s ease;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* ===== NAVEGACIÓN ===== */
.main-nav {
    background-color: transparent;
    position: absolute;
    width: 100%;
    z-index: 100;
    padding: 20px 0;
    transition: var(--transition);
}

.main-nav.scrolled {
    background-color: var(--background);
    box-shadow: var(--shadow);
    position: fixed;
    top: 0;
    padding: 10px 0;
    animation: slideDown 0.5s;
}

@keyframes slideDown {
    from { transform: translateY(-100%); }
    to { transform: translateY(0); }
}

.main-nav .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo .material-icons {
    color: var(--primary);
    font-size: 28px;
    transition: var(--transition);
}

.main-nav.scrolled .logo .material-icons {
    transform: rotate(180deg);
}

.logo h1, .logo h2 {
    font-size: 24px;
    font-weight: 300;
    color: var(--primary-dark);
    margin: 0;
    letter-spacing: 1px;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 30px;
    margin: 0;
    padding: 0;
}

.nav-links a {
    color: var(--text-dark);
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    padding: 8px 2px;
    position: relative;
    transition: var(--transition);
}

.nav-links a:hover,
.nav-links a.active {
    color: var(--primary);
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.nav-links a:hover::after,
.nav-links a.active::after {
    transform: scaleX(1);
}

.user-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.icon-button {
    background: none;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-dark);
    transition: var(--transition);
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.icon-button:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: var(--primary);
}

.dark-theme .icon-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
    border: 2px solid transparent;
    transition: var(--transition);
    background: none;
    padding: 0;
    position: relative;
}

.avatar:hover {
    border-color: var(--primary);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2);
}

.avatar:active, .avatar.clicked {
    transform: scale(0.95);
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
}

.avatar.clicked {
    animation: avatarPulse 0.3s ease-out;
}

@keyframes avatarPulse {
    0% { transform: scale(0.95); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.avatar::after {
    content: '';
    position: absolute;
    top: -4px;
    right: -4px;
    bottom: -4px;
    left: -4px;
    border-radius: 50%;
    background-color: rgba(76, 175, 80, 0.1);
    opacity: 0;
    transform: scale(0.8);
    transition: var(--transition);
}

.avatar:hover::after {
    opacity: 1;
    transform: scale(1);
}

.avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: relative;
    z-index: 1;
}

.dropdown-menu {
    position: absolute;
    top: 60px;
    right: 20px;
    background-color: var(--background);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    width: 220px;
    z-index: 100;
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
    border: 1px solid rgba(0,0,0,0.05);
    padding: 8px;
}

.dropdown-menu.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    animation: dropdownFadeIn 0.3s ease-out;
}

@keyframes dropdownFadeIn {
    from {
        opacity: 0;
        transform: translateY(-15px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.dropdown-menu a {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    color: var(--text-dark);
    text-decoration: none;
    transition: var(--transition);
    border-radius: 10px;
    margin-bottom: 4px;
    font-weight: 500;
    font-size: 14px;
}

.dropdown-menu a:last-child {
    margin-bottom: 0;
}

.dropdown-menu a:hover {
    background-color: var(--background-alt);
    color: var(--primary);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.dropdown-menu a:active {
    transform: translateY(0);
    box-shadow: none;
}

.dropdown-menu .material-icons {
    font-size: 20px;
    color: var(--primary);
}

.dropdown-menu a:hover .material-icons,
.dropdown-menu .material-icons.icon-hover {
    transform: scale(1.1);
    animation: iconPulse 0.5s ease;
}

@keyframes iconPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1.1); }
}

/* Estilo especial para el botón de cerrar sesión */
.dropdown-menu a#logout-button {
    border-top: 1px solid rgba(0,0,0,0.05);
    margin-top: 4px;
    padding-top: 12px;
    color: #f44336;
}

.dropdown-menu a#logout-button .material-icons {
    color: #f44336;
}

.dropdown-menu a#logout-button:hover {
    background-color: rgba(244, 67, 54, 0.1);
    color: #d32f2f;
}

.dark-theme .dropdown-menu {
    border-color: rgba(255,255,255,0.05);
}

.dark-theme .dropdown-menu a#logout-button {
    border-top-color: rgba(255,255,255,0.05);
}

.auth-buttons {
    display: flex;
    gap: 10px;
}

/* ===== BOTONES ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 10px 20px;
    border-radius: 50px;
    font-weight: 500;
    text-decoration: none;
    text-align: center;
    letter-spacing: 0.5px;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    font-size: 14px;
}

.btn .material-icons {
    font-size: 20px;
}

.btn-primary {
    background-color: var(--primary);
    color: white;
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
}

.btn-outline {
    background-color: transparent;
    color: var(--text-dark);
    box-shadow: inset 0 0 0 2px var(--primary);
}

.btn-outline:hover {
    background-color: rgba(76, 175, 80, 0.1);
    transform: translateY(-2px);
}

.btn-icon {
    width: 40px;
    height: 40px;
    padding: 0;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* ===== HEADER SECTION ===== */
.page-header {
    position: relative;
    height: 300px;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.header-image-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.header-background-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: brightness(0.8);
}

.header-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(56, 142, 60, 0.9) 0%,
        rgba(76, 175, 80, 0.4) 100%
    );
}

.header-content {
    width: 100%;
    position: relative;
    color: var(--text-light);
    text-align: left;
    transform: translateY(30px);
    opacity: 0;
    animation: fadeUp 1s forwards 0.5s;
}

@keyframes fadeUp {
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.header-content h1 {
    font-size: 3rem;
    font-weight: 300;
    line-height: 1.2;
    margin-bottom: 20px;
}

.header-content p {
    font-size: 1.25rem;
    margin: 0;
    opacity: 0.9;
}

/* ===== MAIN CONTENT ===== */
main {
    padding: 50px 0;
    min-height: 100vh;
    background-color: var(--background-alt);
}

.card {
    background-color: var(--background);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
    margin-bottom: 30px;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.dark-theme .card:hover {
    box-shadow: 0 15px 35px rgba(0,0,0,0.3);
}

.card-header {
    padding: 20px;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dark-theme .card-header {
    border-bottom-color: rgba(255,255,255,0.05);
}

.card-header h3 {
    margin: 0;
    font-weight: 500;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-header .material-icons {
    color: var(--primary);
}

.card-actions {
    display: flex;
    gap: 10px;
}

/* ===== LOGIN PROMPT ===== */
.login-prompt {
    display: flex;
    align-items: center;
    padding: 30px;
    margin-bottom: 30px;
}

.login-prompt-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: var(--primary-light);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 30px;
    flex-shrink: 0;
}

.login-prompt-icon .material-icons {
    font-size: 40px;
    color: white;
}

.login-prompt-content {
    flex-grow: 1;
}

.login-prompt-content h3 {
    margin: 0 0 10px;
    font-weight: 500;
    font-size: 20px;
}

.login-prompt-content p {
    margin: 0 0 20px;
    color: var(--text-dark);
    opacity: 0.8;
}

.login-prompt-buttons {
    display: flex;
    gap: 15px;
}

/* ===== CALENDAR STYLES ===== */
.calendar-container {
    padding: 20px;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.calendar-nav {
    display: flex;
    align-items: center;
    gap: 15px;
}

.calendar-nav h2 {
    margin: 0;
    font-weight: 400;
    font-size: 24px;
}

.add-task {
    padding: 10px 20px;
}

.calendar-grid {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.calendar-weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    background-color: var(--primary-light);
    color: white;
    text-align: center;
    font-weight: 500;
    padding: 15px 0;
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background-color: rgba(0,0,0,0.05);
}

.dark-theme .calendar-days {
    background-color: rgba(255,255,255,0.05);
}

.calendar-day {
    background-color: var(--background);
    min-height: 100px;
    padding: 10px;
    position: relative;
    transition: var(--transition);
    cursor: pointer;
}

.calendar-day:hover {
    background-color: rgba(76, 175, 80, 0.05);
}

.calendar-day.today {
    background-color: rgba(76, 175, 80, 0.1);
    font-weight: bold;
}

.calendar-day.selected {
    background-color: rgba(76, 175, 80, 0.2);
}

.calendar-day.other-month {
    opacity: 0.5;
}

.day-number {
    font-size: 16px;
    margin-bottom: 10px;
}

.today .day-number {
    color: var(--primary);
}

.day-tasks {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 5px;
}

.day-task-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--primary);
}

.day-task-dot.task-type-1 {
    background-color: var(--task-type-1);
}

.day-task-dot.task-type-2 {
    background-color: var(--task-type-2);
}

.day-task-dot.task-type-3 {
    background-color: var(--task-type-3);
}

.day-task-dot.task-type-4 {
    background-color: var(--task-type-4);
}

/* ===== TASKS OVERVIEW ===== */
.tasks-overview {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.tasks-card, .stats-card {
    height: 100%;
}

.tasks-list {
    padding: 0;
}

.task-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    transition: var(--transition);
}

.dark-theme .task-item {
    border-bottom-color: rgba(255,255,255,0.05);
}

.task-item:last-child {
    border-bottom: none;
}

.task-item:hover {
    background-color: rgba(0,0,0,0.02);
}

.dark-theme .task-item:hover {
    background-color: rgba(255,255,255,0.02);
}

.task-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
    color: white;
    background-color: var(--primary);
}

.task-icon.task-type-1 {
    background-color: var(--task-type-1);
}

.task-icon.task-type-2 {
    background-color: var(--task-type-2);
}

.task-icon.task-type-3 {
    background-color: var(--task-type-3);
}

.task-icon.task-type-4 {
    background-color: var(--task-type-4);
}

.task-info {
    flex-grow: 1;
}

.task-info h4 {
    margin: 0 0 5px;
    font-weight: 500;
    font-size: 16px;
}

.task-info p {
    margin: 0;
    font-size: 14px;
    color: var(--text-dark);
    opacity: 0.7;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.plant-info {
    display: inline-flex;
    align-items: center;
    color: var(--primary);
    font-weight: 500;
    margin-left: 4px;
}

.plant-info .material-icons {
    font-size: 16px;
    margin-right: 4px;
}

.repeat-icon {
    font-size: 14px;
    margin-left: 8px;
    color: var(--accent);
    vertical-align: middle;
}

.task-actions {
    display: flex;
    gap: 10px;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
    color: var(--text-dark);
    opacity: 0.6;
}

.empty-state .material-icons {
    font-size: 48px;
    margin-bottom: 15px;
}

.empty-state p {
    margin: 0;
    font-size: 16px;
}

/* ===== STATS CARD ===== */
.stats-content {
    padding: 20px;
}

.stat-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.stat-item:last-child {
    margin-bottom: 0;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
    color: white;
    background-color: var(--primary);
}

.stat-icon.task-type-1 {
    background-color: var(--task-type-1);
}

.stat-icon.task-type-2 {
    background-color: var(--task-type-2);
}

.stat-icon.task-type-3 {
    background-color: var(--task-type-3);
}

.stat-icon.task-type-4 {
    background-color: var(--task-type-4);
}

.stat-info h4 {
    margin: 0 0 5px;
    font-weight: 500;
    font-size: 16px;
}

.stat-info p {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
}

/* ===== UPCOMING TASKS ===== */
.upcoming-tasks {
    margin-bottom: 50px;
}

/* ===== MODAL ===== */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background-color: var(--background);
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 15px 50px rgba(0,0,0,0.2);
    transform: translateY(-50px);
    transition: var(--transition);
}

.modal.active .modal-content {
    transform: translateY(0);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    background-color: var(--background);
    z-index: 1;
}

.dark-theme .modal-header {
    border-bottom-color: rgba(255,255,255,0.05);
}

.modal-header h3 {
    margin: 0;
    font-weight: 500;
    font-size: 20px;
}

/* ===== FORM STYLES ===== */
form {
    padding: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-dark);
}

.input-with-icon {
    position: relative;
}

.input-with-icon .material-icons {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary);
    font-size: 20px;
}

.textarea-icon .material-icons {
    top: 20px;
    transform: none;
}

.input-with-icon input,
.input-with-icon select,
.input-with-icon textarea {
    padding-left: 40px;
}

input, select, textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid rgba(0,0,0,0.1);
    border-radius: 8px;
    font-size: 16px;
    color: var(--text-dark);
    background-color: var(--background);
    transition: var(--transition);
}

.dark-theme input,
.dark-theme select,
.dark-theme textarea {
    border-color: rgba(255,255,255,0.1);
    background-color: rgba(255,255,255,0.05);
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

textarea {
    height: 120px;
    resize: vertical;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 30px;
}

/* ===== NOTIFICACIONES ===== */
#notification-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.notification {
    background-color: var(--background);
    color: var(--text-dark);
    border-radius: var(--border-radius);
    padding: 15px 20px;
    margin-bottom: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    width: 300px;
    animation: slideInRight 0.3s ease, fadeOut 0.5s ease 2.5s forwards;
}

.notification.success {
    border-left: 4px solid var(--primary);
}

.notification.error {
    border-left: 4px solid #F44336;
}

.notification.warning {
    border-left: 4px solid #FFC107;
}

.notification .material-icons {
    font-size: 24px;
}

.notification.success .material-icons {
    color: var(--primary);
}

.notification.error .material-icons {
    color: #F44336;
}

.notification.warning .material-icons {
    color: #FFC107;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

/* ===== FOOTER ===== */
.app-footer {
    background-color: var(--background);
    padding: 80px 0 30px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h3 {
    color: var(--primary-dark);
    margin-bottom: 20px;
    font-weight: 500;
    font-size: 18px;
}

.footer-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul a {
    color: var(--text-dark);
    opacity: 0.7;
    text-decoration: none;
    transition: var(--transition);
}

.footer-section ul a:hover {
    color: var(--primary);
    opacity: 1;
    padding-left: 5px;
}

.footer-bottom {
    padding-top: 30px;
    border-top: 1px solid rgba(0,0,0,0.05);
    text-align: center;
    color: var(--text-dark);
    opacity: 0.7;
}

.dark-theme .footer-bottom {
    border-top-color: rgba(255,255,255,0.05);
}

/* ===== ANIMACIONES ===== */
.scroll-reveal {
    opacity: 0;
    transform: translateY(40px);
    transition: all 1s ease;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

.spin {
    animation: spin 1s infinite linear;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* ===== MEDIA QUERIES ===== */
@media (max-width: 992px) {
    .page-header {
        height: 250px;
    }

    .header-content h1 {
        font-size: 2.5rem;
    }

    .tasks-overview {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .page-header {
        height: 200px;
    }

    .header-content h1 {
        font-size: 2rem;
    }

    .header-content p {
        font-size: 1rem;
    }

    .nav-links {
        display: none;
    }

    .login-prompt {
        flex-direction: column;
        text-align: center;
    }

    .login-prompt-icon {
        margin: 0 0 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    .calendar-day {
        min-height: 80px;
        padding: 5px;
    }

    .day-number {
        font-size: 14px;
    }

    .task-item {
        padding: 10px 15px;
    }

    .footer-content {
        grid-template-columns: 1fr;
    }
}

/* ===== MOBILE MENU ===== */
.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-dark);
}

@media (max-width: 768px) {
    .mobile-menu-btn {
        display: block;
    }

    .nav-links.mobile-visible {
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 80px;
        left: 0;
        right: 0;
        background: var(--background);
        box-shadow: var(--shadow);
        padding: 20px;
        z-index: 100;
    }
}