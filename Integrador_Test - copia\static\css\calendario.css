* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 50%, #1B5E20 100%);
    min-height: 100vh;
    color: white;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Navegación */
.main-nav {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 15px 0;
    margin-bottom: 30px;
    border-radius: 15px;
}

.nav-links {
    display: flex;
    justify-content: center;
    list-style: none;
    gap: 30px;
    flex-wrap: wrap;
}

.nav-links a {
    color: white;
    text-decoration: none;
    padding: 10px 20px;
    border-radius: 25px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-links a:hover,
.nav-links a.active {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* <PERSON>er del calendario */
.calendar-header {
    text-align: center;
    margin-bottom: 40px;
}

.calendar-header h1 {
    font-size: 3rem;
    font-weight: 300;
    margin-bottom: 10px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.calendar-header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Calendario principal */
.calendar-main {
    margin-bottom: 40px;
}

.calendar-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.calendar-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.control-btn {
    background: #4CAF50;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: white;
}

.control-btn:hover {
    background: #2E7D32;
    transform: scale(1.1);
}

#current-month-year {
    font-size: 2rem;
    font-weight: 400;
    text-align: center;
    flex: 1;
    color: #2E7D32;
}

.calendar-grid {
    width: 100%;
}

.weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 10px;
    margin-bottom: 15px;
}

.weekday {
    text-align: center;
    font-weight: 500;
    padding: 15px 0;
    background: #f5f5f5;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #2E7D32;
}

.days-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 10px;
}

.day {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f9f9f9;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    font-weight: 500;
    min-height: 60px;
    color: #333;
    border: 2px solid transparent;
}

.day:hover {
    background: #e8f5e8;
    transform: scale(1.05);
    border-color: #4CAF50;
}

.day.today {
    background: #4CAF50;
    color: white;
    box-shadow: 0 0 20px rgba(76, 175, 80, 0.3);
    font-weight: 700;
}

.day.other-month {
    opacity: 0.3;
    color: #999;
}

.day.has-tasks::after {
    content: '';
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 8px;
    height: 8px;
    background: #FFD700;
    border-radius: 50%;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.day.agricultural::before {
    content: '🌾';
    position: absolute;
    top: 2px;
    left: 2px;
    font-size: 12px;
}

/* Acciones del calendario */
.calendar-actions {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    justify-content: center;
}

.add-event-btn, .toggle-btn {
    background: #4CAF50;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.add-event-btn:hover, .toggle-btn:hover {
    background: #2E7D32;
    transform: translateY(-2px);
}

.toggle-btn.active {
    background: #FF9800;
}

/* Modal para agregar eventos */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 20px;
    padding: 30px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.modal-header h3 {
    color: #2E7D32;
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: #f5f5f5;
    color: #333;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #2E7D32;
    font-weight: 500;
}

.form-group input, .form-group select, .form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.form-group input:focus, .form-group select:focus, .form-group textarea:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 10px rgba(76, 175, 80, 0.2);
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 25px;
}

.btn-cancel {
    background: #f5f5f5;
    color: #666;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-cancel:hover {
    background: #e0e0e0;
}

.btn-save {
    background: #4CAF50;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-save:hover {
    background: #2E7D32;
}

/* Información agrícola */
.agricultural-info {
    margin-bottom: 30px;
}

.agricultural-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.agricultural-card h3 {
    color: white;
    margin-bottom: 20px;
    font-size: 1.4rem;
}

.agricultural-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.agricultural-section h4 {
    color: white;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.agricultural-section ul {
    list-style: none;
    padding: 0;
}

.agricultural-section li {
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 12px;
    margin-bottom: 5px;
    border-radius: 8px;
    color: white;
}

.day.selected {
    background: #FF9800 !important;
    color: white;
    transform: scale(1.1);
}

/* Sección de tareas */
.tasks-section {
    margin-top: 40px;
}

.tasks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.task-card, .stats-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.task-header h3 {
    font-size: 1.3rem;
    font-weight: 500;
}

.task-count {
    background: rgba(255, 255, 255, 0.2);
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 600;
}

.task-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.task-item {
    display: flex;
    align-items: center;
    gap: 15px;
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.task-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(5px);
}

.task-icon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
}

.task-info {
    flex: 1;
}

.task-info h4 {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 5px;
}

.task-info p {
    font-size: 0.9rem;
    opacity: 0.8;
}

.task-complete {
    background: rgba(76, 175, 80, 0.3);
    border: 2px solid rgba(76, 175, 80, 0.5);
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: white;
    font-weight: bold;
}

.task-complete:hover {
    background: rgba(76, 175, 80, 0.5);
    transform: scale(1.1);
}

/* Estadísticas */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 15px;
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 12px;
}

.stat-icon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
}

.stat-info {
    display: flex;
    flex-direction: column;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Colores por tipo de tarea */
.riego .task-icon, .riego .stat-icon {
    background: rgba(33, 150, 243, 0.3);
}

.fertilizar .task-icon, .fertilizar .stat-icon {
    background: rgba(76, 175, 80, 0.3);
}

.podar .task-icon, .podar .stat-icon {
    background: rgba(255, 152, 0, 0.3);
}

.trasplantar .task-icon, .trasplantar .stat-icon {
    background: rgba(156, 39, 176, 0.3);
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .calendar-header h1 {
        font-size: 2rem;
    }

    .calendar-card {
        padding: 20px;
    }

    #current-month-year {
        font-size: 1.5rem;
    }

    .tasks-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .nav-links {
        gap: 15px;
    }

    .nav-links a {
        padding: 8px 15px;
        font-size: 0.9rem;
    }
}
