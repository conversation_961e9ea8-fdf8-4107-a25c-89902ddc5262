/* ===== SISTEMA DE CLUBES ===== */

/* Layout principal */
.clubs-container {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 32px;
    margin-top: 32px;
}

/* Sidebar */
.clubs-sidebar {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.sidebar-section {
    background: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
}

.sidebar-section h3 {
    margin: 0 0 16px 0;
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
}

/* Mis clubes */
.my-clubs {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 16px;
}

.my-club-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.my-club-item:hover {
    background: #f9fafb;
}

.club-mini-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #e5e7eb;
}

.club-mini-info {
    display: flex;
    flex-direction: column;
}

.club-mini-name {
    font-weight: 600;
    font-size: 14px;
    color: #1f2937;
}

.club-mini-members {
    font-size: 12px;
    color: #6b7280;
}

/* Filtros de categoría */
.category-filters {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.category-filter {
    background: none;
    border: none;
    padding: 12px 16px;
    text-align: left;
    border-radius: 8px;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.category-filter:hover,
.category-filter.active {
    background: #059669;
    color: white;
}

/* Estadísticas */
.stats-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
}

.stat-item {
    text-align: center;
    padding: 16px;
    background: #f9fafb;
    border-radius: 12px;
}

.stat-number {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: #059669;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Contenido principal */
.clubs-content {
    display: flex;
    flex-direction: column;
    gap: 32px;
}

/* Acciones de clubes */
.clubs-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 20px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
}

.search-box {
    display: flex;
    flex: 1;
    max-width: 400px;
    margin-right: 20px;
}

.search-box input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #e5e7eb;
    border-radius: 12px 0 0 12px;
    outline: none;
    font-size: 16px;
}

.search-box button {
    padding: 12px 16px;
    background: #059669;
    color: white;
    border: none;
    border-radius: 0 12px 12px 0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-box button:hover {
    background: #047857;
}

/* Sección de clubes destacados */
.featured-clubs h2 {
    margin: 0 0 24px 0;
    font-size: 28px;
    font-weight: 700;
    color: #1f2937;
}

/* Grid de clubes */
.clubs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 24px;
}

/* Tarjeta de club */
.club-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.club-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.club-card.featured {
    border: 2px solid #059669;
    background: linear-gradient(135deg, 
        rgba(5, 150, 105, 0.02) 0%, 
        rgba(16, 185, 129, 0.02) 100%);
}

/* Header del club */
.club-header {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.club-avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.4s ease;
}

.club-card:hover .club-avatar {
    transform: scale(1.05);
}

.club-badge {
    position: absolute;
    top: 16px;
    right: 16px;
    background: linear-gradient(135deg, #f59e0b, #f97316);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.club-badge .material-icons {
    font-size: 16px;
}

/* Información del club */
.club-info {
    padding: 24px;
}

.club-info h3 {
    margin: 0 0 12px 0;
    font-size: 20px;
    font-weight: 700;
    color: #1f2937;
    line-height: 1.3;
}

.club-description {
    margin: 0 0 20px 0;
    color: #6b7280;
    line-height: 1.6;
    font-size: 14px;
}

/* Estadísticas del club */
.club-stats {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 16px;
}

.club-stats .stat {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: #6b7280;
}

.club-stats .material-icons {
    font-size: 16px;
    color: #9ca3af;
}

/* Tags del club */
.club-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 20px;
}

.club-tags .tag {
    background: #f3f4f6;
    color: #6b7280;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 11px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
}

.club-tags .tag:hover {
    background: #059669;
    color: white;
}

/* Acciones del club */
.club-actions {
    display: flex;
    gap: 12px;
    padding: 0 24px 24px 24px;
}

.club-actions .btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 16px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    text-decoration: none;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: linear-gradient(135deg, #059669, #10b981);
    color: white;
    box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #047857, #059669);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(5, 150, 105, 0.4);
}

.btn-outline {
    background: white;
    color: #6b7280;
    border: 1px solid #e5e7eb;
}

.btn-outline:hover {
    background: #f9fafb;
    color: #374151;
    border-color: #d1d5db;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 13px;
}

/* Estados de botones */
.join-club.joined {
    background: #10b981;
    color: white;
}

.join-club.joined .material-icons {
    content: 'check';
}

/* Responsive */
@media (max-width: 1024px) {
    .clubs-container {
        grid-template-columns: 1fr;
        gap: 24px;
    }
    
    .clubs-sidebar {
        order: 2;
        flex-direction: row;
        overflow-x: auto;
    }
    
    .sidebar-section {
        min-width: 250px;
    }
    
    .clubs-content {
        order: 1;
    }
}

@media (max-width: 768px) {
    .clubs-actions {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .search-box {
        max-width: none;
        margin-right: 0;
    }
    
    .clubs-grid {
        grid-template-columns: 1fr;
    }
    
    .clubs-sidebar {
        flex-direction: column;
    }
    
    .sidebar-section {
        min-width: auto;
    }
    
    .club-actions {
        flex-direction: column;
    }
}

/* Animaciones */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.club-card {
    animation: slideInUp 0.6s ease-out;
}

.club-card:nth-child(1) { animation-delay: 0.1s; }
.club-card:nth-child(2) { animation-delay: 0.2s; }
.club-card:nth-child(3) { animation-delay: 0.3s; }
.club-card:nth-child(4) { animation-delay: 0.4s; }

/* Efectos hover adicionales */
.club-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, 
        rgba(5, 150, 105, 0.05) 0%, 
        rgba(16, 185, 129, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.club-card:hover::before {
    opacity: 1;
}

/* Modal de bienvenida */
.welcome-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.welcome-content {
    background: white;
    border-radius: 20px;
    padding: 32px;
    max-width: 400px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.welcome-header {
    margin-bottom: 20px;
}

.welcome-header .material-icons {
    font-size: 48px;
    color: #f59e0b;
    margin-bottom: 12px;
}

.welcome-header h3 {
    margin: 0;
    color: #1f2937;
    font-size: 20px;
}

.welcome-content ul {
    text-align: left;
    margin: 20px 0;
    padding: 0;
    list-style: none;
}

.welcome-content li {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 8px 0;
    color: #374151;
}

.welcome-content li .material-icons {
    color: #10b981;
    font-size: 18px;
}

.welcome-actions {
    margin-top: 24px;
}

/* Modal de crear club */
.create-club-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.modal-content {
    background: white;
    border-radius: 20px;
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 24px 0 24px;
    margin-bottom: 20px;
}

.modal-header h3 {
    margin: 0;
    color: #1f2937;
    font-size: 20px;
}

.close-modal {
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.close-modal:hover {
    background: #f3f4f6;
    color: #6b7280;
}

.create-club-form {
    padding: 0 24px 24px 24px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #059669;
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
}

.form-group textarea {
    min-height: 100px;
    resize: vertical;
}

.form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
}

/* Notificaciones */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 12px;
    padding: 16px 20px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 12px;
    z-index: 1001;
    transform: translateX(400px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-left: 4px solid #6b7280;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left-color: #10b981;
}

.notification.success .material-icons {
    color: #10b981;
}

.notification.error {
    border-left-color: #ef4444;
}

.notification.error .material-icons {
    color: #ef4444;
}

.notification.info {
    border-left-color: #3b82f6;
}

.notification.info .material-icons {
    color: #3b82f6;
}

/* Animaciones */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes rotating {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.rotating {
    animation: rotating 1s linear infinite;
}

/* Highlights de búsqueda */
mark {
    background: #fef3c7;
    color: #92400e;
    padding: 2px 4px;
    border-radius: 4px;
}
