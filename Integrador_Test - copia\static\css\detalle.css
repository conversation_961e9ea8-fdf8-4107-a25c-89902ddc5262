/* Base Styles */
:root {
    --primary-color: #4caf50;
    --primary-light: #80e27e;
    --primary-dark: #087f23;
    --secondary-color: #2196f3;
    --text-color: #333333;
    --text-light: #757575;
    --background-color: #ffffff;
    --background-light: #f5f5f5;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --border-radius: 8px;
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Dark Theme */
.dark-theme {
    --primary-color: #81c784;
    --primary-light: #b2fab4;
    --primary-dark: #519657;
    --secondary-color: #64b5f6;
    --text-color: #f5f5f5;
    --text-light: #b0b0b0;
    --background-color: #121212;
    --background-light: #1e1e1e;
    --border-color: #333333;
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--background-light);
    transition: var(--transition);
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    border: none;
    outline: none;
    text-decoration: none;
    gap: 8px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.icon-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: transparent;
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.icon-button:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.dark-theme .icon-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Header */
.app-header {
    background-color: var(--background-color);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
    padding: 12px 0;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--primary-color);
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 500;
}

.main-nav ul {
    display: flex;
    list-style: none;
    gap: 24px;
}

.main-nav a {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
    transition: var(--transition);
    padding: 8px 0;
    position: relative;
}

.main-nav a:hover,
.main-nav a.active {
    color: var(--primary-color);
}

.main-nav a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: var(--transition);
}

.main-nav a:hover::after,
.main-nav a.active::after {
    width: 100%;
}

.user-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
    border: none;
    background: none;
    padding: 0;
}

.avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--background-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    min-width: 200px;
    padding: 8px 0;
    z-index: 1000;
    display: none;
}

.dropdown-menu.active {
    display: block;
}

.dropdown-menu a {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
}

.dropdown-menu a:hover {
    background-color: var(--background-light);
    color: var(--primary-color);
}

.user-menu {
    position: relative;
}

/* Footer */
.app-footer {
    background-color: var(--background-color);
    padding: 48px 0 24px;
    margin-top: 64px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 32px;
    margin-bottom: 32px;
}

.footer-section h3 {
    color: var(--primary-color);
    margin-bottom: 16px;
    font-weight: 500;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-section a {
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 24px;
    border-top: 1px solid var(--border-color);
    color: var(--text-light);
}

/* Plant Detail Specific Styles */
.back-button {
    margin: 24px 0;
}

.plant-detail-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 32px;
    margin-bottom: 48px;
    background-color: var(--background-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.plant-gallery {
    padding: 24px;
}

.main-image {
    position: relative;
    margin-bottom: 16px;
    border-radius: var(--border-radius);
    overflow: hidden;
    height: 400px;
}

.main-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.plant-badges {
    position: absolute;
    top: 16px;
    left: 16px;
    display: flex;
    gap: 8px;
}

.badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.native-badge {
    background-color: var(--primary-color);
    color: white;
}

.category-badge {
    background-color: var(--secondary-color);
    color: white;
}

.thumbnail-gallery {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 8px;
}

.thumbnail {
    height: 80px;
    border-radius: 4px;
    overflow: hidden;
    cursor: pointer;
    border: 2px solid transparent;
    transition: var(--transition);
}

.thumbnail:hover, .thumbnail.active {
    border-color: var(--primary-color);
}

.thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.plant-info-container {
    padding: 32px;
}

.plant-title {
    font-size: 2rem;
    margin-bottom: 8px;
    color: var(--text-color);
}

.scientific-name {
    font-style: italic;
    color: var(--text-light);
    margin-bottom: 24px;
}

.plant-attributes {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    margin-bottom: 32px;
}

.attribute-card {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background-color: var(--background-light);
    border-radius: var(--border-radius);
}

.attribute-card .material-icons {
    font-size: 2rem;
    color: var(--primary-color);
}

.attribute-info h3 {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 4px;
}

.plant-actions {
    display: flex;
    gap: 16px;
}

/* Tabs Styles */
.plant-details-tabs {
    margin-bottom: 48px;
    background-color: var(--background-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.tabs-header {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--background-light);
}

.tab-button {
    padding: 16px 24px;
    background: none;
    border: none;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-color);
    position: relative;
}

.tab-button:hover {
    color: var(--primary-color);
}

.tab-button.active {
    color: var(--primary-color);
}

.tab-button.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--primary-color);
}

.tabs-content {
    padding: 32px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.tab-content h2 {
    margin-bottom: 24px;
    color: var(--primary-color);
}

.plant-characteristics, .plant-habitat, .care-section, .tips-section {
    margin-bottom: 32px;
}

.plant-characteristics h3, .plant-habitat h3, .care-section h3, .tips-section h3 {
    margin-bottom: 16px;
    font-weight: 500;
}

.plant-characteristics ul {
    list-style-position: inside;
    margin-left: 16px;
}

.plant-characteristics li {
    margin-bottom: 8px;
}

/* Seasons Tab */
.seasons-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
}

.season-card {
    padding: 24px;
    background-color: var(--background-light);
    border-radius: var(--border-radius);
    text-align: center;
}

.season-card h3 {
    margin-bottom: 16px;
}

.season-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 64px;
    height: 64px;
    margin: 0 auto 16px;
    background-color: rgba(76, 175, 80, 0.1);
    border-radius: 50%;
}

.season-icon .material-icons {
    font-size: 32px;
    color: var(--primary-color);
}

/* Problems and Tips */
.problems-list {
    display: grid;
    gap: 16px;
}

.problem-card {
    background-color: var(--background-light);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.problem-header {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background-color: rgba(244, 67, 54, 0.1);
}

.problem-icon {
    color: var(--danger-color);
}

.problem-content {
    padding: 16px;
}

.problem-description {
    margin-bottom: 16px;
}

.problem-solution h5 {
    margin-bottom: 8px;
    color: var(--success-color);
}

.tips-list {
    list-style-position: inside;
    margin-left: 16px;
}

.tips-list li {
    margin-bottom: 12px;
}

/* Related Plants */
.related-plants h2 {
    margin-bottom: 24px;
    color: var(--primary-color);
}

.related-plants-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
}

.related-plant-card {
    background-color: var(--background-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
    cursor: pointer;
}

.related-plant-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.related-plant-image {
    height: 160px;
}

.related-plant-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.related-plant-info {
    padding: 16px;
}

.related-plant-name {
    font-size: 1rem;
    margin-bottom: 4px;
}

.related-plant-scientific-name {
    font-size: 0.875rem;
    font-style: italic;
    color: var(--text-light);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .plant-detail-container {
        grid-template-columns: 1fr;
    }
    
    .related-plants-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .seasons-container {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 16px;
    }
    
    .main-nav ul {
        gap: 16px;
    }
    
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .plant-attributes {
        grid-template-columns: 1fr;
    }
    
    .plant-actions {
        flex-direction: column;
    }
    
    .related-plants-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .tabs-header {
        overflow-x: auto;
        white-space: nowrap;
    }
}

@media (max-width: 576px) {
    .related-plants-grid {
        grid-template-columns: 1fr;
    }
    
    .seasons-container {
        grid-template-columns: 1fr;
    }
    
    .main-nav ul {
        flex-wrap: wrap;
        justify-content: center;
    }
}
