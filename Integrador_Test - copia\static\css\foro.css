/* Base Styles */
:root {
    --primary-color: #4caf50;
    --primary-light: #80e27e;
    --primary-dark: #087f23;
    --secondary-color: #2196f3;
    --text-color: #333333;
    --text-light: #757575;
    --background-color: #ffffff;
    --background-light: #f5f5f5;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --border-radius: 8px;
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Dark Theme */
.dark-theme {
    --primary-color: #81c784;
    --primary-light: #b2fab4;
    --primary-dark: #519657;
    --secondary-color: #64b5f6;
    --text-color: #f5f5f5;
    --text-light: #b0b0b0;
    --background-color: #121212;
    --background-light: #1e1e1e;
    --border-color: #333333;
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    color: var(--text-color);
    background-color: var(--background-light);
    line-height: 1.6;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 16px;
}

/* Header Styles */
.app-header {
    background-color: var(--background-color);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--primary-color);
}

.logo h1 {
    font-size: 1.5rem;
}

.main-nav ul {
    display: flex;
    list-style: none;
    gap: 24px;
}

.main-nav a {
    text-decoration: none;
    color: var(--text-color);
    padding: 8px 0;
    position: relative;
    font-weight: 500;
    transition: var(--transition);
}

.main-nav a:hover {
    color: var(--primary-color);
}

.main-nav a.active {
    color: var(--primary-color);
}

.main-nav a.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--primary-color);
}

.user-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.icon-button {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: var(--transition);
}

.icon-button:hover {
    background-color: var(--background-light);
    color: var(--primary-color);
}

.user-menu {
    position: relative;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
    padding: 0;
    border: none;
    background: none;
}

.avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.dropdown-menu {
    position: absolute;
    top: 50px;
    right: 0;
    background-color: var(--background-color);
    box-shadow: var(--shadow);
    border-radius: var(--border-radius);
    width: 200px;
    overflow: hidden;
    display: none;
    z-index: 10;
}

.dropdown-menu.active {
    display: block;
}

.dropdown-menu a {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    text-decoration: none;
    color: var(--text-color);
    transition: var(--transition);
}

.dropdown-menu a:hover {
    background-color: var(--background-light);
}

.dropdown-menu a.active {
    color: var(--primary-color);
    background-color: var(--background-light);
}

/* Page Specific Styles */

main {
    padding: 32px 0;
}

.page-header {
    text-align: center;
    margin-bottom: 32px;
}

.page-header h1 {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 8px;
}

.subtitle {
    color: var(--text-light);
    font-size: 1.1rem;
}

/* Forum Styles */
.forum-container {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 24px;
    margin-bottom: 32px;
}

.forum-sidebar {
    background-color: var(--background-color);
    border-radius: var(--border-radius);
    padding: 24px;
    box-shadow: var(--shadow);
}

.sidebar-section {
    margin-bottom: 24px;
}

.sidebar-section:last-child {
    margin-bottom: 0;
}

.sidebar-section h3 {
    margin-bottom: 16px;
    color: var(--primary-color);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 8px;
}

.category-list,
.popular-tags,
.filter-options {
    list-style: none;
}

.category-list li,
.filter-options li {
    margin-bottom: 8px;
}

.category-list a,
.filter-options a {
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    color: var(--text-color);
    padding: 8px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.category-list a:hover,
.filter-options a:hover,
.category-list a.active,
.filter-options a.active {
    background-color: var(--background-light);
    color: var(--primary-color);
}

.tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tag {
    display: inline-block;
    padding: 6px 12px;
    background-color: var(--background-light);
    color: var(--text-color);
    border-radius: 20px;
    font-size: 0.875rem;
    text-decoration: none;
    transition: var(--transition);
}

.tag:hover {
    background-color: var(--primary-light);
    color: var(--text-color);
}

.forum-content {
    background-color: var(--background-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.forum-actions {
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
}

.search-box {
    display: flex;
    align-items: center;
    flex: 1;
    max-width: 400px;
    position: relative;
}

.search-box input {
    width: 100%;
    padding: 8px 40px 8px 16px;
    border: 1px solid var(--border-color);
    border-radius: 20px;
    outline: none;
    background-color: var(--background-light);
    color: var(--text-color);
    transition: var(--transition);
}

.search-box input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
}

.search-box .icon-button {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
}

.btn {
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    border: none;
    outline: none;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-color);
}

.btn-outline:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.topic-filters {
    display: flex;
    gap: 16px;
}

.forum-topics {
    padding: 0 16px;
}

.topic-item {
    padding: 16px 0;
    display: flex;
    gap: 16px;
    border-bottom: 1px solid var(--border-color);
}

.topic-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.topic-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.topic-content {
    flex: 1;
}

.topic-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.topic-author {
    font-size: 0.875rem;
    color: var(--text-light);
}

.topic-date {
    font-size: 0.875rem;
    color: var(--text-light);
}

.topic-title {
    font-size: 1.125rem;
    margin-bottom: 8px;
}

.topic-title a {
    text-decoration: none;
    color: var(--text-color);
    transition: var(--transition);
}

.topic-title a:hover {
    color: var(--primary-color);
}

.topic-excerpt {
    color: var(--text-light);
    margin-bottom: 12px;
}

.topic-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.topic-tags {
    display: flex;
    gap: 8px;
}

.topic-stats {
    display: flex;
    gap: 16px;
    color: var(--text-light);
    font-size: 0.875rem;
}

.stat {
    display: flex;
    align-items: center;
    gap: 4px;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    padding: 24px 0;
}

.pagination-btn {
    background: none;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    border-radius: 50%;
    transition: var(--transition);
}

.pagination-btn:hover:not(:disabled) {
    background-color: var(--background-light);
    color: var(--primary-color);
}

.pagination-btn:disabled {
    color: var(--border-color);
    cursor: not-allowed;
}

.page-nums {
    display: flex;
    gap: 8px;
}

.page-num {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: none;
    border: none;
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
}

.page-num:hover:not(.active) {
    background-color: var(--background-light);
}

.page-num.active {
    background-color: var(--primary-color);
    color: white;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 24px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background-color: var(--background-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: var(--primary-color);
}

.modal form {
    padding: 16px;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--background-light);
    color: var(--text-color);
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    margin-top: 24px;
}

.file-upload {
    position: relative;
    display: inline-block;
}

.file-upload input {
    position: absolute;
    left: -9999px;
}

.file-upload-label {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    border: 1px dashed var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.file-upload-label:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.image-preview {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-top: 12px;
}

/* Footer Styles */
.app-footer {
    background-color: var(--background-color);
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    margin-top: 48px;
    padding: 48px 0 0;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 32px;
    margin-bottom: 32px;
}

.footer-section h3 {
    margin-bottom: 16px;
    color: var(--primary-color);
}

.footer-section p {
    color: var(--text-light);
    margin-bottom: 16px;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-section ul a {
    text-decoration: none;
    color: var(--text-color);
    transition: var(--transition);
}

.footer-section ul a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding: 16px 0;
    border-top: 1px solid var(--border-color);
    color: var(--text-light);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .forum-container {
        grid-template-columns: 1fr;
    }
    
    .forum-sidebar {
        order: 2;
    }
    
    .forum-content {
        order: 1;
    }
}

@media (max-width: 768px) {
    .main-nav {
        display: none;
    }
    
    .forum-actions {
        flex-direction: column;
        gap: 16px;
    }
    
    .search-box {
        max-width: 100%;
    }
    
    .topic-filters {
        flex-wrap: wrap;
    }
    
    .pagination {
        flex-wrap: wrap;
        gap: 8px;
    }
}

@media (max-width: 576px) {
    .topic-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .topic-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .header-content {
        flex-wrap: wrap;
        gap: 16px;
    }
    
    .user-actions {
        margin-left: auto;
    }
}
