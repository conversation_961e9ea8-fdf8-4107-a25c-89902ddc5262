/* ===== FORO MODERNO CON IMÁGENES ===== */

/* Crear nueva publicación */
.create-post-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 24px;
    padding: 20px;
    border: 1px solid #e5e7eb;
}

.create-post-header {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.user-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #e5e7eb;
}

.create-post-input {
    flex: 1;
}

.create-post-input textarea {
    width: 100%;
    min-height: 80px;
    border: none;
    outline: none;
    resize: vertical;
    font-size: 16px;
    font-family: inherit;
    background: #f9fafb;
    border-radius: 12px;
    padding: 16px;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
}

.create-post-input textarea:focus {
    border-color: #059669;
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
}

.create-post-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 16px;
    border-top: 1px solid #e5e7eb;
}

.post-options {
    display: flex;
    gap: 16px;
}

.post-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: none;
    border: none;
    border-radius: 8px;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.post-option:hover {
    background: #f3f4f6;
    color: #059669;
}

.post-submit {
    display: flex;
    align-items: center;
    gap: 12px;
}

.category-select {
    padding: 8px 12px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background: white;
    color: #6b7280;
    font-size: 14px;
}

/* Área de carga de imágenes */
.image-upload-area {
    margin-top: 16px;
    padding: 16px;
    background: #f9fafb;
    border-radius: 12px;
    border: 2px dashed #d1d5db;
}

.upload-zone {
    text-align: center;
    padding: 32px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-zone:hover {
    border-color: #059669;
    background: rgba(5, 150, 105, 0.05);
}

.upload-zone .material-icons {
    font-size: 48px;
    color: #9ca3af;
    margin-bottom: 12px;
}

.upload-zone p {
    margin: 8px 0 4px 0;
    color: #6b7280;
    font-weight: 500;
}

.upload-zone small {
    color: #9ca3af;
}

.image-preview-container {
    display: flex;
    gap: 12px;
    margin-top: 16px;
    flex-wrap: wrap;
}

.preview-image {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: 8px;
    overflow: hidden;
}

.preview-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.remove-image {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

/* Filtros del foro */
.forum-filters {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 24px;
    padding: 20px;
    border: 1px solid #e5e7eb;
}

.search-box {
    display: flex;
    margin-bottom: 16px;
}

.search-box input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #e5e7eb;
    border-radius: 12px 0 0 12px;
    outline: none;
    font-size: 16px;
}

.search-box button {
    padding: 12px 16px;
    background: #059669;
    color: white;
    border: none;
    border-radius: 0 12px 12px 0;
    cursor: pointer;
}

.filter-tabs {
    display: flex;
    gap: 8px;
}

.filter-tab {
    padding: 8px 16px;
    background: none;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.filter-tab.active,
.filter-tab:hover {
    background: #059669;
    color: white;
    border-color: #059669;
}

/* Feed de publicaciones */
.posts-feed {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.post-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
    overflow: hidden;
    transition: all 0.3s ease;
}

.post-card:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.post-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 0 20px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-details h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
}

.post-time {
    font-size: 14px;
    color: #6b7280;
}

.menu-btn {
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.menu-btn:hover {
    background: #f3f4f6;
    color: #6b7280;
}

.post-content {
    padding: 16px 20px;
}

.post-content p {
    margin: 0 0 12px 0;
    line-height: 1.6;
    color: #374151;
}

.post-content ul {
    margin: 12px 0;
    padding-left: 20px;
}

.post-content li {
    margin: 4px 0;
    color: #374151;
}

.post-tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-top: 12px;
}

.tag {
    background: #f3f4f6;
    color: #6b7280;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
}

.tag:hover {
    background: #059669;
    color: white;
}

/* Imágenes en publicaciones */
.post-images {
    margin: 16px 0;
}

.image-grid {
    display: grid;
    gap: 4px;
    border-radius: 12px;
    overflow: hidden;
}

.image-grid.single {
    grid-template-columns: 1fr;
    max-height: 400px;
}

.image-grid.double {
    grid-template-columns: 1fr 1fr;
    max-height: 300px;
}

.image-grid.multiple {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    max-height: 400px;
    position: relative;
}

.post-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    cursor: pointer;
    transition: all 0.3s ease;
}

.post-image:hover {
    transform: scale(1.02);
}

.image-more {
    position: absolute;
    bottom: 4px;
    right: 4px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
}

/* Acciones de publicación */
.post-actions {
    display: flex;
    justify-content: space-between;
    padding: 16px 20px;
    border-top: 1px solid #f3f4f6;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 14px;
}

.action-btn:hover {
    background: #f3f4f6;
}

.action-btn.liked {
    color: #ef4444;
}

.action-btn.saved {
    color: #059669;
}

.action-btn .material-icons {
    font-size: 20px;
}

/* Comentarios */
.post-comments {
    border-top: 1px solid #f3f4f6;
    padding: 16px 20px;
    background: #f9fafb;
}

.comment {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.comment-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    object-fit: cover;
}

.comment-content {
    flex: 1;
}

.comment-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.comment-author {
    font-weight: 600;
    font-size: 14px;
    color: #1f2937;
}

.comment-time {
    font-size: 12px;
    color: #9ca3af;
}

.comment-content p {
    margin: 0 0 8px 0;
    font-size: 14px;
    line-height: 1.5;
    color: #374151;
}

.comment-actions {
    display: flex;
    gap: 16px;
}

.comment-like,
.comment-reply {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.comment-like:hover,
.comment-reply:hover {
    background: #e5e7eb;
}

.add-comment {
    display: flex;
    gap: 12px;
    margin-top: 16px;
}

.comment-input {
    flex: 1;
    display: flex;
    background: white;
    border-radius: 24px;
    border: 1px solid #e5e7eb;
    overflow: hidden;
}

.comment-input input {
    flex: 1;
    padding: 12px 16px;
    border: none;
    outline: none;
    font-size: 14px;
}

.send-comment {
    background: #059669;
    color: white;
    border: none;
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.send-comment:hover {
    background: #047857;
}

/* Modal de imagen */
.image-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
}

.image-modal .modal-content {
    position: relative;
    margin: auto;
    padding: 0;
    width: 90%;
    max-width: 800px;
    top: 50%;
    transform: translateY(-50%);
    text-align: center;
}

.close-modal {
    position: absolute;
    top: 20px;
    right: 30px;
    color: white;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    z-index: 1001;
}

.close-modal:hover {
    opacity: 0.7;
}

#modal-image {
    max-width: 100%;
    max-height: 80vh;
    border-radius: 12px;
}

.image-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    pointer-events: none;
}

.nav-btn {
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: all;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    background: rgba(0, 0, 0, 0.7);
}

/* Responsive */
@media (max-width: 768px) {
    .create-post-actions {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .post-options {
        justify-content: center;
    }
    
    .post-submit {
        justify-content: center;
    }
    
    .filter-tabs {
        flex-wrap: wrap;
    }
    
    .image-grid.multiple {
        grid-template-columns: 1fr;
        grid-template-rows: auto;
    }
    
    .post-actions {
        flex-wrap: wrap;
        gap: 8px;
    }
}
