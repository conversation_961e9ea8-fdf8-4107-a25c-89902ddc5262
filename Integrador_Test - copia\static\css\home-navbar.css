/* ===== NAVEGACIÓN ATRACTIVA PARA INICIO ===== */

/* Ocultar navegación minimalista en inicio */
.navbar {
    display: none !important;
}

/* Navegación principal atractiva */
.home-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.95) 0%, 
        rgba(248, 255, 254, 0.95) 50%, 
        rgba(240, 249, 255, 0.95) 100%);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(5, 150, 105, 0.1);
    box-shadow: 
        0 4px 30px rgba(5, 150, 105, 0.08),
        0 2px 10px rgba(0, 0, 0, 0.04);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    height: 80px;
}

.home-navbar.scrolled {
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.98) 0%, 
        rgba(248, 255, 254, 0.98) 50%, 
        rgba(240, 249, 255, 0.98) 100%);
    box-shadow: 
        0 8px 40px rgba(5, 150, 105, 0.12),
        0 4px 20px rgba(0, 0, 0, 0.08);
    height: 70px;
}

.home-navbar-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 30px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Logo atractivo */
.home-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    text-decoration: none;
    padding: 12px 20px;
    border-radius: 16px;
    background: linear-gradient(135deg, 
        rgba(5, 150, 105, 0.1) 0%, 
        rgba(16, 185, 129, 0.08) 100%);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.home-logo::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(255, 255, 255, 0.3) 50%, 
        transparent 100%);
    transition: left 0.6s ease;
}

.home-logo:hover::before {
    left: 100%;
}

.home-logo:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 30px rgba(5, 150, 105, 0.2);
    background: linear-gradient(135deg, 
        rgba(5, 150, 105, 0.15) 0%, 
        rgba(16, 185, 129, 0.12) 100%);
}

.home-logo-icon {
    font-size: 32px;
    background: linear-gradient(135deg, #059669, #10b981, #34d399);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 2px 4px rgba(5, 150, 105, 0.3));
    transition: all 0.4s ease;
}

.home-logo:hover .home-logo-icon {
    transform: rotate(10deg) scale(1.1);
}

.home-logo-text {
    font-size: 28px;
    font-weight: 800;
    background: linear-gradient(135deg, #059669, #10b981);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.5px;
    margin: 0;
}

/* Navegación central atractiva */
.home-nav-links {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 8px;
    align-items: center;
}

.home-nav-item {
    position: relative;
}

.home-nav-link {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    color: #64748b;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.home-nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, 
        rgba(0, 0, 0, 0.04) 0%, 
        rgba(0, 0, 0, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 12px;
}

.home-nav-link:hover::before {
    opacity: 1;
}

.home-nav-link:hover {
    color: #000000;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.9) 0%, 
        rgba(248, 250, 252, 0.9) 100%);
}

.home-nav-link.active {
    color: #059669;
    background: linear-gradient(135deg, 
        rgba(5, 150, 105, 0.15) 0%, 
        rgba(16, 185, 129, 0.1) 100%);
    box-shadow: 0 4px 20px rgba(5, 150, 105, 0.2);
}

.home-nav-icon {
    font-size: 20px;
    transition: all 0.3s ease;
}

.home-nav-link:hover .home-nav-icon {
    transform: scale(1.1);
}

/* Botones de acción atractivos */
.home-auth-buttons {
    display: flex;
    gap: 12px;
    align-items: center;
}

.home-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    font-size: 15px;
    font-weight: 600;
    text-decoration: none;
    border-radius: 12px;
    border: 2px solid transparent;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.home-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(255, 255, 255, 0.3) 50%, 
        transparent 100%);
    transition: left 0.6s ease;
}

.home-btn:hover::before {
    left: 100%;
}

.home-btn-outline {
    color: #64748b;
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.9) 0%, 
        rgba(248, 250, 252, 0.9) 100%);
    border-color: rgba(5, 150, 105, 0.2);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.home-btn-outline:hover {
    color: #000000;
    background: linear-gradient(135deg, 
        rgba(0, 0, 0, 0.05) 0%, 
        rgba(0, 0, 0, 0.02) 100%);
    border-color: #000000;
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.home-btn-primary {
    color: white;
    background: linear-gradient(135deg, #059669, #10b981);
    border-color: transparent;
    box-shadow: 0 6px 20px rgba(5, 150, 105, 0.3);
}

.home-btn-primary:hover {
    background: linear-gradient(135deg, #000000, #1f2937);
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.25);
}

.home-btn-icon {
    font-size: 18px;
    transition: all 0.3s ease;
}

.home-btn:hover .home-btn-icon {
    transform: scale(1.1);
}

/* Responsive */
@media (max-width: 768px) {
    .home-navbar-container {
        padding: 0 20px;
    }
    
    .home-nav-links {
        display: none;
    }
    
    .home-logo-text {
        font-size: 24px;
    }
    
    .home-logo-icon {
        font-size: 28px;
    }
    
    .home-auth-buttons {
        gap: 8px;
    }
    
    .home-btn {
        padding: 10px 16px;
        font-size: 13px;
    }
}

/* Compensar altura de navegación */
body {
    padding-top: 80px;
}

.home-navbar.scrolled ~ * {
    padding-top: 70px;
}
