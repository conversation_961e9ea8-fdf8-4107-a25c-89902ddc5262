/* ===== ESTILOS BASE ===== */
:root {
    --primary: #4CAF50;
    --primary-light: #81C784;
    --primary-dark: #388E3C;
    --accent: #FF9800;
    --text-dark: #ffffff;
    --text-light: #ECEFF1;
    --background: #FFFFFF;
    --background-alt: #F5F8F5;
    --shadow: 0 8px 30px rgba(0,0,0,0.05);
    --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

body {
    font-family: 'Roboto', sans-serif;
    color: var(--text-dark);
    background-color: var(--background);
    margin: 0;
    padding: 0;
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* ===== NAVEGACIÓN ===== */
.main-nav {
    background-color: transparent;
    position: absolute;
    width: 100%;
    z-index: 100;
    padding: 20px 0;
    transition: var(--transition);
}

.main-nav.scrolled {
    background-color: var(--background);
    box-shadow: var(--shadow);
    position: fixed;
    top: 0;
    padding: 10px 0;
    animation: slideDown 0.5s;
}

@keyframes slideDown {
    from { transform: translateY(-100%); }
    to { transform: translateY(0); }
}

.main-nav .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo .material-icons {
    color: var(--primary);
    font-size: 28px;
    transition: var(--transition);
}

.main-nav.scrolled .logo .material-icons {
    transform: rotate(180deg);
}

.logo h1 {
    font-size: 24px;
    font-weight: 300;
    color: var(--primary-dark);
    margin: 0;
    letter-spacing: 1px;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 30px;
    margin: 0;
    padding: 0;
}

.nav-links a {
    color: var(--text-dark);
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    padding: 8px 2px;
    position: relative;
    transition: var(--transition);
}

.nav-links a:hover,
.nav-links a.active {
    color: var(--primary);
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.nav-links a:hover::after,
.nav-links a.active::after {
    transform: scaleX(1);
}

/* ===== HERO SECTION ===== */
.hero-section {
    position: relative;
    height: 100vh;
    min-height: 600px;
    display: flex;
    align-items: center;
    overflow: hidden;
    background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 50%, #1B5E20 100%);
}

/* Animaciones de fondo */
.hero-animations {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.floating-leaves {
    position: absolute;
    width: 100%;
    height: 100%;
}

.leaf {
    position: absolute;
    font-size: 24px;
    opacity: 0.7;
    animation: floatLeaf 8s ease-in-out infinite;
}

.leaf:nth-child(1) {
    left: 10%;
    top: 20%;
    animation-delay: 0s;
    animation-duration: 8s;
}

.leaf:nth-child(2) {
    left: 20%;
    top: 60%;
    animation-delay: 1s;
    animation-duration: 10s;
}

.leaf:nth-child(3) {
    left: 70%;
    top: 30%;
    animation-delay: 2s;
    animation-duration: 9s;
}

.leaf:nth-child(4) {
    left: 80%;
    top: 70%;
    animation-delay: 3s;
    animation-duration: 7s;
}

.leaf:nth-child(5) {
    left: 50%;
    top: 10%;
    animation-delay: 4s;
    animation-duration: 11s;
}

.leaf:nth-child(6) {
    left: 30%;
    top: 80%;
    animation-delay: 5s;
    animation-duration: 8s;
}

@keyframes floatLeaf {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    25% {
        transform: translateY(-20px) rotate(5deg);
    }
    50% {
        transform: translateY(-10px) rotate(-5deg);
    }
    75% {
        transform: translateY(-30px) rotate(3deg);
    }
}

.floating-bubbles {
    position: absolute;
    width: 100%;
    height: 100%;
}

.bubble {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: floatBubble 6s ease-in-out infinite;
}

.bubble:nth-child(1) {
    width: 60px;
    height: 60px;
    left: 15%;
    animation-delay: 0s;
}

.bubble:nth-child(2) {
    width: 40px;
    height: 40px;
    left: 35%;
    animation-delay: 1s;
}

.bubble:nth-child(3) {
    width: 80px;
    height: 80px;
    left: 60%;
    animation-delay: 2s;
}

.bubble:nth-child(4) {
    width: 50px;
    height: 50px;
    left: 85%;
    animation-delay: 3s;
}

.bubble:nth-child(5) {
    width: 70px;
    height: 70px;
    left: 75%;
    animation-delay: 4s;
}

@keyframes floatBubble {
    0% {
        transform: translateY(100vh) scale(0);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) scale(1);
        opacity: 0;
    }
}

.hero-content-container {
    width: 100%;
    position: relative;
    color: white;
    text-align: left;
    transform: translateY(30px);
    opacity: 0;
    animation: fadeUp 1s forwards 0.5s;
    z-index: 10;
}

@keyframes fadeUp {
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.hero-content-container h1 {
    font-size: 3.5rem;
    font-weight: 300;
    line-height: 1.2;
    margin-bottom: 20px;
    max-width: 600px;
}

.hero-content-container p {
    font-size: 1.25rem;
    max-width: 500px;
    margin-bottom: 40px;
}

.hero-buttons {
    display: flex;
    gap: 20px;
}

.btn {
    display: inline-block;
    padding: 14px 32px;
    border-radius: 50px;
    font-weight: 500;
    text-decoration: none;
    text-align: center;
    letter-spacing: 0.5px;
    transition: var(--transition);
    border: none;
    cursor: pointer;
}

.btn-primary {
    background-color: var(--primary);
    color: white;
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
}

.btn-outline {
    background-color: transparent;
    color: white;
    box-shadow: inset 0 0 0 2px white;
}

.btn-outline:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* ===== FLOATING ELEMENTS ===== */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.float-item {
    position: absolute;
    opacity: 0.1;
    border-radius: 50%;
    background-color: white;
}

.float-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation: float 15s ease-in-out infinite;
}

.float-2 {
    width: 60px;
    height: 60px;
    top: 60%;
    left: 80%;
    animation: float 12s ease-in-out infinite 2s;
}

.float-3 {
    width: 40px;
    height: 40px;
    top: 30%;
    left: 70%;
    animation: float 18s ease-in-out infinite 1s;
}

@keyframes float {
    0% { transform: translate(0, 0) rotate(0deg); }
    33% { transform: translate(30px, -50px) rotate(45deg); }
    66% { transform: translate(-20px, 20px) rotate(90deg); }
    100% { transform: translate(0, 0) rotate(0deg); }
}

/* ===== FEATURES SECTION ===== */
.features {
    padding: 100px 0;
    background-color: var(--background-alt);
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 300;
    margin-bottom: 60px;
    color: var(--primary-dark);
    position: relative;
}

.section-title::after {
    content: '';
    display: block;
    width: 80px;
    height: 3px;
    background-color: var(--primary);
    margin: 15px auto 0;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 40px;
}

.feature-card {
    background-color: var(--background);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    z-index: 1;
    padding: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 0;
    background-color: var(--primary-light);
    opacity: 0.05;
    z-index: -1;
    transition: var(--transition);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.feature-card:hover::before {
    height: 100%;
}

.feature-icon {
    width: 70px;
    height: 70px;
    background-color: var(--primary-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 25px;
    transition: var(--transition);
}

.feature-card:hover .feature-icon {
    transform: scale(1.1);
    background-color: var(--primary);
}

.feature-icon .material-icons {
    font-size: 32px;
    color: white;
}

.feature-card h3 {
    margin: 0 0 15px;
    font-size: 22px;
    font-weight: 500;
    color: var(--text-dark);
}

.feature-card p {
    color: #546E7A;
    margin-bottom: 25px;
}

.feature-link {
    color: var(--primary);
    text-decoration: none;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    margin-top: auto;
    position: relative;
    overflow: hidden;
}

.feature-link::after {
    content: '→';
    opacity: 0;
    margin-left: -20px;
    transition: var(--transition);
}

.feature-link:hover::after {
    opacity: 1;
    margin-left: 8px;
}

/* ===== POPULAR PLANTS SECTION ===== */
.popular-plants {
    padding: 100px 0;
}

.plants-carousel {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.plant-card {
    background-color: var(--background);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.plant-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.plant-image {
    height: 220px;
    position: relative;
    overflow: hidden;
}

.plant-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.plant-card:hover .plant-image img {
    transform: scale(1.05);
}

.plant-badges {
    position: absolute;
    top: 15px;
    left: 15px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.badge {
    padding: 6px 12px;
    border-radius: 50px;
    font-size: 12px;
    font-weight: 500;
}

.native-badge {
    background-color: var(--primary);
    color: white;
}

.plant-info {
    padding: 25px;
}

.plant-name {
    margin: 0 0 5px;
    font-size: 20px;
    font-weight: 500;
}

.plant-scientific-name {
    color: #78909C;
    font-style: italic;
    margin: 0 0 15px;
}

.plant-attributes {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.attribute {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    color: #546E7A;
}

.attribute .material-icons {
    font-size: 18px;
    color: var(--primary);
}

.view-details {
    width: 100%;
    display: block;
    text-align: center;
    padding: 12px;
    border-radius: 8px;
}

/* ===== CTA SECTION ===== */
.cta {
    padding: 100px 0;
    background-color: var(--primary-dark);
    color: var(--text-light);
    position: relative;
    overflow: hidden;
}

.cta::before {
    content: '';
    position: absolute;
    top: -10%;
    right: -5%;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.05);
}

.cta::after {
    content: '';
    position: absolute;
    bottom: -15%;
    left: -5%;
    width: 400px;
    height: 400px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.05);
}

.cta-content {
    text-align: center;
    position: relative;
    z-index: 1;
    max-width: 800px;
    margin: 0 auto;
}

.cta-content h2 {
    font-size: 2.8rem;
    font-weight: 300;
    margin-bottom: 20px;
}

.cta-content p {
    font-size: 1.25rem;
    margin-bottom: 40px;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
}

.cta .btn-primary {
    background-color: white;
    color: var(--primary-dark);
}

.cta .btn-primary:hover {
    background-color: var(--text-light);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
}

.cta .btn-outline {
    color: white;
    box-shadow: inset 0 0 0 2px white;
}

/* ===== FOOTER ===== */
.app-footer {
    background-color: var(--background);
    padding: 80px 0 30px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h3 {
    color: var(--primary-dark);
    margin-bottom: 20px;
    font-weight: 500;
    font-size: 18px;
}

.footer-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul a {
    color: #546E7A;
    text-decoration: none;
    transition: var(--transition);
}

.footer-section ul a:hover {
    color: var(--primary);
    padding-left: 5px;
}

.footer-bottom {
    padding-top: 30px;
    border-top: 1px solid #ECEFF1;
    text-align: center;
    color: #78909C;
}

/* ===== ANIMATIONS ===== */
@keyframes fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    opacity: 0;
    animation: fade-in 1s forwards;
}

.fade-in-delay-1 {
    animation-delay: 0.2s;
}

.fade-in-delay-2 {
    animation-delay: 0.4s;
}

.fade-in-delay-3 {
    animation-delay: 0.6s;
}

.fade-in-delay-4 {
    animation-delay: 0.8s;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 992px) {
    .hero-content-container h1 {
        font-size: 2.8rem;
    }
    
    .hero-content-container p {
        font-size: 1.1rem;
    }
    
    .section-title {
        font-size: 2.2rem;
    }
    
    .cta-content h2 {
        font-size: 2.4rem;
    }
    
    .cta-content p {
        font-size: 1.1rem;
    }
}

@media (max-width: 768px) {
    .hero-section {
        height: 80vh;
    }
    
    .hero-content-container h1 {
        font-size: 2.4rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        gap: 15px;
    }
    
    .btn {
        width: 100%;
    }
    
    .features-grid, 
    .plants-carousel {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        grid-template-columns: 1fr 1fr;
    }
}

@media (max-width: 576px) {
    .nav-links {
        display: none;
    }
    
    .hero-content-container h1 {
        font-size: 2rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
    }
}

/* ===== JavaScript Triggered Classes ===== */
.scroll-reveal {
    opacity: 0;
    transform: translateY(40px);
    transition: all 1s ease;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

.nav-links .mobile-menu-btn {
    display: none;
}

@media (max-width: 576px) {
    .nav-links.mobile-visible {
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 80px;
        left: 0;
        right: 0;
        background: white;
        box-shadow: var(--shadow);
        padding: 20px;
        z-index: 100;
    }
    
    .nav-links .mobile-menu-btn {
        display: block;
    }
}

/* ===== FEATURES SECTION MODERNA ===== */
.features-modern {
    padding: 120px 0;
    background: linear-gradient(135deg, #f8fffe 0%, #f0f9ff 50%, #ecfdf5 100%);
    position: relative;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
}

.features-modern .container {
    width: 100%;
    max-width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.features-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23059669" opacity="0.05"/><circle cx="75" cy="75" r="1" fill="%23059669" opacity="0.05"/><circle cx="50" cy="10" r="1" fill="%23059669" opacity="0.03"/><circle cx="10" cy="90" r="1" fill="%23059669" opacity="0.03"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

/* Header de la sección */
.features-header {
    text-align: center;
    margin-bottom: 100px;
    position: relative;
    z-index: 2;
    padding: 0 20px;
}

.features-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, #059669, #10b981);
    color: white;
    padding: 8px 20px;
    border-radius: 50px;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3);
    animation: float 3s ease-in-out infinite;
}

.features-badge .material-icons {
    font-size: 18px;
}

.features-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 20px;
    line-height: 1.2;
}

.gradient-text {
    background: linear-gradient(135deg, #059669, #10b981, #34d399);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradient-shift 3s ease-in-out infinite;
}

.features-subtitle {
    font-size: 1.25rem;
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Grid moderno */
.features-grid-modern {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 60px;
    margin-bottom: 80px;
    padding: 0 30px;
    justify-items: center;
    max-width: 1600px;
    margin-left: auto;
    margin-right: auto;
}

/* Espaciado adicional para pantallas grandes */
@media (min-width: 1200px) {
    .features-grid-modern {
        gap: 80px;
        padding: 0 50px;
        max-width: 1800px;
    }

    .feature-card-modern.featured {
        max-width: 900px;
    }
}

@media (min-width: 1400px) {
    .features-grid-modern {
        gap: 100px;
        max-width: 2000px;
        margin: 0 auto 80px auto;
        padding: 0 80px;
    }

    .feature-card-modern.featured {
        max-width: 1000px;
    }

    .features-stats {
        max-width: 1400px;
        padding: 60px 80px;
        gap: 60px;
    }

    .stat-item {
        padding: 30px 20px;
    }

    .stat-icon {
        width: 80px;
        height: 80px;
    }

    .stat-icon .material-icons {
        font-size: 36px;
    }

    .stat-info .stat-number {
        font-size: 3rem;
    }

    .stat-info .stat-label {
        font-size: 1rem;
    }
}

/* Tarjetas de características modernas */
.feature-card-modern {
    background: white;
    border-radius: 20px;
    padding: 30px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.06), 0 3px 10px rgba(0, 0, 0, 0.04);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(5, 150, 105, 0.08);
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 400px;
    width: 100%;
    max-width: 400px;
}

.feature-card-modern:hover {
    transform: translateY(-12px);
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.12), 0 8px 20px rgba(0, 0, 0, 0.08);
}

.feature-card-modern.featured {
    grid-column: span 2;
    background: linear-gradient(135deg, #059669, #10b981);
    color: white;
    max-width: 820px;
    justify-self: center;
}

.feature-background {
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    opacity: 0.1;
    transition: all 0.4s ease;
}

.feature-pattern {
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, currentColor 2px, transparent 2px);
    background-size: 20px 20px;
}

.feature-card-modern:hover .feature-background {
    transform: scale(1.2) rotate(10deg);
    opacity: 0.15;
}

/* Iconos modernos */
.feature-icon-modern {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #059669, #10b981);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
    transition: all 0.4s ease;
}

.feature-card-modern.featured .feature-icon-modern {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.feature-icon-modern .material-icons {
    font-size: 28px;
    color: white;
}

.feature-card-modern:hover .feature-icon-modern {
    transform: scale(1.1) rotate(5deg);
}

/* Contenido */
.feature-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.feature-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 12px;
    color: #1f2937;
}

.feature-card-modern.featured .feature-content h3 {
    color: white;
}

.feature-content p {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 20px;
    flex-grow: 1;
}

.feature-card-modern.featured .feature-content p {
    color: rgba(255, 255, 255, 0.9);
}

/* Beneficios */
.feature-benefits {
    list-style: none;
    padding: 0;
    margin: 16px 0 20px 0;
}

.feature-benefits li {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
}

.feature-benefits .material-icons {
    font-size: 16px;
    color: #34d399;
    flex-shrink: 0;
}

/* Estadísticas */
.feature-stats {
    display: flex;
    gap: 16px;
    margin: 16px 0 20px 0;
}

.stat {
    text-align: center;
    flex: 1;
}

.stat-number {
    display: block;
    font-size: 1.4rem;
    font-weight: 700;
    color: #059669;
    line-height: 1.2;
}

.stat-label {
    font-size: 0.75rem;
    color: #6b7280;
    margin-top: 2px;
}

/* Highlights */
.feature-highlight {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(5, 150, 105, 0.1);
    padding: 8px 12px;
    border-radius: 8px;
    margin: 12px 0 20px 0;
    font-size: 14px;
    color: #059669;
    font-weight: 500;
}

.feature-card-modern.featured .feature-highlight {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.feature-highlight .material-icons {
    font-size: 16px;
    flex-shrink: 0;
}

/* Botones modernos */
.feature-btn-modern {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 14px 20px;
    background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
    color: #374151;
    text-decoration: none;
    border-radius: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
    margin-top: auto;
    margin-bottom: 0;
    box-sizing: border-box;
    min-height: 48px;
}

.feature-card-modern.featured .feature-btn-modern {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    backdrop-filter: blur(10px);
}

.feature-btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.feature-card-modern.featured .feature-btn-modern:hover {
    background: rgba(255, 255, 255, 0.3);
}

.feature-btn-modern .material-icons {
    font-size: 18px;
    transition: transform 0.3s ease;
}

.feature-btn-modern:hover .material-icons {
    transform: translateX(4px);
}

/* Backgrounds específicos */
.scanner-bg { background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1)); }
.calendar-bg { background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(251, 191, 36, 0.1)); }
.community-bg { background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(74, 222, 128, 0.1)); }
.profile-bg { background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(248, 113, 113, 0.1)); }

/* Iconos específicos */
.scanner-icon { background: linear-gradient(135deg, #3b82f6, #9333ea) !important; }
.calendar-icon { background: linear-gradient(135deg, #f59e0b, #fbbf24) !important; }
.community-icon { background: linear-gradient(135deg, #22c55e, #4ade80) !important; }
.profile-icon { background: linear-gradient(135deg, #ef4444, #f87171) !important; }

/* Estadísticas de la sección */
.features-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 40px;
    margin-top: 60px;
    padding: 50px 60px;
    background: white;
    border-radius: 25px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    text-align: center;
    padding: 20px 15px;
    border-radius: 15px;
    background: linear-gradient(135deg, #f8fffe, #f0f9ff);
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(5, 150, 105, 0.15);
}

.stat-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #059669, #10b981);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin-bottom: 10px;
    box-shadow: 0 8px 20px rgba(5, 150, 105, 0.3);
}

.stat-icon .material-icons {
    color: white;
    font-size: 32px;
}

.stat-info .stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 800;
    color: #1f2937;
    line-height: 1;
    margin-bottom: 5px;
    background: linear-gradient(135deg, #059669, #10b981);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-info .stat-label {
    color: #6b7280;
    font-size: 0.95rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Animaciones */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Responsive */
@media (max-width: 768px) {
    .features-modern {
        padding: 80px 0;
    }

    .features-grid-modern {
        grid-template-columns: 1fr;
        gap: 50px;
        padding: 0 20px;
        justify-items: center;
    }

    .feature-card-modern {
        min-height: 350px;
        padding: 25px 20px;
        max-width: 500px;
        width: 100%;
    }

    .feature-card-modern.featured {
        grid-column: span 1;
        min-height: 400px;
        max-width: 500px;
    }

    .features-title {
        font-size: 2.5rem;
    }

    .feature-stats {
        gap: 12px;
        margin: 12px 0 16px 0;
    }

    .stat-number {
        font-size: 1.2rem;
    }

    .stat-label {
        font-size: 0.7rem;
    }

    .features-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 25px;
        padding: 40px 30px;
        max-width: 600px;
    }

    .stat-item {
        padding: 15px 10px;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
    }

    .stat-icon .material-icons {
        font-size: 28px;
    }

    .stat-info .stat-number {
        font-size: 2rem;
    }

    .stat-info .stat-label {
        font-size: 0.85rem;
    }
}

@media (max-width: 480px) {
    .features-grid-modern {
        gap: 40px;
        padding: 0 15px;
        justify-items: center;
    }

    .feature-card-modern {
        min-height: 320px;
        padding: 20px 16px;
        max-width: 100%;
        width: 100%;
    }

    .feature-card-modern.featured {
        max-width: 100%;
    }

    .feature-content h3 {
        font-size: 1.3rem;
    }

    .feature-content p {
        font-size: 0.9rem;
    }

    .feature-btn-modern {
        padding: 12px 16px;
        font-size: 0.9rem;
    }

    .features-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
        padding: 30px 20px;
        max-width: 100%;
        margin: 40px 15px 0 15px;
    }

    .stat-item {
        padding: 12px 8px;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
    }

    .stat-icon .material-icons {
        font-size: 24px;
    }

    .stat-info .stat-number {
        font-size: 1.8rem;
    }

    .stat-info .stat-label {
        font-size: 0.75rem;
    }
}