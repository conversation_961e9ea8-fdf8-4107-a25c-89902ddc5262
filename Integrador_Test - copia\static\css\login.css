/* ===== ESTILOS BASE ===== */
:root {
    --primary: #4CAF50;
    --primary-light: #81C784;
    --primary-dark: #388E3C;
    --accent: #FF9800;
    --text-dark: #263238;
    --text-light: #ECEFF1;
    --background: #FFFFFF;
    --background-alt: #F5F8F5;
    --shadow: 0 8px 30px rgba(0,0,0,0.05);
    --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    --border-radius: 15px;
    --google-color: #DB4437;
    --facebook-color: #4267B2;
}

body {
    font-family: 'Roboto', sans-serif;
    color: var(--text-dark);
    background-color: var(--background-alt);
    margin: 0;
    padding: 0;
    line-height: 1.6;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
    background-image: linear-gradient(135deg, rgba(129, 199, 132, 0.1) 0%, rgba(255, 255, 255, 0.1) 100%);
}

/* ===== INTERACTIVE FLOWER ===== */
.interactive-flower {
    position: fixed;
    bottom: 30px;
    left: 30px;
    z-index: 1000;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.interactive-flower:hover {
    transform: scale(1.2);
}

.flower-emoji {
    font-size: 32px;
    display: block;
    filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.2));
}

.weather-effect {
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 24px;
    opacity: 0;
    transition: all 0.5s ease;
    pointer-events: none;
}

.weather-effect.active {
    opacity: 1;
    animation: weatherEffect 2s forwards;
}

@keyframes weatherEffect {
    0% {
        transform: translateX(-50%) translateY(0);
        opacity: 0;
    }
    20% {
        opacity: 1;
    }
    80% {
        opacity: 1;
    }
    100% {
        transform: translateX(-50%) translateY(-40px);
        opacity: 0;
    }
}

/* Animaciones para la lluvia especial de gatitos y flores */
@keyframes fallDown {
    from {
        transform: translateY(-50px) rotate(0deg);
    }
    to {
        transform: translateY(calc(100vh + 50px)) rotate(360deg);
    }
}

@keyframes fadeInOut {
    0% {
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}

.special-rain-drop {
    position: absolute;
    filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.2));
    z-index: 9999;
    user-select: none;
}

/* ===== TOP NAVIGATION ===== */
.top-nav {
    background-color: transparent;
    padding: 20px 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
}

.logo .material-icons {
    color: var(--primary);
    font-size: 28px;
    transition: var(--transition);
}

.logo h1 {
    font-size: 24px;
    font-weight: 300;
    color: var(--primary-dark);
    margin: 0;
    letter-spacing: 1px;
}

/* ===== CONTAINER ===== */
.container {
    width: 90%;
    max-width: 1000px;
    background-color: var(--background);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
    min-height: 600px;
    margin: 40px auto;
    flex-grow: 1;
    transform: translateY(20px);
    opacity: 0;
    animation: fadeInUp 0.8s forwards;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== FORM CONTAINER ===== */
.form-container {
    position: absolute;
    top: 0;
    height: 100%;
    transition: all 0.6s ease-in-out;
}

.sign-in {
    left: 0;
    width: 50%;
    z-index: 2;
}

.sign-up {
    left: 0;
    width: 50%;
    opacity: 0;
    z-index: 1;
}

.container.active .sign-in {
    transform: translateX(100%);
}

.container.active .sign-up {
    transform: translateX(100%);
    opacity: 1;
    z-index: 5;
    animation: show 0.6s;
}

@keyframes show {
    0%, 49.99% {
        opacity: 0;
        z-index: 1;
    }
    50%, 100% {
        opacity: 1;
        z-index: 5;
    }
}

/* ===== TOGGLE CONTAINER ===== */
.toggle-container {
    position: absolute;
    top: 0;
    left: 50%;
    width: 50%;
    height: 100%;
    overflow: hidden;
    transition: all 0.6s ease-in-out;
    z-index: 1000;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
}

.container.active .toggle-container {
    transform: translateX(-100%);
}

.toggle {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
    color: var(--text-light);
    position: relative;
    left: -100%;
    height: 100%;
    width: 200%;
    transform: translateX(0);
    transition: all 0.6s ease-in-out;
}

.container.active .toggle {
    transform: translateX(50%);
}

.toggle-panel {
    position: absolute;
    top: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 30px;
    width: 50%;
    height: 100%;
    text-align: center;
    transform: translateX(0);
    transition: all 0.6s ease-in-out;
}

.toggle-left {
    transform: translateX(-200%);
}

.container.active .toggle-left {
    transform: translateX(0);
}

.toggle-right {
    right: 0;
    transform: translateX(0);
}

.container.active .toggle-right {
    transform: translateX(200%);
}

.panel-icon {
    font-size: 60px;
    margin-bottom: 20px;
    opacity: 0.9;
}

.toggle-panel h1 {
    font-weight: 300;
    margin: 0 0 15px;
    font-size: 28px;
}

.toggle-panel p {
    font-size: 14px;
    padding: 0 30px;
    margin: 20px 0 30px;
    opacity: 0.8;
}

/* ===== FORM STYLES ===== */
form {
    background-color: var(--background);
    display: flex;
    flex-direction: column;
    padding: 40px;
    height: 100%;
    justify-content: center;
    text-align: center;
}

h1 {
    margin: 0 0 10px;
    font-weight: 400;
    font-size: 28px;
    color: var(--text-dark);
}

.subtitle {
    font-size: 16px;
    margin-bottom: 30px;
    color: var(--text-dark);
    opacity: 0.7;
}

.form-group {
    position: relative;
    margin-bottom: 20px;
    width: 100%;
}

.input-with-icon {
    position: relative;
}

.input-with-icon .material-icons {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary);
    font-size: 20px;
}

input {
    width: 100%;
    padding: 15px 15px 15px 50px;
    background-color: var(--background-alt);
    border: none;
    border-radius: 50px;
    outline: none;
    font-size: 14px;
    color: var(--text-dark);
    transition: var(--transition);
}

input:focus {
    box-shadow: 0 0 0 2px var(--primary-light);
}

.validation-message {
    display: none;
    color: #F44336;
    font-size: 12px;
    margin-top: 8px;
    text-align: left;
    padding-left: 15px;
    display: flex;
    align-items: center;
}

.validation-message .material-icons {
    font-size: 16px;
    margin-right: 5px;
}

.password-strength {
    height: 4px;
    background-color: #E0E0E0;
    border-radius: 2px;
    margin-top: 8px;
    overflow: hidden;
}

.password-strength-meter {
    height: 100%;
    width: 0;
    border-radius: 2px;
    transition: var(--transition);
}

.password-strength-meter.weak {
    width: 33%;
    background-color: #F44336;
}

.password-strength-meter.medium {
    width: 66%;
    background-color: #FFA000;
}

.password-strength-meter.strong {
    width: 100%;
    background-color: #4CAF50;
}

.terms-container {
    margin: 15px 0 25px;
    text-align: left;
}

.remember-forgot {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 15px 0 25px;
}

.forgot-link {
    color: var(--primary);
    text-decoration: none;
    font-size: 14px;
    transition: var(--transition);
}

.forgot-link:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.terms-link {
    color: var(--primary);
    text-decoration: none;
    transition: var(--transition);
}

.terms-link:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* ===== CHECKBOX STYLES ===== */
.checkbox-container {
    display: flex;
    align-items: center;
    position: relative;
    padding-left: 30px;
    cursor: pointer;
    font-size: 14px;
    user-select: none;
}

.checkbox-container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: var(--background-alt);
    border-radius: 5px;
}

.checkbox-container:hover input ~ .checkmark {
    background-color: #DDD;
}

.checkbox-container input:checked ~ .checkmark {
    background-color: var(--primary);
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.checkbox-container input:checked ~ .checkmark:after {
    display: block;
}

.checkbox-container .checkmark:after {
    left: 7px;
    top: 3px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.checkbox-text {
    font-size: 14px;
}

/* ===== BUTTON STYLES ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: 500;
    text-decoration: none;
    text-align: center;
    letter-spacing: 0.5px;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    font-size: 16px;
    margin: 10px 0;
}

.btn .material-icons {
    font-size: 20px;
}

.btn-primary {
    background-color: var(--primary);
    color: white;
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
}

.btn-outline {
    background-color: transparent;
    color: white;
    box-shadow: inset 0 0 0 2px white;
}

.btn-outline:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* ===== SOCIAL LOGIN ===== */
.social-login {
    margin-top: 20px;
}

.social-login p {
    font-size: 14px;
    color: var(--text-dark);
    opacity: 0.7;
    margin-bottom: 15px;
}

.social-icons {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.social-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 10px 20px;
    border-radius: 50px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    color: white;
    transition: var(--transition);
}

.social-icon .material-icons {
    font-size: 18px;
}

.social-icon.google {
    background-color: var(--google-color);
}

.social-icon.facebook {
    background-color: var(--facebook-color);
}

.social-icon:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* ===== FLASH MESSAGES ===== */
.flash-messages {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1100;
    max-width: 400px;
}

.flash-message {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 10px;
    background-color: var(--background);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    animation: slideInRight 0.3s ease forwards;
    transition: var(--transition);
}

.flash-message.error {
    border-left: 4px solid #F44336;
}

.flash-message.success {
    border-left: 4px solid var(--primary);
}

.flash-message.info {
    border-left: 4px solid #2196F3;
}

.flash-message .material-icons {
    margin-right: 10px;
    font-size: 20px;
}

.flash-message.error .material-icons {
    color: #F44336;
}

.flash-message.success .material-icons {
    color: var(--primary);
}

.flash-message.info .material-icons {
    color: #2196F3;
}

.dismiss-btn {
    margin-left: auto;
    background: none;
    border: none;
    color: #757575;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 5px;
    border-radius: 50%;
    transition: var(--transition);
}

.dismiss-btn:hover {
    background-color: rgba(0,0,0,0.05);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ===== NOTIFICATION CONTAINER ===== */
#notification-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1100;
}

.notification {
    background-color: var(--background);
    color: var(--text-dark);
    border-radius: var(--border-radius);
    padding: 15px 20px;
    margin-bottom: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    width: 300px;
    animation: slideInRight 0.3s ease, fadeOut 0.5s ease 2.5s forwards;
}

.notification.success {
    border-left: 4px solid var(--primary);
}

.notification.error {
    border-left: 4px solid #F44336;
}

.notification.warning {
    border-left: 4px solid #FFC107;
}

.notification .material-icons {
    font-size: 24px;
}

.notification.success .material-icons {
    color: var(--primary);
}

.notification.error .material-icons {
    color: #F44336;
}

.notification.warning .material-icons {
    color: #FFC107;
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

/* ===== FOOTER ===== */
.app-footer {
    background-color: var(--background);
    padding: 30px 40px;
    box-shadow: 0 -5px 20px rgba(0,0,0,0.02);
    margin-top: auto;
}

.footer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.footer-content .logo {
    margin-right: auto;
}

.footer-content p {
    margin: 0;
    color: var(--text-dark);
    opacity: 0.7;
    font-size: 14px;
}

.footer-links {
    display: flex;
    gap: 20px;
}

.footer-links a {
    color: var(--text-dark);
    opacity: 0.7;
    text-decoration: none;
    transition: var(--transition);
    font-size: 14px;
}

.footer-links a:hover {
    color: var(--primary);
    opacity: 1;
}

/* ===== RESPONSIVE STYLES ===== */
@media (max-width: 768px) {
    .container {
        min-height: 800px;
    }

    .toggle-container {
        display: none;
    }

    .sign-in, .sign-up {
        width: 100%;
    }

    .container.active .sign-in {
        transform: translateX(-100%);
    }

    .container.active .sign-up {
        transform: translateX(0);
        opacity: 1;
        z-index: 5;
    }

    /* Enlaces de alternancia para móviles */
    .mobile-toggle {
        display: block;
        margin-top: 20px;
        font-size: 14px;
        color: var(--text-dark);
        opacity: 0.7;
    }

    .mobile-toggle a {
        color: var(--primary);
        text-decoration: none;
        font-weight: 500;
    }

    form {
        padding: 30px;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }

    .footer-content .logo {
        margin-right: 0;
    }

    .footer-links {
        flex-wrap: wrap;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .top-nav {
        padding: 15px;
    }

    .container {
        width: 95%;
        margin: 20px auto;
    }

    form {
        padding: 20px;
    }

    h1 {
        font-size: 24px;
    }

    .btn {
        padding: 12px 20px;
        font-size: 14px;
    }

    .social-icons {
        flex-direction: column;
    }

    .flash-messages {
        width: 90%;
        max-width: none;
        left: 5%;
        right: 5%;
    }
}

/* ===== ANIMATION STYLES ===== */
.fade-in {
    opacity: 0;
    animation: fadeIn 0.5s forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

.slide-up {
    transform: translateY(20px);
    opacity: 0;
    animation: slideUp 0.5s forwards;
}

@keyframes slideUp {
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Transición suave para elementos con hover */
.btn, .forgot-link, .terms-link, input, .social-icon, .dismiss-btn {
    transition: var(--transition);
}

/* Efecto de elevación en elementos interactivos */
.btn:active, .social-icon:active {
    transform: translateY(1px);
}