/* ===== NAVEGACIÓN MINIMALISTA ===== */
:root {
    --primary: #059669;
    --primary-dark: #047857;
    --text-dark: #1f2937;
    --text-light: #6b7280;
    --background: #ffffff;
    --border-light: #e5e7eb;
    --transition: all 0.2s ease;
}

/* Navegación principal minimalista */
.main-nav {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    padding: 12px 0;
    transition: var(--transition);
    border-bottom: 1px solid var(--border-light);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.main-nav.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    padding: 10px 0;
}

.main-nav .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
}

/* Logo minimalista */
.logo {
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    transition: var(--transition);
}

.logo .material-icons {
    font-size: 24px;
    color: var(--primary);
    transition: var(--transition);
}

.logo:hover .material-icons {
    color: var(--primary-dark);
}

.logo h1 {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
    color: var(--text-dark);
    letter-spacing: -0.5px;
}

/* Enlaces de navegación minimalistas */
.nav-links {
    display: flex;
    list-style: none;
    gap: 32px;
    margin: 0;
    padding: 0;
    align-items: center;
}

.nav-links a {
    color: var(--text-light);
    text-decoration: none;
    font-weight: 500;
    font-size: 15px;
    padding: 8px 12px;
    border-radius: 6px;
    transition: var(--transition);
    position: relative;
}

.nav-links a:hover {
    color: #000000;
    background: rgba(0, 0, 0, 0.04);
}

.nav-links a.active {
    color: var(--primary);
    background: rgba(5, 150, 105, 0.08);
}

/* Botones de autenticación minimalistas */
.auth-buttons {
    display: flex;
    gap: 12px;
    align-items: center;
}

.auth-buttons .btn {
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 6px;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 6px;
    text-decoration: none;
    border: 1px solid transparent;
}

.login-btn {
    color: var(--text-light) !important;
    background: transparent !important;
    border-color: var(--border-light) !important;
}

.login-btn:hover {
    color: #000000 !important;
    background: rgba(0, 0, 0, 0.04) !important;
    border-color: #000000 !important;
}

.register-btn {
    color: white !important;
    background: var(--primary) !important;
    border-color: var(--primary) !important;
}

.register-btn:hover {
    background: #000000 !important;
    border-color: #000000 !important;
}

/* Responsive minimalista */
@media (max-width: 768px) {
    .main-nav .container {
        padding: 0 16px;
    }

    .nav-links {
        gap: 20px;
    }

    .nav-links a {
        font-size: 14px;
        padding: 6px 8px;
    }

    .auth-buttons {
        gap: 8px;
    }

    .auth-buttons .btn {
        padding: 6px 12px;
        font-size: 13px;
    }

    .logo h1 {
        font-size: 18px;
    }

    .logo .material-icons {
        font-size: 22px;
    }
}

@media (max-width: 640px) {
    .nav-links {
        display: none;
    }
}

/* Compensar navegación fija */
body {
    padding-top: 60px;
}
