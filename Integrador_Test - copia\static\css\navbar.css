/* ===== NAVEGACIÓN MINIMALISTA NUEVA ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --nav-bg: #ffffff;
    --nav-text: #64748b;
    --nav-text-hover: #000000;
    --nav-border: #f1f5f9;
    --nav-primary: #059669;
    --nav-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    --nav-transition: all 0.15s ease;
}

/* Navegación principal */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: var(--nav-bg);
    border-bottom: 1px solid var(--nav-border);
    box-shadow: var(--nav-shadow);
    height: 64px;
}

.navbar-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Logo */
.navbar-brand {
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    color: inherit;
    transition: var(--nav-transition);
}

.navbar-brand:hover {
    opacity: 0.8;
}

.brand-icon {
    width: 24px;
    height: 24px;
    color: var(--nav-primary);
    font-size: 24px;
}

.brand-text {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    letter-spacing: -0.025em;
}

/* Navegación central */
.navbar-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 0;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: block;
    padding: 8px 16px;
    color: var(--nav-text);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: var(--nav-transition);
    border-radius: 6px;
    margin: 0 2px;
}

.nav-link:hover {
    color: var(--nav-text-hover);
    background-color: rgba(0, 0, 0, 0.04);
}

.nav-link.active {
    color: var(--nav-primary);
    background-color: rgba(5, 150, 105, 0.08);
}

/* Botones de acción */
.navbar-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    font-size: 13px;
    font-weight: 500;
    text-decoration: none;
    border-radius: 6px;
    border: 1px solid transparent;
    transition: var(--nav-transition);
    cursor: pointer;
}

.btn-outline {
    color: var(--nav-text);
    border-color: #e2e8f0;
    background: transparent;
}

.btn-outline:hover {
    color: var(--nav-text-hover);
    border-color: var(--nav-text-hover);
    background: rgba(0, 0, 0, 0.02);
}

.btn-primary {
    color: white;
    background: var(--nav-primary);
    border-color: var(--nav-primary);
}

.btn-primary:hover {
    background: var(--nav-text-hover);
    border-color: var(--nav-text-hover);
}

/* Menu móvil */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 20px;
    color: var(--nav-text);
    cursor: pointer;
    padding: 4px;
}

/* Responsive */
@media (max-width: 768px) {
    .navbar-container {
        padding: 0 16px;
    }

    .navbar-nav {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--nav-bg);
        border-top: 1px solid var(--nav-border);
        box-shadow: var(--nav-shadow);
        flex-direction: column;
        padding: 8px 0;
    }

    .navbar-nav.show {
        display: flex;
    }

    .nav-link {
        padding: 12px 20px;
        margin: 0;
        border-radius: 0;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .navbar-actions {
        gap: 6px;
    }

    .btn {
        padding: 5px 10px;
        font-size: 12px;
    }

    .brand-text {
        font-size: 16px;
    }
}

/* Compensar navegación fija */
body {
    padding-top: 64px;
}

/* Ocultar navegación antigua */
.main-nav {
    display: none !important;
}
