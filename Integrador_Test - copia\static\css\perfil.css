/* Base Styles */
:root {
    --primary-color: #4caf50;
    --primary-light: #80e27e;
    --primary-dark: #087f23;
    --secondary-color: #2196f3;
    --text-color: #333333;
    --text-light: #757575;
    --background-color: #ffffff;
    --background-light: #f5f5f5;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --border-radius: 8px;
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Dark Theme */
.dark-theme {
    --primary-color: #81c784;
    --primary-light: #b2fab4;
    --primary-dark: #519657;
    --secondary-color: #64b5f6;
    --text-color: #f5f5f5;
    --text-light: #b0b0b0;
    --background-color: #121212;
    --background-light: #1e1e1e;
    --border-color: #333333;
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--background-light);
    transition: var(--transition);
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    border: none;
    outline: none;
    text-decoration: none;
    gap: 8px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn-text {
    background-color: transparent;
    color: var(--primary-color);
    padding: 4px 8px;
}

.btn-text:hover {
    background-color: rgba(76, 175, 80, 0.1);
}

.btn-sm {
    padding: 4px 12px;
    font-size: 0.875rem;
}

.icon-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: transparent;
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.icon-button:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.dark-theme .icon-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Header */
.app-header {
    background-color: var(--background-color);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
    padding: 12px 0;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--primary-color);
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 500;
}

.main-nav ul {
    display: flex;
    list-style: none;
    gap: 24px;
}

.main-nav a {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
    transition: var(--transition);
    padding: 8px 0;
    position: relative;
}

.main-nav a:hover,
.main-nav a.active {
    color: var(--primary-color);
}

.main-nav a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: var(--transition);
}

.main-nav a:hover::after,
.main-nav a.active::after {
    width: 100%;
}

.user-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
    border: none;
    background: none;
    padding: 0;
}

.avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-menu {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--background-color);
    box-shadow: var(--shadow);
    border-radius: var(--border-radius);
    width: 200px;
    padding: 8px 0;
    margin-top: 8px;
    display: none;
    z-index: 1000;
}

.dropdown-menu.active {
    display: block;
}

.dropdown-menu a {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    text-decoration: none;
    color: var(--text-color);
    transition: var(--transition);
}

.dropdown-menu a:hover,
.dropdown-menu a.active {
    background-color: var(--background-light);
    color: var(--primary-color);
}

/* Main Content */
main {
    padding: 40px 0;
    min-height: calc(100vh - 64px - 300px); /* Viewport height minus header and footer */
}

.page-header {
    margin-bottom: 32px;
    text-align: center;
}

.page-header h1 {
    font-size: 2.5rem;
    margin-bottom: 8px;
    color: var(--primary-color);
}

.subtitle {
    font-size: 1.25rem;
    color: var(--text-light);
}

/* Profile Container */
.profile-container {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 32px;
}

@media (max-width: 768px) {
    .profile-container {
        grid-template-columns: 1fr;
    }
}

/* Profile Sidebar */
.profile-sidebar {
    background-color: var(--background-color);
    border-radius: var(--border-radius);
    padding: 24px;
    box-shadow: var(--shadow);
    text-align: center;
}

.profile-avatar {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto 16px;
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid var(--primary-light);
}

.change-avatar-btn {
    position: absolute;
    bottom: 0;
    right: 0;
    background-color: var(--primary-color);
    color: white;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.change-avatar-btn:hover {
    background-color: var(--primary-dark);
}

.profile-name {
    font-size: 1.5rem;
    margin-bottom: 8px;
}

.profile-location {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    color: var(--text-light);
    margin-bottom: 16px;
}

.profile-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 24px;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 500;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-light);
}

.profile-actions {
    margin-top: 24px;
}

/* Profile Content */
.profile-content {
    background-color: var(--background-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--background-light);
}

.tab-btn {
    padding: 16px 24px;
    background-color: transparent;
    border: none;
    cursor: pointer;
    font-weight: 500;
    color: var(--text-light);
    transition: var(--transition);
    position: relative;
}

.tab-btn:hover {
    color: var(--primary-color);
}

.tab-btn.active {
    color: var(--primary-color);
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--primary-color);
}

.tab-content {
    display: none;
    padding: 24px;
}

.tab-content.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.section-header h3 {
    font-size: 1.25rem;
    color: var(--primary-color);
}

/* Plants Grid */
.plants-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 24px;
}

.plant-card {
    background-color: var(--background-light);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.plant-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.plant-image {
    position: relative;
    height: 180px;
}

.plant-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.plant-status {
    position: absolute;
    top: 12px;
    right: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    color: white;
}

.plant-status.healthy {
    background-color: var(--success-color);
}

.plant-status.warning {
    background-color: var(--warning-color);
}

.plant-status.danger {
    background-color: var(--danger-color);
}

.plant-info {
    padding: 16px;
}

.plant-info h4 {
    margin-bottom: 4px;
}

.scientific-name {
    font-style: italic;
    color: var(--text-light);
    margin-bottom: 12px;
    font-size: 0.875rem;
}

.plant-care-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 16px;
}

.care-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    font-size: 0.875rem;
    color: var(--text-light);
}

.care-details {
    display: flex;
    flex-direction: column;
}

.care-label {
    font-size: 0.75rem;
    color: var(--text-light);
}

.care-value {
    font-size: 0.875rem;
    color: var(--text-color);
    font-weight: 500;
}

.plant-actions {
    display: flex;
    justify-content: space-between;
}

/* Activity Timeline */
.activity-timeline {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.activity-item {
    display: flex;
    gap: 16px;
    padding: 16px;
    background-color: var(--background-light);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.activity-item:hover {
    box-shadow: var(--shadow);
}

.activity-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--primary-light);
    color: var(--primary-dark);
    border-radius: 50%;
    flex-shrink: 0;
}

.activity-text {
    margin-bottom: 4px;
}

.activity-date {
    font-size: 0.875rem;
    color: var(--text-light);
}

/* Saved Items */
.saved-categories {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
}

.category-btn {
    padding: 8px 16px;
    background-color: transparent;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
}

.category-btn:hover {
    background-color: var(--background-light);
}

.category-btn.active {
    background-color: var(--primary-color);
    color: white;
}

.saved-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 24px;
}

.saved-card {
    background-color: var(--background-light);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.saved-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.saved-card img {
    width: 100%;
    height: 120px;
    object-fit: cover;
}

.saved-info {
    padding: 12px;
}

.saved-info h4 {
    margin-bottom: 4px;
}

.saved-info p {
    font-style: italic;
    color: var(--text-light);
    margin-bottom: 8px;
    font-size: 0.875rem;
}

/* Notifications */
.notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: var(--background-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 300px;
    max-width: 400px;
    transform: translateX(120%);
    transition: transform 0.3s ease;
    z-index: 2000;
}

.notification.visible {
    transform: translateX(0);
}

.notification.success {
    border-left: 4px solid var(--success-color);
}

.notification.success .material-icons {
    color: var(--success-color);
}

.notification.error {
    border-left: 4px solid var(--danger-color);
}

.notification.error .material-icons {
    color: var(--danger-color);
}

.notification.warning {
    border-left: 4px solid var(--warning-color);
}

.notification.warning .material-icons {
    color: var(--warning-color);
}

.notification .message {
    flex: 1;
    color: var(--text-color);
}

.notification .close-notification {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
}

.notification .close-notification:hover {
    color: var(--text-color);
}

/* Footer */
.app-footer {
    background-color: var(--background-color);
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    padding: 40px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 32px;
    margin-bottom: 32px;
}

.footer-section h3 {
    color: var(--primary-color);
    margin-bottom: 16px;
    font-size: 1.125rem;
}

.footer-section p {
    color: var(--text-light);
    margin-bottom: 16px;
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    margin-bottom: 8px;
}

.footer-section a {
    text-decoration: none;
    color: var(--text-light);
    transition: var(--transition);
}

.footer-section a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
    color: var(--text-light);
    font-size: 0.875rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 16px;
    }

    .main-nav ul {
        flex-wrap: wrap;
        justify-content: center;
        gap: 16px;
    }

    .user-actions {
        margin-top: 16px;
    }

    .profile-container {
        grid-template-columns: 1fr;
    }

    .plants-grid,
    .saved-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
    }
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    overflow-y: auto;
    padding: 20px;
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: var(--background-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    width: 100%;
    max-width: 500px;
    position: relative;
    animation: modalFadeIn 0.3s;
}

.modal-content.modal-lg {
    max-width: 800px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    font-size: 1.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.close-modal {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-light);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.close-modal:hover {
    color: var(--text-color);
}

.modal-body {
    padding: 24px;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-color);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--background-color);
    color: var(--text-color);
    font-family: 'Roboto', sans-serif;
    transition: var(--transition);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    margin-top: 24px;
}

/* Avatar Change Modal */
.avatar-preview {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 24px;
    border: 4px solid var(--primary-color);
}

.avatar-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-options {
    margin-bottom: 24px;
}

.upload-option {
    text-align: center;
    margin-bottom: 24px;
}

.default-avatars h3 {
    font-size: 1rem;
    text-align: center;
    margin-bottom: 16px;
    color: var(--text-light);
}

.avatar-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 16px;
}

.avatar-option {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
    border: 2px solid transparent;
    transition: var(--transition);
}

.avatar-option:hover,
.avatar-option.selected {
    border-color: var(--primary-color);
    transform: scale(1.1);
}

.avatar-option img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Plant Detail Modal */
.plant-detail-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.plant-detail-header {
    display: flex;
    gap: 24px;
}

.plant-detail-image {
    width: 200px;
    height: 200px;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.plant-detail-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.plant-detail-info {
    flex: 1;
}

.plant-detail-info h3 {
    font-size: 1.5rem;
    margin-bottom: 4px;
}

.plant-status-container {
    margin: 12px 0;
}

.plant-attributes {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    margin-top: 16px;
}

.attribute {
    display: flex;
    align-items: center;
    gap: 12px;
}

.attribute .material-icons {
    color: var(--primary-color);
}

.attribute-info {
    display: flex;
    flex-direction: column;
}

.attribute-label {
    font-size: 0.75rem;
    color: var(--text-light);
}

.attribute-value {
    font-weight: 500;
}

.plant-detail-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 24px;
}

.detail-tab-btn {
    padding: 12px 24px;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    font-weight: 500;
    color: var(--text-light);
    transition: var(--transition);
}

.detail-tab-btn:hover {
    color: var(--primary-color);
}

.detail-tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.plant-detail-tab-content {
    display: none;
}

.plant-detail-tab-content.active {
    display: block;
}

.plant-detail-tab-content h3 {
    margin-bottom: 16px;
    color: var(--text-color);
}

/* Care Instructions */
.care-instructions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
    margin-bottom: 24px;
}

.care-item {
    display: flex;
    gap: 16px;
    padding: 16px;
    background-color: var(--background-light);
    border-radius: var(--border-radius);
}

.care-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--primary-color);
}

.care-info h4 {
    margin-bottom: 8px;
}

.care-info p {
    font-size: 0.875rem;
    color: var(--text-light);
}

.next-care-reminder {
    padding: 16px;
    background-color: rgba(76, 175, 80, 0.05);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
}

.next-care-reminder h4 {
    margin-bottom: 8px;
}

.reminder-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    color: var(--text-color);
}

/* Care History */
.care-history {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 24px;
}

.care-history-item {
    display: flex;
    gap: 16px;
    padding: 16px;
    background-color: var(--background-light);
    border-radius: var(--border-radius);
}

.care-date {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-width: 60px;
    padding: 8px;
    background-color: var(--background-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.care-date .day {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

.care-date .month {
    font-size: 0.75rem;
    color: var(--text-light);
}

.care-details {
    flex: 1;
}

.care-type {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-weight: 500;
}

.care-type .material-icons {
    font-size: 18px;
    color: var(--primary-color);
}

.care-notes {
    font-size: 0.875rem;
    color: var(--text-light);
}

/* Notes */
.notes-container {
    margin-bottom: 24px;
}

.note-item {
    padding: 16px;
    background-color: var(--background-light);
    border-radius: var(--border-radius);
    margin-bottom: 16px;
}

.note-date {
    font-size: 0.75rem;
    color: var(--text-light);
    margin-bottom: 8px;
}

.add-note-form textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--background-color);
    color: var(--text-color);
    font-family: 'Roboto', sans-serif;
    resize: vertical;
    min-height: 100px;
    margin-bottom: 16px;
}

.add-note-form textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* Responsive Styles for Modals */
@media (max-width: 768px) {
    .plant-detail-header {
        flex-direction: column;
    }

    .plant-detail-image {
        width: 100%;
        height: auto;
        aspect-ratio: 1/1;
    }

    .plant-attributes {
        grid-template-columns: 1fr;
    }

    .care-instructions {
        grid-template-columns: 1fr;
    }
}

/* Change Avatar Button */
.change-avatar-btn {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow);
}

.change-avatar-btn:hover {
    background-color: var(--primary-dark);
    transform: scale(1.1);
}

.profile-avatar {
    position: relative;
}

/* Estilos para el modal de biblioteca de plantas */
.library-plants-container {
    margin: 20px 0;
    max-height: 500px;
    overflow-y: auto;
}

.library-plant-card {
    display: flex;
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 15px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.library-plant-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.library-plant-card .plant-image {
    width: 120px;
    min-width: 120px;
    height: 120px;
    position: relative;
    overflow: hidden;
}

.library-plant-card .plant-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.library-plant-card .plant-info {
    padding: 15px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.library-plant-card .plant-name {
    margin: 0 0 5px;
    font-size: 18px;
}

.library-plant-card .plant-scientific-name {
    font-style: italic;
    color: #666;
    margin: 0 0 10px;
    font-size: 14px;
}

.library-plant-card .plant-attributes {
    display: flex;
    margin-bottom: 10px;
}

.library-plant-card .attribute {
    display: flex;
    align-items: center;
    margin-right: 15px;
    font-size: 14px;
}

.library-plant-card .attribute .material-icons {
    font-size: 18px;
    margin-right: 5px;
    color: var(--primary-color);
}

.library-plant-card .select-plant {
    margin-top: auto;
    align-self: flex-start;
}

/* Estilos para notificaciones toast */
.toast-notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: #4CAF50;
    color: white;
    padding: 10px 20px;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    z-index: 1100;
    animation: fadeIn 0.3s, fadeOut 0.3s 2.7s;
}

.toast-content {
    display: flex;
    align-items: center;
}

.toast-content .material-icons {
    margin-right: 10px;
}

@keyframes fadeIn {
    from {opacity: 0; transform: translateY(20px);}
    to {opacity: 1; transform: translateY(0);}
}

@keyframes fadeOut {
    from {opacity: 1; transform: translateY(0);}
    to {opacity: 0; transform: translateY(-20px);}
}

/* Estilos para resultados vacíos */
.no-results {
    padding: 30px;
    text-align: center;
    color: #666;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.no-results .material-icons {
    font-size: 48px;
    margin-bottom: 15px;
    color: #999;
}

/* Modificación para que los modales sean más grandes cuando sea necesario */
.modal-lg {
    width: 80%;
    max-width: 900px;
}
