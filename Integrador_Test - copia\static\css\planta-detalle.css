/* Plant Detail Page Styles */

/* Global styles specific to plant details page */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

main {
    padding-top: 2rem;
    padding-bottom: 4rem;
}

/* Breadcrumb styles */
.breadcrumb {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    font-size: 0.9rem;
    color: #666;
}

.breadcrumb a {
    color: #4CAF50;
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.breadcrumb .material-icons {
    font-size: 18px;
    margin: 0 0.5rem;
}

/* Plant details section */
.plant-details {
    margin-bottom: 4rem;
}

.plant-details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 3rem;
}

/* Plant images gallery */
.plant-images {
    border-radius: 12px;
    overflow: hidden;
    background-color: #f9f9f9;
}

.main-image {
    width: 100%;
    height: 400px;
    overflow: hidden;
    position: relative;
}

.main-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.thumbnail-gallery {
    display: flex;
    padding: 1rem;
    gap: 0.5rem;
}

.thumbnail {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    object-fit: cover;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.thumbnail.active {
    border-color: #4CAF50;
}

.thumbnail:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Plant info card */
.plant-info-card {
    background-color: #fff;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.plant-info-card h1 {
    margin: 0 0 0.5rem 0;
    color: #333;
}

.scientific-name {
    font-style: italic;
    color: #666;
    margin-top: 0;
    margin-bottom: 1rem;
}

.plant-badges {
    margin-bottom: 1.5rem;
    display: flex;
    gap: 0.5rem;
}

.badge {
    display: inline-block;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.native-badge {
    background-color: #E8F5E9;
    color: #2E7D32;
}

/* Plant care overview */
.plant-care-overview {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
}

.plant-care-overview h3 {
    margin-bottom: 1rem;
    color: #333;
}

.care-attributes {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.attribute {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.attribute .material-icons {
    color: #4CAF50;
    font-size: 1.5rem;
}

.attribute h4 {
    margin: 0 0 0.25rem 0;
    font-size: 0.9rem;
    color: #666;
}

.attribute p {
    margin: 0;
    font-weight: 500;
    color: #333;
}

/* Action buttons */
.action-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    border: none;
}

.btn .material-icons {
    font-size: 1.25rem;
}

.btn-primary {
    background-color: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background-color: #43A047;
}

.btn-outline {
    background-color: transparent;
    color: #4CAF50;
    border: 1px solid #4CAF50;
}

.btn-outline:hover {
    background-color: #E8F5E9;
}

.btn-success {
    background-color: #8BC34A;
    color: white;
    margin-top: 10px;
    width: 100%;
}

.btn-success:hover {
    background-color: #7CB342;
}

.btn-success.added {
    background-color: #4CAF50;
    pointer-events: none;
}

/* Animación de carga */
.material-icons.loading {
    animation: spin 1.5s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notificaciones */
.notification-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.notification {
    background-color: white;
    color: #333;
    border-radius: 8px;
    padding: 15px 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 300px;
    max-width: 400px;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification p {
    margin: 0;
    flex-grow: 1;
}

.notification .material-icons {
    font-size: 24px;
}

.notification.success {
    border-left: 4px solid #4CAF50;
}

.notification.success .material-icons {
    color: #4CAF50;
}

.notification.error {
    border-left: 4px solid #F44336;
}

.notification.error .material-icons {
    color: #F44336;
}

.notification.warning {
    border-left: 4px solid #FFC107;
}

.notification.warning .material-icons {
    color: #FFC107;
}

.notification.info {
    border-left: 4px solid #2196F3;
}

.notification.info .material-icons {
    color: #2196F3;
}

.dark-theme .notification {
    background-color: #333;
    color: #fff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Tabs */
.plant-details-tabs {
    background-color: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.tabs-header {
    display: flex;
    border-bottom: 1px solid #e0e0e0;
    background-color: #f9f9f9;
}

.tab-button {
    padding: 1rem 1.5rem;
    background-color: transparent;
    border: none;
    cursor: pointer;
    font-weight: 500;
    color: #666;
    transition: all 0.2s ease;
    position: relative;
}

.tab-button:hover {
    color: #4CAF50;
}

.tab-button.active {
    color: #4CAF50;
}

.tab-button.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background-color: #4CAF50;
}

.tabs-content {
    padding: 2rem;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.tab-content h2 {
    margin-top: 0;
    margin-bottom: 1rem;
    color: #333;
}

.tab-content h3 {
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
    color: #444;
}

.tab-content p {
    margin-bottom: 1.5rem;
    line-height: 1.6;
    color: #555;
}

.tab-content ul {
    padding-left: 1.5rem;
    margin-bottom: 1.5rem;
}

.tab-content li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

/* Estilos para la biografía de la planta */
.plant-biography {
    background-color: #f5f9f5;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border-left: 4px solid #4CAF50;
}

.plant-biography p {
    margin-bottom: 1rem;
}

.plant-biography p:last-child {
    margin-bottom: 0;
}

/* Estilos para la distribución en Chihuahua */
.plant-distribution {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

#plant-regions li {
    padding-left: 0.5rem;
    border-left: 2px solid #4CAF50;
    margin-bottom: 0.75rem;
}

#plant-best-region {
    font-weight: 500;
    color: #2E7D32;
}

/* Problem cards */
.problem-card {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.problem-card h3 {
    margin-top: 0;
}

/* Related plants section */
.related-plants {
    margin-top: 3rem;
}

.related-plants h2 {
    margin-bottom: 1.5rem;
    color: #333;
}

.plants-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
}

.plant-card {
    background-color: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}

.plant-card:hover {
    transform: translateY(-5px);
}

.plant-image {
    height: 200px;
    position: relative;
    overflow: hidden;
}

.plant-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.plant-info {
    padding: 1.5rem;
}

.plant-name {
    margin: 0 0 0.25rem 0;
    font-size: 1.25rem;
}

.plant-scientific-name {
    margin: 0 0 1rem 0;
    font-style: italic;
    color: #666;
    font-size: 0.9rem;
}

.view-details {
    width: 100%;
    text-align: center;
    text-decoration: none;
    justify-content: center;
    margin-top: 1rem;
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal.active {
    display: flex;
}

.modal-content {
    background-color: #fff;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.25rem;
}

.close-button {
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
}

.close-button:hover {
    color: #333;
}

.modal-body {
    padding: 1.5rem;
}

.form-group {
    margin-bottom: 1.25rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #555;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-family: inherit;
    font-size: 1rem;
}

.form-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 1rem;
}

/* Add animation for the favorite button */
.btn-favorite.active .material-icons {
    color: #FF4081;
    animation: pulse 0.3s ease;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* Navigation color adjustment for better visibility */
.navbar-logo,
.nav-item a,
.nav-link,
.navigation-text {
    color: #333333; /* Dark gray color for better visibility on white background */
}

.navbar-logo:hover,
.nav-item a:hover,
.nav-link:hover,
.navigation-text:hover {
    color: #4CAF50; /* Green color on hover for consistency with site theme */
}

/* If the navigation uses icons with text */
.nav-icon,
.menu-icon {
    color: #333333;
}

/* If there are specific classes for each navigation item */
.nav-inicio, .nav-biblioteca, .nav-scanner, .nav-calendario, .nav-foro {
    color: #333333;
}

/* Navigation color adjustment for better visibility - with higher specificity */
header .navbar-logo,
header .nav-item a,
header .nav-link,
header .navigation-text,
nav .navbar-logo,
nav .nav-item a,
nav .nav-link,
nav .navigation-text,
.navbar .navbar-logo,
.navbar .nav-item a,
.navbar .nav-link,
.navbar .navigation-text {
    color: #333333 !important; /* Using !important to override any other styles */
    text-shadow: none !important; /* Remove any text shadows that might make text hard to see */
}

/* Target specific navigation items by their text content or likely IDs */
#logo, #inicio, #biblioteca, #scanner, #calendario, #foro,
a[href*="inicio"], a[href*="biblioteca"], a[href*="scanner"], a[href*="calendario"], a[href*="foro"],
.logo, .inicio, .biblioteca, .scanner, .calendario, .foro {
    color: #333333 !important;
}

/* Target menu items more generally */
.menu-item, .nav-item, .navbar-item {
    color: #333333 !important;
}

/* Hover states */
header a:hover,
nav a:hover,
.navbar a:hover,
.menu-item:hover,
.nav-item:hover {
    color: #4CAF50 !important;
}

/* Specific styling for the inicio button with maximum specificity */
#inicio,
.inicio,
a#inicio,
a.inicio,
[href="inicio.html"],
[href="./inicio.html"],
[href="../inicio.html"],
[href*="inicio"],
.navbar a[href*="inicio"],
header a[href*="inicio"],
nav a[href*="inicio"],
.navigation a:first-child,
.nav-item:first-child a,
.menu-item:first-child {
    color: #333333 !important;
    font-weight: bold !important;
    text-shadow: none !important;
    background-color: transparent !important;
}

/* Improve visibility for all navigation elements by adding background effect on hover */
.navbar a:hover,
.nav-item a:hover,
.menu-item:hover,
header a:hover,
nav a:hover,
#inicio:hover,
.inicio:hover {
    background-color: rgba(76, 175, 80, 0.1) !important; /* Light green background on hover */
    border-radius: 4px !important;
    transition: all 0.2s ease !important;
}

/* Make the inicio button specifically black with highest priority */
#inicio,
.inicio,
a#inicio,
a.inicio,
button#inicio,
button.inicio,
[href="inicio.html"],
[href="./inicio.html"],
[href="../inicio.html"],
[href*="inicio"],
.navbar a[href*="inicio"],
header a[href*="inicio"],
nav a[href*="inicio"],
.navigation a:first-child,
.nav-item:first-child a,
.menu-item:first-child,
.inicio-link {
    color: #000000 !important; /* Pure black */
    font-weight: bold !important;
    text-shadow: none !important;
    background-color: transparent !important;
}

/* Responsive styles */
@media (max-width: 992px) {
    .plant-details-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .plants-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .care-attributes {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        flex-direction: column;
    }
}

@media (max-width: 576px) {
    .plants-grid {
        grid-template-columns: 1fr;
    }

    .tabs-header {
        flex-wrap: wrap;
    }

    .tab-button {
        flex: 1;
        font-size: 0.9rem;
        padding: 0.75rem;
        text-align: center;
    }
}
