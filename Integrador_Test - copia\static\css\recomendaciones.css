/* ===== ESTILOS BASE ===== */
:root {
    --primary: #4CAF50;
    --primary-light: #81C784;
    --primary-dark: #388E3C;
    --accent: #FF9800;
    --text-dark: #263238;
    --text-light: #ECEFF1;
    --background: #FFFFFF;
    --background-alt: #F5F8F5;
    --card-bg: #FFFFFF;
    --shadow: 0 8px 30px rgba(0,0,0,0.05);
    --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    --border-radius: 8px;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --border-color: #e0e0e0;
}

/* Dark Theme */
.dark-theme {
    --primary: #81C784;
    --primary-light: #b2fab4;
    --primary-dark: #519657;
    --accent: #FFB74D;
    --text-dark: #f5f5f5;
    --text-light: #b0b0b0;
    --background: #121212;
    --background-alt: #1e1e1e;
    --card-bg: #1e1e1e;
    --border-color: #333333;
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    color: var(--text-dark);
    background-color: var(--background-alt);
    margin: 0;
    padding: 0;
    line-height: 1.6;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* ===== HEADER & NAVEGACIÓN ===== */
.app-header {
    background-color: var(--background);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 100;
    transition: var(--transition);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--primary);
}

.logo h1 {
    font-size: 1.5rem;
}

.main-nav ul {
    display: flex;
    list-style: none;
    gap: 24px;
}

.main-nav a {
    text-decoration: none;
    color: var(--text-dark);
    padding: 8px 0;
    position: relative;
    font-weight: 500;
    transition: var(--transition);
}

.main-nav a:hover {
    color: var(--primary);
}

.main-nav a.active {
    color: var(--primary);
}

.main-nav a.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--primary);
}

.user-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.icon-button {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: var(--transition);
}

.icon-button:hover {
    background-color: var(--background-light);
    color: var(--primary-color);
}

.user-menu {
    position: relative;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
    padding: 0;
    border: none;
    background: none;
}

.avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.dropdown-menu {
    position: absolute;
    top: 50px;
    right: 0;
    background-color: var(--background-color);
    box-shadow: var(--shadow);
    border-radius: var(--border-radius);
    width: 200px;
    overflow: hidden;
    display: none;
    z-index: 10;
}

.dropdown-menu.active {
    display: block;
}

.dropdown-menu a {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    text-decoration: none;
    color: var(--text-color);
    transition: var(--transition);
}

.dropdown-menu a:hover {
    background-color: var(--background-light);
}

.dropdown-menu a.active {
    color: var(--primary-color);
    background-color: var(--background-light);
}

/* Main Content */
main {
    padding: 32px 0;
}

.page-header {
    text-align: center;
    margin-bottom: 32px;
}

.page-header h1 {
    font-size: 2.5rem;
    color: var(--primary);
    margin-bottom: 8px;
}

.subtitle {
    color: var(--text-light);
    font-size: 1.1rem;
}

/* Recommendations Styles */
.recommendations-container {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 24px;
}

.recommendations-sidebar {
    background-color: var(--background);
    border-radius: var(--border-radius);
    padding: 24px;
    box-shadow: var(--shadow);
    align-self: start;
    position: sticky;
    top: 100px;
}

.filter-section h3 {
    margin-bottom: 16px;
    color: var(--primary);
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group select,
.form-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--background-light);
    color: var(--text-color);
    transition: var(--transition);
}

.form-group select:focus,
.form-group input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.checkbox-label input {
    width: 16px;
    height: 16px;
}

.btn {
    padding: 10px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    border: none;
    outline: none;
}

.btn-primary {
    background-color: var(--primary);
    color: white;
    width: 100%;
    justify-content: center;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-color);
}

.btn-outline:hover {
    border-color: var(--primary);
    color: var(--primary);
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.875rem;
}

.recommendations-content {
    background-color: var(--background);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 24px;
}

.recommendations-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.recommendations-header h2 {
    color: var(--primary);
}

.recommendations-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.recommendations-actions select {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--background-light);
    color: var(--text-color);
    transition: var(--transition);
}

.recommendations-actions select:focus {
    border-color: var(--primary-color);
    outline: none;
}

.view-toggle {
    display: flex;
    background-color: var(--background-light);
    border-radius: 20px;
    overflow: hidden;
}

.view-btn {
    background: none;
    border: none;
    padding: 8px 12px;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-light);
}

.view-btn.active {
    background-color: var(--primary);
    color: white;
}

.plants-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 24px;
    transition: var(--transition);
}

/* List View */
.plants-grid.list-view {
    grid-template-columns: 1fr;
}

.plants-grid.list-view .plant-card {
    display: flex;
    flex-direction: row;
    height: auto;
}

.plants-grid.list-view .plant-image {
    width: 200px;
    height: 200px;
    flex-shrink: 0;
}

.plants-grid.list-view .plant-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.plants-grid.list-view .plant-attributes {
    margin-top: auto;
}

.no-results {
    grid-column: 1 / -1;
    text-align: center;
    padding: 40px;
    background-color: var(--background);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    color: var(--text-light);
    font-size: 1.1rem;
}

.plant-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    border: 1px solid var(--border-color);
}

.plant-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.match-badge {
    position: absolute;
    top: 16px;
    right: 16px;
    background-color: rgba(76, 175, 80, 0.9);
    color: white;
    padding: 4px 8px;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 700;
    z-index: 2;
}

.high-match .match-badge {
    background-color: rgba(76, 175, 80, 0.9);
}

.medium-match .match-badge {
    background-color: rgba(255, 152, 0, 0.9);
}

.low-match .match-badge {
    background-color: rgba(244, 67, 54, 0.9);
}

/* Experience badge */
.experience-badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 20px;
    margin-left: 15px;
    font-weight: 500;
    font-size: 14px;
}

.experience-badge .material-icons {
    margin-right: 6px;
    font-size: 18px;
}

.experience-badge.beginner {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.experience-badge.intermediate {
    background-color: #fff3e0;
    color: #ef6c00;
}

.experience-badge.advanced {
    background-color: #e3f2fd;
    color: #1565c0;
}

.dark-theme .experience-badge.beginner {
    background-color: rgba(46, 125, 50, 0.2);
    color: #81c784;
}

.dark-theme .experience-badge.intermediate {
    background-color: rgba(239, 108, 0, 0.2);
    color: #ffb74d;
}

.dark-theme .experience-badge.advanced {
    background-color: rgba(21, 101, 192, 0.2);
    color: #64b5f6;
}

.plant-image {
    position: relative;
    height: 180px;
    overflow: hidden;
}

.plant-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.plant-card:hover .plant-image img {
    transform: scale(1.05);
}

.plant-badges {
    position: absolute;
    bottom: 16px;
    left: 16px;
    display: flex;
    gap: 8px;
}

.badge {
    padding: 4px 8px;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 500;
}

.native-badge {
    background-color: rgba(33, 150, 243, 0.9);
    color: white;
}

.purifying-badge {
    background-color: rgba(76, 175, 80, 0.9);
    color: white;
}

.edible-badge {
    background-color: rgba(255, 152, 0, 0.9);
    color: white;
}

.plant-info {
    padding: 16px;
}

.plant-name {
    font-size: 1.25rem;
    margin-bottom: 4px;
}

.plant-scientific-name {
    color: var(--text-light);
    font-style: italic;
    margin-bottom: 16px;
    font-size: 0.875rem;
}

.plant-attributes {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
}

.attribute {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 0.875rem;
}

.attribute .material-icons {
    color: var(--primary-color);
    margin-bottom: 4px;
}

.plant-actions {
    display: flex;
    justify-content: space-between;
    gap: 8px;
}

.plant-actions .btn {
    flex: 1;
}

.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-info {
    color: var(--text-light);
}

/* Footer Styles */
.app-footer {
    background-color: var(--background-color);
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    margin-top: 48px;
    padding: 48px 0 0;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 32px;
    margin-bottom: 32px;
}

.footer-section h3 {
    margin-bottom: 16px;
    color: var(--primary-color);
}

.footer-section p {
    color: var(--text-light);
    margin-bottom: 16px;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-section ul a {
    text-decoration: none;
    color: var(--text-color);
    transition: var(--transition);
}

.footer-section ul a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding: 16px 0;
    border-top: 1px solid var(--border-color);
    color: var(--text-light);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .recommendations-container {
        grid-template-columns: 1fr;
    }

    .recommendations-sidebar {
        position: static;
        margin-bottom: 24px;
    }
}

@media (max-width: 768px) {
    .main-nav {
        display: none;
    }

    .recommendations-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }

    .recommendations-actions {
        width: 100%;
        justify-content: space-between;
    }

    .plants-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    .pagination {
        flex-direction: column;
        gap: 16px;
    }

    .plant-attributes {
        flex-wrap: wrap;
        gap: 8px;
    }

    .plant-actions {
        flex-direction: column;
    }
}
