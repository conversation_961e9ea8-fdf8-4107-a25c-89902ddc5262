/* ===== ESTILOS BASE ===== */
:root {
    --primary: #4CAF50;
    --primary-light: #81C784;
    --primary-dark: #388E3C;
    --accent: #FF9800;
    --text-dark: #263238;
    --text-light: #ECEFF1;
    --background: #FFFFFF;
    --background-alt: #F5F8F5;
    --card-bg: #FFFFFF;
    --shadow: 0 8px 30px rgba(0,0,0,0.05);
    --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

body {
    font-family: 'Roboto', sans-serif;
    color: var(--text-dark);
    background-color: var(--background);
    margin: 0;
    padding: 0;
    line-height: 1.6;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* ===== HEADER & NAVEGACIÓN ===== */
.app-header {
    background-color: var(--background);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 100;
    transition: var(--transition);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo .material-icons {
    color: var(--primary);
    font-size: 28px;
}

.logo h1 {
    font-size: 24px;
    font-weight: 300;
    color: var(--primary-dark);
    margin: 0;
    letter-spacing: 1px;
}

.main-nav ul {
    display: flex;
    list-style: none;
    gap: 30px;
    margin: 0;
    padding: 0;
}

.main-nav a {
    color: var(--text-dark);
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    padding: 8px 2px;
    position: relative;
    transition: var(--transition);
}

.main-nav a:hover,
.main-nav a.active {
    color: var(--primary);
}

.main-nav a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.main-nav a:hover::after,
.main-nav a.active::after {
    transform: scaleX(1);
}

/* User actions & menu */
.user-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-menu {
    position: relative;
}

.avatar {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    border: 2px solid var(--primary-light);
    overflow: hidden;
    padding: 0;
    cursor: pointer;
    background: none;
    transition: var(--transition);
}

.avatar:hover {
    border-color: var(--primary);
    transform: scale(1.05);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.dropdown-menu {
    position: absolute;
    top: calc(100% + 10px);
    right: 0;
    background-color: var(--card-bg);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    width: 220px;
    z-index: 1000;
    padding: 8px 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
}

.dropdown-menu.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 16px;
    width: 12px;
    height: 12px;
    background-color: var(--card-bg);
    transform: rotate(45deg);
    box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.04);
}

.dropdown-menu a {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    color: var(--text-dark);
    text-decoration: none;
    transition: var(--transition);
}

.dropdown-menu a:hover {
    background-color: rgba(76, 175, 80, 0.08);
    color: var(--primary);
}

.dropdown-menu a .material-icons {
    color: var(--primary);
    font-size: 20px;
}

/* Auth buttons */
.auth-buttons {
    display: flex;
    gap: 12px;
    align-items: center;
}

.auth-buttons .btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border-radius: 8px;
    font-weight: 500;
    font-size: 14px;
    transition: var(--transition);
    cursor: pointer;
}

.auth-buttons .login-btn {
    color: var(--primary-dark);
    border: 1px solid var(--primary-light);
    background: transparent;
}

.auth-buttons .login-btn:hover {
    background-color: rgba(76, 175, 80, 0.08);
}

.auth-buttons .register-btn {
    color: white;
    background-color: var(--primary);
    border: 1px solid var(--primary);
}

.auth-buttons .register-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2);
}

/* ===== PAGE HEADER ===== */
.page-header {
    text-align: center;
    padding: 60px 0 40px;
    max-width: 800px;
    margin: 0 auto;
}

.page-header h1 {
    font-size: 2.5rem;
    font-weight: 300;
    color: var(--primary-dark);
    margin-bottom: 15px;
    animation: fadeInDown 0.8s ease forwards;
}

.page-header .subtitle {
    font-size: 1.2rem;
    color: #546E7A;
    margin: 0;
    animation: fadeInUp 0.8s ease 0.2s forwards;
    opacity: 0;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== SCANNER CONTAINER ===== */
.scanner-container {
    margin-bottom: 80px;
}

.scanner-state {
    display: none;
    animation: fadeIn 0.5s ease forwards;
}

.scanner-state.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* ===== INITIAL STATE ===== */
.scanner-card {
    background-color: var(--card-bg);
    border-radius: 16px;
    padding: 40px;
    text-align: center;
    box-shadow: var(--shadow);
    max-width: 700px;
    margin: 0 auto 40px;
    transition: var(--transition);
    transform: translateY(20px);
    opacity: 0;
    animation: slideUpFade 0.8s forwards 0.3s;
}

@keyframes slideUpFade {
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.scanner-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.scanner-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 25px;
    background-color: var(--primary-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.scanner-card:hover .scanner-icon {
    transform: scale(1.1);
    background-color: var(--primary);
}

.scanner-icon .material-icons {
    font-size: 40px;
    color: white;
}

.scanner-card h2 {
    font-size: 28px;
    font-weight: 400;
    color: var(--text-dark);
    margin: 0 0 20px;
}

.scanner-card p {
    color: #546E7A;
    margin-bottom: 30px;
    font-size: 16px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.scanner-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    display: inline-block;
    padding: 12px 25px;
    border-radius: 8px;
    font-weight: 500;
    text-decoration: none;
    text-align: center;
    letter-spacing: 0.3px;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 15px;
}

.btn-primary {
    background-color: var(--primary);
    color: white;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.2);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 6px 18px rgba(76, 175, 80, 0.25);
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-dark);
    box-shadow: inset 0 0 0 2px var(--primary-light);
}

.btn-outline:hover {
    background-color: rgba(76, 175, 80, 0.08);
    box-shadow: inset 0 0 0 2px var(--primary);
    transform: translateY(-2px);
}

.btn-accent {
    background-color: var(--accent);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 152, 0, 0.2);
}

.btn-accent:hover {
    background-color: #F57C00;
    transform: translateY(-2px);
    box-shadow: 0 6px 18px rgba(255, 152, 0, 0.25);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

.btn .material-icons {
    font-size: 20px;
}

/* ===== TIPS SECTION ===== */
.scanner-tips {
    background-color: var(--background-alt);
    border-radius: 16px;
    padding: 30px;
    max-width: 800px;
    margin: 0 auto;
    box-shadow: var(--shadow);
    transform: translateY(20px);
    opacity: 0;
    animation: slideUpFade 0.8s forwards 0.5s;
}

.scanner-tips h3 {
    font-size: 20px;
    font-weight: 500;
    color: var(--primary-dark);
    margin: 0 0 20px;
    text-align: center;
}

.scanner-tips ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.scanner-tips li {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 15px;
    border-radius: 12px;
    background-color: var(--card-bg);
    transition: var(--transition);
}

.scanner-tips li:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.07);
}

.scanner-tips .material-icons {
    font-size: 24px;
    color: var(--primary);
    background-color: rgba(76, 175, 80, 0.1);
    padding: 10px;
    border-radius: 50%;
}

.scanner-tips h4 {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-dark);
    margin: 0 0 5px;
}

.scanner-tips p {
    font-size: 14px;
    color: #546E7A;
    margin: 0;
}

/* ===== CAMERA STATE ===== */
.camera-container {
    max-width: 900px;
    margin: 0 auto;
    position: relative;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    background-color: #000;
    aspect-ratio: 16/9;
}

#camera-feed {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.camera-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none;
}

.camera-frame {
    width: 70%;
    height: 70%;
    border: 2px dashed rgba(255, 255, 255, 0.8);
    border-radius: 8px;
}

.camera-controls {
    position: absolute;
    bottom: 30px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    gap: 20px;
    padding: 0 20px;
}

/* ===== PREVIEW STATE ===== */
.preview-container {
    max-width: 700px;
    margin: 0 auto;
    text-align: center;
}

.preview-header {
    margin-bottom: 30px;
}

.preview-header h2 {
    font-size: 24px;
    font-weight: 500;
    color: var(--text-dark);
    margin: 0 0 10px;
}

.preview-header p {
    font-size: 16px;
    color: #546E7A;
    margin: 0;
}

.preview-image-container {
    border-radius: 16px;
    overflow: hidden;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

#preview-image {
    width: 100%;
    display: block;
}

.preview-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
}

/* ===== LOADING STATE ===== */
.loading-container {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
    padding: 50px 0;
}

.spinner {
    width: 80px;
    height: 80px;
    border: 6px solid rgba(76, 175, 80, 0.2);
    border-radius: 50%;
    border-top-color: var(--primary);
    animation: spin 1.5s linear infinite;
    margin: 0 auto 30px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.loading-container h2 {
    font-size: 24px;
    font-weight: 500;
    color: var(--text-dark);
    margin: 0 0 15px;
}

.loading-container p {
    font-size: 16px;
    color: #546E7A;
    margin: 0 0 30px;
}

.ai-processing-indicators {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin: 0 auto;
    max-width: 300px;
}

.processing-step {
    padding: 15px;
    background-color: rgba(76, 175, 80, 0.1);
    border-radius: 8px;
    color: #546E7A;
    font-size: 15px;
    transition: var(--transition);
}

.processing-step.active {
    background-color: var(--primary);
    color: white;
    transform: translateX(10px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2);
}

/* ===== RESULT STATE ===== */
.result-container {
    max-width: 1000px;
    margin: 0 auto;
}

.result-header {
    text-align: center;
    margin-bottom: 40px;
}

.result-header h2 {
    font-size: 28px;
    font-weight: 500;
    color: var(--primary-dark);
    margin: 0 0 10px;
}

.result-header p {
    font-size: 16px;
    color: #546E7A;
    margin: 0;
}

.result-card {
    display: grid;
    grid-template-columns: minmax(300px, 2fr) 3fr;
    gap: 30px;
    background-color: var(--card-bg);
    border-radius: 16px;
    box-shadow: var(--shadow);
    overflow: hidden;
    margin-bottom: 50px;
}

@media (max-width: 768px) {
    .result-card {
        grid-template-columns: 1fr;
    }
}

.result-image {
    position: relative;
    height: 100%;
    min-height: 300px;
}

.result-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.ai-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 12px;
    border-radius: 50px;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
}

.ai-badge::before {
    content: '•';
    color: var(--primary-light);
    font-size: 20px;
}

.result-info {
    padding: 30px;
}

.result-confidence {
    margin-bottom: 25px;
}

.confidence-bar {
    height: 6px;
    background-color: rgba(76, 175, 80, 0.1);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 8px;
}

.confidence-level {
    height: 100%;
    background-color: var(--primary);
    border-radius: 3px;
}

#confidence-percentage {
    font-size: 14px;
    color: #546E7A;
}

.result-info h3 {
    font-size: 28px;
    font-weight: 500;
    color: var(--text-dark);
    margin: 0 0 5px;
}

.scientific-name {
    font-style: italic;
    font-size: 16px;
    color: #78909C;
    margin: 0 0 25px;
}

.care-instructions {
    margin-bottom: 30px;
}

.care-instructions h4 {
    font-size: 18px;
    font-weight: 500;
    color: var(--text-dark);
    margin: 0 0 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.plant-attributes {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.attribute {
    display: flex;
    gap: 12px;
    align-items: flex-start;
}

.attribute .material-icons {
    color: var(--primary);
    font-size: 20px;
}

.attribute div {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.attribute strong {
    font-weight: 500;
    color: var(--text-dark);
    font-size: 15px;
}

.attribute span {
    font-size: 14px;
    color: #546E7A;
}

.plant-description {
    font-size: 15px;
    color: #546E7A;
    line-height: 1.6;
    margin-bottom: 25px;
}

.care-tips {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
}

.care-tip {
    background-color: rgba(76, 175, 80, 0.08);
    border-radius: 12px;
    padding: 15px;
    display: flex;
    gap: 12px;
    align-items: flex-start;
}

.care-tip .material-icons {
    color: var(--primary);
    font-size: 20px;
}

.care-tip p {
    margin: 0;
    font-size: 14px;
    color: #546E7A;
}

.result-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

/* ===== SIMILAR PLANTS ===== */
.similar-plants {
    margin-top: 50px;
}

.similar-plants h3 {
    font-size: 24px;
    font-weight: 400;
    color: var(--text-dark);
    margin: 0 0 10px;
}

.similar-plants p {
    font-size: 16px;
    color: #546E7A;
    margin: 0 0 30px;
}

.similar-plants-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 25px;
}

.similar-plant-item {
    background-color: var(--card-bg);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow);
    cursor: pointer;
    transition: var(--transition);
}

.similar-plant-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.similar-plant-image {
    width: 100%;
    height: 180px;
    object-fit: cover;
}

.similar-plant-info {
    padding: 20px;
}

.similar-plant-info h4 {
    font-size: 18px;
    font-weight: 500;
    color: var(--text-dark);
    margin: 0 0 5px;
}

.similar-plant-info p {
    font-style: italic;
    font-size: 14px;
    color: #78909C;
    margin: 0 0 10px;
}

.similar-plant-match {
    display: inline-block;
    padding: 5px 10px;
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--primary-dark);
    border-radius: 50px;
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 15px;
}

.similar-view-btn {
    color: var(--primary);
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.similar-view-btn::after {
    content: '→';
    transition: var(--transition);
}

.similar-view-btn:hover::after {
    transform: translateX(5px);
}

/* ===== ERROR STATE ===== */
.error-container {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
    padding: 50px 20px;
    background-color: var(--card-bg);
    border-radius: 16px;
    box-shadow: var(--shadow);
}

.error-icon {
    font-size: 70px;
    color: #F44336;
    margin-bottom: 20px;
}

.error-container h2 {
    font-size: 24px;
    font-weight: 500;
    color: var(--text-dark);
    margin: 0 0 15px;
}

.error-container p {
    font-size: 16px;
    color: #546E7A;
    margin: 0 0 25px;
}

.error-tips {
    background-color: rgba(255, 152, 0, 0.08);
    border-radius: 12px;
    padding: 20px;
    text-align: left;
    margin-bottom: 30px;
}

.error-tips h4 {
    font-size: 16px;
    font-weight: 500;
    color: var(--accent);
    margin: 0 0 15px;
}

.error-tips ul {
    margin: 0;
    padding: 0 0 0 20px;
}

.error-tips li {
    color: #546E7A;
    margin-bottom: 8px;
    font-size: 15px;
}

.error-tips li:last-child {
    margin-bottom: 0;
}

/* ===== FOOTER ===== */
.app-footer {
    background-color: var(--background-alt);
    padding: 80px 0 30px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h3 {
    color: var(--primary-dark);
    margin-bottom: 20px;
    font-weight: 500;
    font-size: 18px;
}

.footer-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul a {
    color: #546E7A;
    text-decoration: none;
    transition: var(--transition);
}

.footer-section ul a:hover {
    color: var(--primary);
    padding-left: 5px;
}

.footer-bottom {
    padding-top: 30px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    text-align: center;
    color: #78909C;
}

/* ===== DARK THEME ===== */
body.dark-theme {
    --background: #121212;
    --background-alt: #1E1E1E;
    --card-bg: #242424;
    --text-dark: #ECEFF1;
    --text-light: #ECEFF1;
    --shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
}

body.dark-theme .logo h1,
body.dark-theme .main-nav a,
body.dark-theme .page-header h1,
body.dark-theme .scanner-card h2,
body.dark-theme .result-info h3,
body.dark-theme .similar-plants h3,
body.dark-theme .error-container h2,
body.dark-theme .care-instructions h4,
body.dark-theme .attribute strong {
    color: var(--text-light);
}

body.dark-theme .page-header .subtitle,
body.dark-theme .scanner-card p,
body.dark-theme .scanner-tips p,
body.dark-theme .loading-container p,
body.dark-theme .result-header p,
body.dark-theme .similar-plants p,
body.dark-theme .plant-description,
body.dark-theme .error-container p,
body.dark-theme .attribute span {
    color: #B0BEC5;
}

body.dark-theme .dropdown-menu::before,
body.dark-theme .dropdown-menu {
    background-color: var(--card-bg);
}

body.dark-theme .btn-outline {
    color: var(--primary-light);
}

body.dark-theme .scanner-tips li {
    background-color: rgba(255, 255, 255, 0.05);
}

body.dark-theme .footer-section ul a {
    color: #B0BEC5;
}

/* ===== ANIMATIONS ===== */
.fade-in {
    animation: fadeIn 0.8s forwards;
}

.fade-in-delay {
    animation: fadeIn 0.8s forwards 0.3s;
    opacity: 0;
}

.slide-up {
    animation: slideUp 0.8s forwards;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== RESPONSIVE ===== */
@media (max-width: 992px) {
    .main-nav ul {
        gap: 20px;
    }
    
    .scanner-card {
        padding: 30px;
    }
    
    .scanner-icon {
        width: 70px;
        height: 70px;
    }
    
    .scanner-tips ul {
        grid-template-columns: 1fr;
    }
    
    .plant-attributes {
        grid-template-columns: 1fr 1fr;
    }
}

@media (max-width: 768px) {
    .main-nav {
        display: none;
    }
    
    .page-header h1 {
        font-size: 2rem;
    }
    
    .scanner-actions {
        flex-direction: column;
    }
    
    .scanner-actions .btn {
        width: 100%;
    }
    
    .camera-controls {
        flex-wrap: wrap;
    }
    
    .scanner-card {
        padding: 25px;
    }
    
    .result-card {
        padding: 0;
    }
    
    .result-image {
        min-height: 250px;
    }
    
    .result-info {
        padding: 20px;
    }
    
    .plant-attributes,
    .care-tips {
        grid-template-columns: 1fr;
    }
    
    .similar-plants-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        grid-template-columns: 1fr 1fr;
    }
}

@media (max-width: 576px) {
    .header-content {
        padding: 10px 0;
    }
    
    .logo h1 {
        font-size: 20px;
    }
    
    .logo .material-icons {
        font-size: 24px;
    }
    
    .page-header {
        padding: 40px 0 30px;
    }
    
    .page-header h1 {
        font-size: 1.8rem;
    }
    
    .scanner-tips {
        padding: 20px;
    }
    
    .result-actions {
        flex-direction: column;
    }
    
    .result-actions .btn {
        width: 100%;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
    }
}