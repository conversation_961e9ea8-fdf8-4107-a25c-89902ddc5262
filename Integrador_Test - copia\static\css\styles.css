/* Estilos globales */
body {
    font-family: '<PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
    color: #333;
}

/* Estilos para el contenedor principal */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Estilos para encabezados */
h1, h2, h3, h4, h5, h6 {
    font-weight: 500;
    margin-top: 0;
}

/* Estilos para botones */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: background-color 0.3s, color 0.3s;
    border: none;
}

.btn-primary {
    background-color: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background-color: #388E3C;
}

.btn-outline {
    background-color: transparent;
    border: 1px solid #4CAF50;
    color: #4CAF50;
}

.btn-outline:hover {
    background-color: rgba(76, 175, 80, 0.1);
}

/* Estilos para tarjetas */
.card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 20px;
}

.card-header {
    padding: 16px;
    border-bottom: 1px solid #eee;
}

.card-body {
    padding: 16px;
}

/* Estilos para formularios */
.form-group {
    margin-bottom: 16px;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

/* Estilos para alertas */
.alert {
    padding: 12px 16px;
    border-radius: 4px;
    margin-bottom: 16px;
}

.alert-info {
    background-color: #E3F2FD;
    color: #0D47A1;
    border: 1px solid #BBDEFB;
}

.alert-success {
    background-color: #E8F5E9;
    color: #1B5E20;
    border: 1px solid #C8E6C9;
}

.alert-warning {
    background-color: #FFF8E1;
    color: #F57F17;
    border: 1px solid #FFECB3;
}

.alert-danger {
    background-color: #FFEBEE;
    color: #B71C1C;
    border: 1px solid #FFCDD2;
}

/* Estilos para la barra de progreso */
.progress {
    height: 8px;
    background-color: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 16px;
}

.progress-bar {
    height: 100%;
    background-color: #4CAF50;
    border-radius: 4px;
}

/* Estilos para imágenes */
.img-fluid {
    max-width: 100%;
    height: auto;
}

.rounded {
    border-radius: 8px;
}

/* Estilos para texto */
.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-muted {
    color: #757575;
}

/* Estilos para espaciado */
.mt-1 { margin-top: 4px; }
.mt-2 { margin-top: 8px; }
.mt-3 { margin-top: 16px; }
.mt-4 { margin-top: 24px; }
.mt-5 { margin-top: 32px; }

.mb-1 { margin-bottom: 4px; }
.mb-2 { margin-bottom: 8px; }
.mb-3 { margin-bottom: 16px; }
.mb-4 { margin-bottom: 24px; }
.mb-5 { margin-bottom: 32px; }

/* Estilos para modo oscuro */
body.dark-theme {
    background-color: #121212;
    color: #e0e0e0;
}

body.dark-theme .card {
    background-color: #1e1e1e;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

body.dark-theme .card-header {
    border-bottom-color: #333;
}

body.dark-theme .form-control {
    background-color: #2d2d2d;
    border-color: #444;
    color: #e0e0e0;
}

body.dark-theme .btn-outline {
    border-color: #4CAF50;
    color: #4CAF50;
}

body.dark-theme .btn-outline:hover {
    background-color: rgba(76, 175, 80, 0.2);
}

/* Estilos para dispositivos móviles */
@media (max-width: 768px) {
    .container {
        padding: 0 10px;
    }
    
    .card {
        margin-bottom: 15px;
    }
    
    .btn {
        padding: 6px 12px;
        font-size: 14px;
    }
}
