/* Base Styles */
:root {
    --primary-color: #4caf50;
    --primary-light: #80e27e;
    --primary-dark: #087f23;
    --secondary-color: #2196f3;
    --text-color: #333333;
    --text-light: #757575;
    --background-color: #ffffff;
    --background-light: #f5f5f5;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --border-radius: 8px;
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Dark Theme */
.dark-theme {
    --primary-color: #81c784;
    --primary-light: #b2fab4;
    --primary-dark: #519657;
    --secondary-color: #64b5f6;
    --text-color: #f5f5f5;
    --text-light: #b0b0b0;
    --background-color: #121212;
    --background-light: #1e1e1e;
    --border-color: #333333;
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--background-light);
    transition: var(--transition);
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    border: none;
    outline: none;
    text-decoration: none;
    gap: 8px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn-text {
    background-color: transparent;
    color: var(--primary-color);
    padding: 8px;
}

.btn-text:hover {
    background-color: rgba(76, 175, 80, 0.1);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.icon-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: transparent;
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.icon-button:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.dark-theme .icon-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Header */
.app-header {
    background-color: var(--background-color);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
    padding: 12px 0;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--primary-color);
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 500;
}

.main-nav ul {
    display: flex;
    list-style: none;
    gap: 24px;
}

.main-nav a {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
    transition: var(--transition);
    padding: 8px 0;
    position: relative;
}

.main-nav a:hover,
.main-nav a.active {
    color: var(--primary-color);
}

.main-nav a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: var(--transition);
}

.main-nav a:hover::after,
.main-nav a.active::after {
    width: 100%;
}

.user-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
    border: none;
    background: none;
    padding: 0;
}

.avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--background-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    min-width: 200px;
    padding: 8px 0;
    z-index: 1000;
    display: none;
}

.dropdown-menu.active {
    display: block;
}

.dropdown-menu a {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
}

.dropdown-menu a:hover {
    background-color: var(--background-light);
    color: var(--primary-color);
}

.user-menu {
    position: relative;
}

/* Footer */
.app-footer {
    background-color: var(--background-color);
    padding: 48px 0 24px;
    margin-top: 64px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 32px;
    margin-bottom: 32px;
}

.footer-section h3 {
    color: var(--primary-color);
    margin-bottom: 16px;
    font-weight: 500;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-section a {
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 24px;
    border-top: 1px solid var(--border-color);
    color: var(--text-light);
}

/* Breadcrumbs */
.breadcrumbs {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 24px 0;
    color: var(--text-light);
    font-size: 0.875rem;
}

.breadcrumbs a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumbs a:hover {
    text-decoration: underline;
}

.breadcrumbs .material-icons {
    font-size: 16px;
}

/* Topic Detail */
.topic-detail {
    background-color: var(--background-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 32px;
    overflow: hidden;
}

.topic-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 24px;
    border-bottom: 1px solid var(--border-color);
}

.topic-title-section h1 {
    font-size: 1.75rem;
    margin-bottom: 12px;
    color: var(--text-color);
}

.topic-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.topic-tags .tag {
    display: inline-block;
    padding: 4px 12px;
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--primary-color);
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 500;
}

.topic-actions {
    display: flex;
    gap: 12px;
}

.topic-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background-color: var(--background-light);
    border-bottom: 1px solid var(--border-color);
}

.topic-author-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.author-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
}

.author-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.author-details {
    display: flex;
    flex-direction: column;
}

.author-name {
    font-weight: 500;
    color: var(--text-color);
}

.post-date {
    font-size: 0.875rem;
    color: var(--text-light);
}

.topic-stats {
    display: flex;
    gap: 16px;
}

.stat {
    display: flex;
    align-items: center;
    gap: 4px;
    color: var(--text-light);
    font-size: 0.875rem;
}

.stat .material-icons {
    font-size: 16px;
}

.topic-content {
    padding: 24px;
    line-height: 1.8;
}

.topic-content p {
    margin-bottom: 16px;
}

.topic-content img {
    max-width: 100%;
    border-radius: var(--border-radius);
    margin: 16px 0;
}

.topic-reactions {
    padding: 16px 24px;
    border-top: 1px solid var(--border-color);
}

.reaction-buttons {
    display: flex;
    gap: 16px;
}

.reaction-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 8px 12px;
    border-radius: 20px;
    background-color: var(--background-light);
    border: none;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-color);
}

.reaction-btn:hover {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--primary-color);
}

.reaction-btn.active {
    background-color: rgba(76, 175, 80, 0.2);
    color: var(--primary-color);
}

/* Replies Section */
.replies-section {
    margin-bottom: 48px;
}

.replies-section h2 {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.sort-options {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 24px;
    color: var(--text-light);
    font-size: 0.875rem;
}

.sort-options select {
    padding: 4px 8px;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    background-color: var(--background-color);
    color: var(--text-color);
    font-size: 0.875rem;
    cursor: pointer;
}

.replies-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 32px;
}

.reply-item {
    background-color: var(--background-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.reply-author {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background-color: var(--background-light);
    border-bottom: 1px solid var(--border-color);
}

.reply-content {
    padding: 16px;
    line-height: 1.8;
}

.reply-actions {
    display: flex;
    justify-content: space-between;
    padding: 8px 16px;
    border-top: 1px solid var(--border-color);
}

.reply-form {
    background-color: var(--background-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 24px;
}

.reply-form h3 {
    margin-bottom: 16px;
    font-weight: 500;
}

.form-group {
    margin-bottom: 16px;
}

.form-group textarea {
    width: 100%;
    padding: 12px;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    background-color: var(--background-color);
    color: var(--text-color);
    resize: vertical;
    font-family: 'Roboto', sans-serif;
    transition: var(--transition);
}

.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
}

/* Related Topics */
.related-topics {
    margin-bottom: 48px;
}

.related-topics h2 {
    margin-bottom: 16px;
}

.related-topics-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
}

.related-topic-item {
    background-color: var(--background-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 16px;
    transition: var(--transition);
}

.related-topic-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.related-topic-item h3 {
    margin-bottom: 8px;
    font-size: 1rem;
}

.related-topic-item h3 a {
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
}

.related-topic-item h3 a:hover {
    color: var(--primary-color);
}

.related-topic-item .topic-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
    background-color: transparent;
    border: none;
    font-size: 0.75rem;
    color: var(--text-light);
}

.related-topic-item .topic-stats {
    display: flex;
    align-items: center;
    gap: 4px;
}

.related-topic-item .material-icons {
    font-size: 14px;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .related-topics-list {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 16px;
    }
    
    .main-nav ul {
        gap: 16px;
    }
    
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .topic-header {
        flex-direction: column;
        gap: 16px;
    }
    
    .topic-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
    
    .topic-stats {
        width: 100%;
        justify-content: flex-start;
    }
    
    .related-topics-list {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    .main-nav ul {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .reaction-buttons {
        flex-wrap: wrap;
    }
}
