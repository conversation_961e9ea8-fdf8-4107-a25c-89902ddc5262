/* ===== ESTILOS PARA EL MENÚ DE USUARIO ===== */

/* Contenedor del menú de usuario */
.user-menu {
    position: relative;
}

/* Botón de avatar */
.avatar {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    border: 2px solid var(--primary-light);
    overflow: hidden;
    padding: 0;
    cursor: pointer;
    background: none;
    transition: var(--transition);
    position: relative;
}

.avatar:hover {
    border-color: var(--primary);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2);
}

.avatar:active, .avatar.clicked {
    transform: scale(0.95);
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
}

.avatar.clicked {
    animation: avatarPulse 0.3s ease-out;
}

@keyframes avatarPulse {
    0% { transform: scale(0.95); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.avatar::after {
    content: '';
    position: absolute;
    top: -4px;
    right: -4px;
    bottom: -4px;
    left: -4px;
    border-radius: 50%;
    background-color: rgba(76, 175, 80, 0.1);
    opacity: 0;
    transform: scale(0.8);
    transition: var(--transition);
}

.avatar:hover::after {
    opacity: 1;
    transform: scale(1);
}

.avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Menú desplegable */
.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--background);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    width: 220px;
    padding: 8px;
    margin-top: 12px;
    display: none;
    z-index: 1000;
    transform: translateY(-10px);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.dropdown-menu::before {
    content: '';
    position: absolute;
    top: -8px;
    right: 16px;
    width: 16px;
    height: 16px;
    background-color: var(--background);
    transform: rotate(45deg);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    border-left: 1px solid rgba(0, 0, 0, 0.05);
}

.dropdown-menu.active {
    display: block;
    transform: translateY(0);
    opacity: 1;
}

/* Elementos del menú */
.dropdown-menu a {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    text-decoration: none;
    color: var(--text-dark);
    transition: var(--transition);
    border-radius: 8px;
    margin-bottom: 4px;
}

.dropdown-menu a:hover {
    background-color: rgba(76, 175, 80, 0.08);
    color: var(--primary);
    transform: translateX(4px);
}

.dropdown-menu a .material-icons {
    color: var(--primary);
    font-size: 20px;
    transition: var(--transition);
}

.dropdown-menu a:hover .material-icons {
    transform: scale(1.1);
    animation: iconPulse 0.3s ease-out;
}

@keyframes iconPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1.1); }
}

/* Estilo especial para el botón de cerrar sesión */
.dropdown-menu a#logout-button {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    margin-top: 4px;
    padding-top: 12px;
    color: #f44336;
}

.dropdown-menu a#logout-button .material-icons {
    color: #f44336;
}

.dropdown-menu a#logout-button:hover {
    background-color: rgba(244, 67, 54, 0.1);
    color: #d32f2f;
}

/* Tema oscuro */
.dark-theme .dropdown-menu {
    background-color: #1e1e1e;
    border-color: rgba(255, 255, 255, 0.05);
}

.dark-theme .dropdown-menu::before {
    background-color: #1e1e1e;
    border-top-color: rgba(255, 255, 255, 0.05);
    border-left-color: rgba(255, 255, 255, 0.05);
}

.dark-theme .dropdown-menu a {
    color: #f5f5f5;
}

.dark-theme .dropdown-menu a:hover {
    background-color: rgba(129, 199, 132, 0.15);
}

.dark-theme .dropdown-menu a#logout-button {
    border-top-color: rgba(255, 255, 255, 0.05);
}
