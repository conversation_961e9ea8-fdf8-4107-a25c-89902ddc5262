document.addEventListener('DOMContentLoaded', function() {

    // User Menu Dropdown
    const userMenuButton = document.getElementById('user-menu-button');
    const userDropdown = document.getElementById('user-dropdown');

    if (userMenuButton && userDropdown) {
        userMenuButton.addEventListener('click', function() {
            userDropdown.classList.toggle('active');
        });

        document.addEventListener('click', function(event) {
            if (!userMenuButton.contains(event.target) && !userDropdown.contains(event.target)) {
                userDropdown.classList.remove('active');
            }
        });
    }

    // Settings Navigation
    const settingsLinks = document.querySelectorAll('.settings-nav a');
    const settingsSections = document.querySelectorAll('.settings-section');

    if (settingsLinks.length > 0 && settingsSections.length > 0) {
        settingsLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                const targetId = this.getAttribute('href').substring(1);

                // Remove active class from all links and sections
                settingsLinks.forEach(l => l.classList.remove('active'));
                settingsSections.forEach(s => s.classList.remove('active'));

                // Add active class to clicked link and corresponding section
                this.classList.add('active');
                document.getElementById(targetId).classList.add('active');
            });
        });
    }

    // Form Submissions
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Determine which API endpoint to use based on form ID
            let endpoint = '';
            switch (this.id) {
                case 'account-form':
                    endpoint = '/settings/account';
                    break;
                case 'notifications-form':
                    endpoint = '/settings/notifications';
                    break;
                case 'privacy-form':
                    endpoint = '/settings/privacy';
                    break;
                case 'language-form':
                    endpoint = '/settings/language';
                    break;
                default:
                    return;
            }
            
            // Collect form data
            const formData = new FormData(this);
            
            // Send AJAX request to backend
            fetch(endpoint, {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                // Create or update message box
                let messageBox = form.querySelector('.message-box');
                if (!messageBox) {
                    messageBox = document.createElement('div');
                    messageBox.className = 'message-box';
                    form.appendChild(messageBox);
                }
                
                // Style based on success or failure
                if (data.success) {
                    messageBox.className = 'message-box success-message';
                    messageBox.textContent = data.message;
                } else {
                    messageBox.className = 'message-box error-message';
                    messageBox.textContent = data.message || 'Ocurrió un error al guardar los cambios.';
                }
                
                // Hide message after 3 seconds
                setTimeout(() => {
                    messageBox.textContent = '';
                    messageBox.className = 'message-box';
                }, 3000);
                
                // If redirect is provided, redirect after showing message
                if (data.redirect) {
                    setTimeout(() => {
                        window.location.href = data.redirect;
                    }, 1500);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                
                // Show error message
                let messageBox = form.querySelector('.message-box');
                if (!messageBox) {
                    messageBox = document.createElement('div');
                    messageBox.className = 'message-box';
                    form.appendChild(messageBox);
                }
                
                messageBox.className = 'message-box error-message';
                messageBox.textContent = 'Error de conexión. Por favor, intenta nuevamente.';
                
                // Hide message after 3 seconds
                setTimeout(() => {
                    messageBox.textContent = '';
                    messageBox.className = 'message-box';
                }, 3000);
            });
        });
    });    

    // Logout Button
    const logoutButton = document.getElementById('logout-button');

    if (logoutButton) {
        logoutButton.addEventListener('click', function(e) {
            e.preventDefault();
    
            // Create custom modal
            const modal = document.createElement('div');
            modal.innerHTML = `
                <div class="modal-overlay">
                    <div class="modal-content">
                        <p>¿Estás seguro de que deseas cerrar sesión?</p>
                        <button id="confirm-logout" class="btn">Sí, cerrar sesión</button>
                        <button id="cancel-logout" class="btn">Cancelar</button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
    
            // Handle confirmation
            document.getElementById('confirm-logout').addEventListener('click', function() {
                // Send logout request to backend
                fetch('/settings/logout', {
                    method: 'POST',
                    credentials: 'same-origin'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.redirect) {
                        window.location.href = data.redirect;
                    }
                })
                .catch(error => {
                    console.error('Error logging out:', error);
                    alert('Error al cerrar sesión. Por favor, intenta nuevamente.');
                });
            });
    
            // Close modal if canceled
            document.getElementById('cancel-logout').addEventListener('click', function() {
                document.body.removeChild(modal);
            });
        });
    }

    // Handle URL hash for direct navigation
    function handleHash() {
        const hash = window.location.hash;
        if (hash) {
            const targetLink = document.querySelector(`.settings-nav a[href="${hash}"]`);
            if (targetLink) {
                targetLink.click();
            }
        }
    }

    // Check for hash on page load
    handleHash();

    // Listen for hash changes
    window.addEventListener('hashchange', handleHash);

});