/**
 * PlantCare - Biblioteca de Plantas
 * JavaScript moderno con animaciones y efectos mejorados
 */

document.addEventListener('DOMContentLoaded', function() {
    // Inicializar tema (oscuro/claro)
    initializeTheme();

    // Configuración del menú de usuario
    initializeUserMenu();

    // Configurar animaciones en scroll
    initializeScrollAnimations();

    // Inicializar biblioteca de plantas
    initializePlantLibrary();

    // Configurar filtros interactivos
    setupFilters();

    // Configurar paginación
    setupPagination();

    // Añadir efectos a las tarjetas
    setupCardInteractions();
});

/**
 * Inicializa el tema de la aplicación
 */
function initializeTheme() {
    // Verificar si existe un tema guardado en localStorage
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
        document.body.classList.add('dark-theme');
    }

    // Añadir botón de cambio de tema si no existe
    if (!document.getElementById('theme-toggle')) {
        const userActions = document.querySelector('.user-actions');
        if (userActions) {
            const themeToggle = document.createElement('button');
            themeToggle.id = 'theme-toggle';
            themeToggle.className = 'icon-button';
            themeToggle.innerHTML = '<span class="material-icons">' +
                (savedTheme === 'dark' ? 'light_mode' : 'dark_mode') + '</span>';

            userActions.prepend(themeToggle);

            themeToggle.addEventListener('click', function() {
                document.body.classList.toggle('dark-theme');

                if (document.body.classList.contains('dark-theme')) {
                    localStorage.setItem('theme', 'dark');
                    this.innerHTML = '<span class="material-icons">light_mode</span>';
                } else {
                    localStorage.setItem('theme', 'light');
                    this.innerHTML = '<span class="material-icons">dark_mode</span>';
                }
            });
        }
    }
}

/**
 * Inicializa el menú de usuario
 */
function initializeUserMenu() {
    const userMenuButton = document.getElementById('user-menu-button');
    const userDropdown = document.getElementById('user-dropdown');

    if (userMenuButton && userDropdown) {
        userMenuButton.addEventListener('click', function() {
            userDropdown.classList.toggle('active');
        });

        // Cerrar menú al hacer clic fuera
        document.addEventListener('click', function(event) {
            if (!userMenuButton.contains(event.target) && !userDropdown.contains(event.target)) {
                userDropdown.classList.remove('active');
            }
        });
    }

    // Confirmar cierre de sesión
    const logoutButton = document.getElementById('logout-button');
    if (logoutButton) {
        logoutButton.addEventListener('click', function(e) {
            if (!confirm('¿Estás seguro de que deseas cerrar sesión?')) {
                e.preventDefault();
            }
        });
    }
}

/**
 * Inicializa animaciones para elementos al hacer scroll
 */
function initializeScrollAnimations() {
    // Elementos a animar durante el scroll
    const elementsToAnimate = document.querySelectorAll('.plant-card');

    // Configurar IntersectionObserver para elementos que entran en vista
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
            if (entry.isIntersecting) {
                // Aplicar animación con retraso proporcional a su índice
                const element = entry.target;
                const delay = Array.from(elementsToAnimate).indexOf(element) % 6 * 0.1;
                element.style.animationDelay = `${delay}s`;
                element.style.opacity = 1;

                // Dejar de observar una vez animado
                observer.unobserve(element);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -100px 0px'
    });

    // Comenzar a observar elementos
    elementsToAnimate.forEach(element => {
        element.style.opacity = 0;
        observer.observe(element);
    });
}

// Datos de ejemplo para la biblioteca de plantas
const plantasData = [
    {
        id: 1,
        nombre: "Biznaga",
        nombreCientifico: "Echinocactus platyacanthus",
        imagen: "/static/assets/biznaga.jpg",
        categoria: "cactus",
        riego: "bajo",
        sol: "pleno",
        dificultad: "facil",
        descripcion: "Cactus nativo de Chihuahua que puede crecer hasta 2 metros de altura."
    },
    {
        id: 2,
        nombre: "Gobernadora",
        nombreCientifico: "Larrea tridentata",
        imagen: "/static/assets/gobernadora.jpg",
        categoria: "arbustos",
        riego: "bajo",
        sol: "pleno",
        dificultad: "facil",
        descripcion: "Arbusto perenne resistente a la sequía, muy común en el desierto chihuahuense."
    },
    {
        id: 3,
        nombre: "Yuca",
        nombreCientifico: "Yucca elata",
        imagen: "/static/assets/yuca.jpg",
        categoria: "suculentas",
        riego: "bajo",
        sol: "pleno",
        dificultad: "moderada",
        descripcion: "Planta icónica del desierto chihuahuense con hojas puntiagudas y flores blancas."
    },
    {
        id: 4,
        nombre: "Mezquite",
        nombreCientifico: "Prosopis glandulosa",
        imagen: "/static/assets/Mezquite.jpg",
        categoria: "arboles",
        riego: "moderado",
        sol: "pleno",
        dificultad: "moderada",
        descripcion: "Árbol resistente que proporciona sombra y es fundamental en el ecosistema del desierto."
    },
    {
        id: 5,
        nombre: "Lechuguilla",
        nombreCientifico: "Agave lechuguilla",
        imagen: "/static/assets/lechuguilla.jpg",
        categoria: "suculentas",
        riego: "bajo",
        sol: "pleno",
        dificultad: "facil",
        descripcion: "Agave pequeño con hojas en roseta y bordes espinosos."
    },
    {
        id: 6,
        nombre: "Ocotillo",
        nombreCientifico: "Fouquieria splendens",
        imagen: "/static/assets/ocotillo.jpg",
        categoria: "arbustos",
        riego: "bajo",
        sol: "pleno",
        dificultad: "moderada",
        descripcion: "Arbusto con tallos largos y espinosos que florece con llamativas flores rojas."
    },
    {
        id: 7,
        nombre: "Nopal",
        nombreCientifico: "Opuntia engelmannii",
        imagen: "/static/assets/nopal.jpg",
        categoria: "cactus",
        riego: "bajo",
        sol: "pleno",
        dificultad: "facil",
        descripcion: "Cactus con pencas aplanadas y espinas, produce tunas comestibles."
    },
    {
        id: 8,
        nombre: "Palo Verde",
        nombreCientifico: "Parkinsonia aculeata",
        imagen: "/static/assets/palo_verde.jpg",
        categoria: "arboles",
        riego: "moderado",
        sol: "pleno",
        dificultad: "moderada",
        descripcion: "Árbol con corteza verde y pequeñas hojas, adaptado a condiciones áridas."
    },
    {
        id: 9,
        nombre: "Naranjo",
        nombreCientifico: "Citrus sinensis",
        imagen: "/static/assets/naranjo.jpg",
        categoria: "arboles",
        riego: "moderado",
        sol: "pleno",
        dificultad: "moderada",
        descripcion: "Árbol frutal de hoja perenne que produce las famosas naranjas. Adaptado para cultivo en Chihuahua con cuidados especiales."
    },
    {
        id: 10,
        nombre: "Rosal",
        nombreCientifico: "Rosa sp.",
        imagen: "/static/assets/rosal.jpg",
        categoria: "flores",
        riego: "moderado",
        sol: "pleno",
        dificultad: "moderada",
        descripcion: "Arbusto ornamental con flores fragantes que pueden cultivarse exitosamente en Chihuahua eligiendo variedades resistentes al clima."
    },
    {
        id: 11,
        nombre: "Pata de Elefante",
        nombreCientifico: "Beaucarnea recurvata",
        imagen: "/static/assets/pata_elefante.jpg",
        categoria: "suculentas",
        riego: "bajo",
        sol: "parcial",
        dificultad: "facil",
        descripcion: "Planta con un tronco ensanchado en la base que almacena agua. Excelente para interiores y exteriores protegidos en Chihuahua."
    },
    {
        id: 12,
        nombre: "Ojo de Poeta",
        nombreCientifico: "Thunbergia alata",
        imagen: "/static/assets/ojo_de_poeta.jpg",
        categoria: "flores",
        riego: "moderado",
        sol: "parcial",
        dificultad: "facil",
        descripcion: "Enredadera de rápido crecimiento con flores características de color naranja con centro negro. Adecuada para pérgolas y cercas."
    },
    {
        id: 13,
        nombre: "Lavanda",
        nombreCientifico: "Lavandula angustifolia",
        imagen: "/static/assets/lavanda.jpg",
        categoria: "arbustos",
        riego: "bajo",
        sol: "pleno",
        dificultad: "facil",
        descripcion: "Arbusto aromático con flores púrpuras, ideal para zonas secas de Chihuahua. Atrae polinizadores y repele plagas."
    },
    {
        id: 14,
        nombre: "Bugambilia",
        nombreCientifico: "Bougainvillea spectabilis",
        imagen: "/static/assets/bugambilia.jpg",
        categoria: "arbustos",
        riego: "bajo",
        sol: "pleno",
        dificultad: "moderada",
        descripcion: "Arbusto trepador con vistosas brácteas de colores. Resistente a la sequía y adaptable al clima de Chihuahua."
    }
];

// Variables para la paginación
let currentPage = 1;
const itemsPerPage = 6;
let filteredPlants = [];

/**
 * Inicializa la biblioteca de plantas
 */
function initializePlantLibrary() {
    // Inicializar con todas las plantas
    filteredPlants = [...plantasData];

    // Renderizar plantas con animación
    setTimeout(() => {
        renderPlants();
        updatePaginationInfo();
    }, 300);
}

/**
 * Configurar filtros interactivos
 */
function setupFilters() {
    const searchInput = document.getElementById('search-input');
    const categoryFilter = document.getElementById('category-filter');
    const waterFilter = document.getElementById('water-filter');
    const sunFilter = document.getElementById('sun-filter');
    const difficultyFilter = document.getElementById('difficulty-filter');
    const resetButton = document.getElementById('reset-filters');

    // Agregar efectos a los inputs de filtro
    const filterInputs = [searchInput, categoryFilter, waterFilter, sunFilter, difficultyFilter];

    filterInputs.forEach(input => {
        if (!input) return;

        // Agregar clase activa al enfocar
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('active');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('active');
        });
    });

    // Evento de búsqueda con retraso para evitar búsquedas excesivas
    let searchTimeout;
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(applyFilters, 300);
        });
    }

    // Eventos de cambio en selectores con efecto visual
    [categoryFilter, waterFilter, sunFilter, difficultyFilter].forEach(select => {
        if (select) {
            select.addEventListener('change', function() {
                // Efecto visual de selección
                this.classList.add('changed');
                setTimeout(() => {
                    this.classList.remove('changed');
                }, 500);

                applyFilters();
            });
        }
    });

    // Botón de reinicio con animación
    if (resetButton) {
        resetButton.addEventListener('click', function() {
            // Efecto visual de reinicio
            this.classList.add('spinning');
            setTimeout(() => {
                this.classList.remove('spinning');
            }, 500);

            // Reiniciar valores
            if (searchInput) searchInput.value = '';
            if (categoryFilter) categoryFilter.selectedIndex = 0;
            if (waterFilter) waterFilter.selectedIndex = 0;
            if (sunFilter) sunFilter.selectedIndex = 0;
            if (difficultyFilter) difficultyFilter.selectedIndex = 0;

            applyFilters();
        });
    }
}

/**
 * Aplicar filtros a las plantas
 */
function applyFilters() {
    const searchInput = document.getElementById('search-input');
    const categoryFilter = document.getElementById('category-filter');
    const waterFilter = document.getElementById('water-filter');
    const sunFilter = document.getElementById('sun-filter');
    const difficultyFilter = document.getElementById('difficulty-filter');

    const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
    const category = categoryFilter ? categoryFilter.value : '';
    const water = waterFilter ? waterFilter.value : '';
    const sun = sunFilter ? sunFilter.value : '';
    const difficulty = difficultyFilter ? difficultyFilter.value : '';

    // Mostrar indicador de carga
    const plantsGrid = document.getElementById('plants-grid');
    if (plantsGrid) {
        plantsGrid.classList.add('loading');
    }

    // Aplicar filtros con pequeño retraso para mostrar animación
    setTimeout(() => {
        filteredPlants = plantasData.filter(plant => {
            // Filtro de búsqueda por texto
            const matchesSearch = !searchTerm ||
                plant.nombre.toLowerCase().includes(searchTerm) ||
                plant.nombreCientifico.toLowerCase().includes(searchTerm) ||
                plant.descripcion.toLowerCase().includes(searchTerm);

            // Filtros de categoría
            const matchesCategory = !category || plant.categoria === category;
            const matchesWater = !water || plant.riego === water;
            const matchesSun = !sun || plant.sol === sun;
            const matchesDifficulty = !difficulty || plant.dificultad === difficulty;

            return matchesSearch && matchesCategory && matchesWater && matchesSun && matchesDifficulty;
        });

        // Resetear a la primera página cuando se aplican filtros
        currentPage = 1;

        // Quitar indicador de carga
        if (plantsGrid) {
            plantsGrid.classList.remove('loading');
        }

        renderPlants();
        updatePaginationInfo();
    }, 300);
}

/**
 * Configurar paginación
 */
function setupPagination() {
    const prevButton = document.getElementById('prev-page');
    const nextButton = document.getElementById('next-page');

    if (prevButton) {
        prevButton.addEventListener('click', function() {
            if (currentPage > 1) {
                currentPage--;
                renderPlants();
                updatePaginationInfo();

                // Desplazar suavemente hacia arriba
                window.scrollTo({
                    top: document.querySelector('.plants-container').offsetTop - 100,
                    behavior: 'smooth'
                });
            }
        });
    }

    if (nextButton) {
        nextButton.addEventListener('click', function() {
            const totalPages = Math.ceil(filteredPlants.length / itemsPerPage);
            if (currentPage < totalPages) {
                currentPage++;
                renderPlants();
                updatePaginationInfo();

                // Desplazar suavemente hacia arriba
                window.scrollTo({
                    top: document.querySelector('.plants-container').offsetTop - 100,
                    behavior: 'smooth'
                });
            }
        });
    }
}

/**
 * Actualizar información de paginación
 */
function updatePaginationInfo() {
    const totalPages = Math.max(1, Math.ceil(filteredPlants.length / itemsPerPage));
    const currentPageEl = document.getElementById('current-page');
    const totalPagesEl = document.getElementById('total-pages');
    const prevButton = document.getElementById('prev-page');
    const nextButton = document.getElementById('next-page');

    if (currentPageEl) {
        currentPageEl.textContent = currentPage;
    }

    if (totalPagesEl) {
        totalPagesEl.textContent = totalPages;
    }

    // Habilitar/deshabilitar botones de paginación
    if (prevButton) {
        prevButton.disabled = currentPage === 1;
    }

    if (nextButton) {
        nextButton.disabled = currentPage === totalPages;
    }
}

/**
 * Renderizar plantas en la página
 */
function renderPlants() {
    const plantsGrid = document.getElementById('plants-grid');
    const template = document.getElementById('plant-card-template');

    if (!plantsGrid || !template) return;

    // Limpiar contenedor con animación de desvanecimiento
    plantsGrid.style.opacity = '0';

    setTimeout(() => {
        plantsGrid.innerHTML = '';

        // Calcular rango de plantas a mostrar
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = Math.min(startIndex + itemsPerPage, filteredPlants.length);

        // Mostrar mensaje si no hay plantas
        if (filteredPlants.length === 0) {
            const noResults = document.createElement('div');
            noResults.className = 'no-results';
            noResults.innerHTML = `
                <span class="material-icons">search_off</span>
                <p>No se encontraron plantas con estos criterios.</p>
                <button id="clear-filters" class="btn btn-primary">Limpiar filtros</button>
            `;
            plantsGrid.appendChild(noResults);

            // Agregar funcionalidad al botón de limpiar filtros
            const clearFiltersBtn = noResults.querySelector('#clear-filters');
            if (clearFiltersBtn) {
                clearFiltersBtn.addEventListener('click', function() {
                    const resetButton = document.getElementById('reset-filters');
                    if (resetButton) {
                        resetButton.click();
                    }
                });
            }

            plantsGrid.style.opacity = '1';
            return;
        }

        // Renderizar plantas para la página actual
        for (let i = startIndex; i < endIndex; i++) {
            const plant = filteredPlants[i];
            const card = template.content.cloneNode(true);

            // Llenar los datos de la planta en la tarjeta
            const img = card.querySelector('.plant-image img');
            img.src = plant.imagen;
            img.alt = plant.nombre;

            card.querySelector('.plant-name').textContent = plant.nombre;
            card.querySelector('.plant-scientific-name').textContent = plant.nombreCientifico;
            card.querySelector('.water-needs').textContent = getWaterText(plant.riego);
            card.querySelector('.sun-needs').textContent = getSunText(plant.sol);

            // Encontrar el enlace y actualizar el href para que incluya el ID de la planta correcta
            const viewDetailsBtn = card.querySelector('.view-details');
            if (viewDetailsBtn) {
                // Mapear IDs a nombres de plantas para la URL
                const plantUrlMap = {
                    1: 'biznaga',        // Biznaga
                    2: 'gobernadora',    // Gobernadora
                    3: 'yucca',          // Yuca
                    4: 'mezquite',       // Mezquite
                    5: 'lechuguilla',    // Lechuguilla
                    6: 'ocotillo',       // Ocotillo
                    7: 'nopal',          // Nopal
                    8: 'palo-verde',     // Palo Verde
                    9: 'naranjo',        // Naranjo
                    10: 'rosal',         // Rosal
                    11: 'pata-elefante', // Pata de Elefante
                    12: 'ojo-poeta',     // Ojo de Poeta
                    13: 'lavanda',       // Lavanda
                    14: 'bugambilia'     // Bugambilia
                };

                const plantKey = plantUrlMap[plant.id] || 'agave';
                viewDetailsBtn.href = `/plant-details?plant=${plantKey}`;
            }

            plantsGrid.appendChild(card);
        }

        // Mostrar plantas con animación
        plantsGrid.style.opacity = '1';

        // Reiniciar animaciones
        initializeScrollAnimations();
    }, 200);
}

/**
 * Configurar interacciones para tarjetas
 */
function setupCardInteractions() {
    // Efecto 3D en tarjetas con movimiento del mouse
    document.addEventListener('mousemove', function(e) {
        const cards = document.querySelectorAll('.plant-card');

        cards.forEach(card => {
            // Solo aplicar efecto si el mouse está sobre la tarjeta
            const rect = card.getBoundingClientRect();

            if (
                e.clientX >= rect.left &&
                e.clientX <= rect.right &&
                e.clientY >= rect.top &&
                e.clientY <= rect.bottom
            ) {
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;
                const percentX = (e.clientX - centerX) / (rect.width / 2);
                const percentY = (e.clientY - centerY) / (rect.height / 2);

                // Limitar rotación a 5 grados
                const maxRotation = 5;
                card.style.transform = `
                    perspective(1000px)
                    rotateY(${percentX * maxRotation}deg)
                    rotateX(${-percentY * maxRotation}deg)
                    translateZ(10px)
                `;
            }
        });
    });

    // Restaurar estado al salir del área
    document.addEventListener('mouseleave', function() {
        const cards = document.querySelectorAll('.plant-card');
        cards.forEach(card => {
            card.style.transform = '';
        });
    });
}

/**
 * Obtener texto descriptivo para necesidades de agua
 * @param {string} water - Nivel de agua requerido
 * @return {string} Texto descriptivo
 */
function getWaterText(water) {
    switch(water) {
        case 'bajo': return 'Riego bajo';
        case 'moderado': return 'Riego moderado';
        case 'alto': return 'Riego frecuente';
        default: return 'Desconocido';
    }
}

/**
 * Obtener texto descriptivo para necesidades de sol
 * @param {string} sun - Nivel de sol requerido
 * @return {string} Texto descriptivo
 */
function getSunText(sun) {
    switch(sun) {
        case 'sombra': return 'Sombra';
        case 'parcial': return 'Sol parcial';
        case 'pleno': return 'Pleno sol';
        default: return 'Desconocido';
    }
}

// Añadir estilos necesarios para efectos
document.head.insertAdjacentHTML('beforeend', `
<style>
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .spinning .material-icons {
        animation: spin 0.5s linear;
    }

    .changed {
        background-color: rgba(76, 175, 80, 0.1) !important;
    }

    .plants-grid.loading {
        opacity: 0.6;
        transition: opacity 0.3s;
    }

    .filter-group.active label {
        color: var(--primary);
    }
</style>
`);