document.addEventListener('DOMContentLoaded', function() {
    // Inicializar todos los componentes
    initThemeToggle();
    initUserMenu();
    initScrollNavigation();
    initScrollReveal();
    initCalendar();
    initModal();
    initMobileMenu();
    initCardHoverEffect();
});

// ===== FUNCIONES DE INTERFAZ DE USUARIO =====

// Cambio de tema claro/oscuro
function initThemeToggle() {
    const themeToggle = document.getElementById('theme-toggle');

    if (themeToggle) {
        const themeIcon = themeToggle.querySelector('.material-icons');

        // Verificar si hay preferencia de tema guardada
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            document.body.classList.add('dark-theme');
            themeIcon.textContent = 'light_mode';
        }

        themeToggle.addEventListener('click', function() {
            document.body.classList.toggle('dark-theme');

            if (document.body.classList.contains('dark-theme')) {
                localStorage.setItem('theme', 'dark');
                themeIcon.textContent = 'light_mode';
                showNotification('Modo oscuro activado', 'success', 'dark_mode');
            } else {
                localStorage.setItem('theme', 'light');
                themeIcon.textContent = 'dark_mode';
                showNotification('Modo claro activado', 'success', 'light_mode');
            }
        });
    }
}

// Menú de usuario
function initUserMenu() {
    const userMenuButton = document.getElementById('user-menu-button');
    const userDropdown = document.getElementById('user-dropdown');

    if (userMenuButton && userDropdown) {
        // Agregar efecto de ripple al botón de usuario
        userMenuButton.addEventListener('click', function(e) {
            // Alternar clase active en el dropdown
            userDropdown.classList.toggle('active');

            // Efecto visual en el avatar
            this.classList.add('clicked');
            setTimeout(() => {
                this.classList.remove('clicked');
            }, 300);

            e.stopPropagation(); // Evitar que el clic se propague al documento
        });

        // Cerrar menú al hacer clic fuera
        document.addEventListener('click', function(event) {
            if (!userMenuButton.contains(event.target) && !userDropdown.contains(event.target)) {
                userDropdown.classList.remove('active');
            }
        });

        // Agregar efectos a los elementos del menú
        const menuItems = userDropdown.querySelectorAll('a');
        menuItems.forEach(item => {
            item.addEventListener('mouseenter', function() {
                // Agregar efecto de hover
                this.querySelector('.material-icons')?.classList.add('icon-hover');
            });

            item.addEventListener('mouseleave', function() {
                // Quitar efecto de hover
                this.querySelector('.material-icons')?.classList.remove('icon-hover');
            });
        });
    }

    // Botón de cerrar sesión
    const logoutButton = document.getElementById('logout-button');

    if (logoutButton) {
        logoutButton.addEventListener('click', function(e) {
            e.preventDefault();

            if (confirm('¿Estás seguro de que deseas cerrar sesión?')) {
                window.location.href = logoutButton.getAttribute('href');
            }
        });
    }
}

// Navegación fija al hacer scroll
function initScrollNavigation() {
    const nav = document.querySelector('.main-nav');
    if (!nav) return;

    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            nav.classList.add('scrolled');
        } else {
            nav.classList.remove('scrolled');
        }
    });
}

// Animaciones al hacer scroll
function initScrollReveal() {
    const elementsToReveal = document.querySelectorAll('.scroll-reveal');

    const revealOnScroll = function() {
        elementsToReveal.forEach((element, index) => {
            const elementTop = element.getBoundingClientRect().top;
            const windowHeight = window.innerHeight;

            if (elementTop < windowHeight - 100) {
                setTimeout(() => {
                    element.classList.add('revealed');
                }, index * 100); // Efecto escalonado
            }
        });
    };

    // Ejecutar al cargar y al hacer scroll
    revealOnScroll();
    window.addEventListener('scroll', revealOnScroll);
}

// Menú móvil
function initMobileMenu() {
    // Crear botón de menú móvil
    const menuBtn = document.createElement('button');
    menuBtn.className = 'mobile-menu-btn';
    menuBtn.innerHTML = '<span class="material-icons">menu</span>';

    const navLinks = document.querySelector('.nav-links');
    if (!navLinks) return;

    // Configuración para móviles
    if (window.innerWidth <= 768) {
        const navContainer = document.querySelector('.main-nav .container');
        if (navContainer) {
            navContainer.insertBefore(menuBtn, navContainer.firstChild.nextSibling);

            menuBtn.addEventListener('click', () => {
                navLinks.classList.toggle('mobile-visible');
                menuBtn.innerHTML = navLinks.classList.contains('mobile-visible')
                    ? '<span class="material-icons">close</span>'
                    : '<span class="material-icons">menu</span>';
            });
        }
    }

    // Actualizar al cambiar tamaño de ventana
    window.addEventListener('resize', () => {
        if (window.innerWidth <= 768) {
            if (!document.querySelector('.mobile-menu-btn')) {
                const navContainer = document.querySelector('.main-nav .container');
                if (navContainer) {
                    navContainer.insertBefore(menuBtn, navContainer.firstChild.nextSibling);
                }
            }
        } else {
            const mobileBtn = document.querySelector('.mobile-menu-btn');
            if (mobileBtn) {
                mobileBtn.remove();
            }
            navLinks.classList.remove('mobile-visible');
        }
    });
}

// Efecto 3D en tarjetas
function initCardHoverEffect() {
    const cards = document.querySelectorAll('.card');

    cards.forEach(card => {
        card.addEventListener('mousemove', function(e) {
            const rect = card.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // Calcular rotación basada en la posición del mouse
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;

            const xRotation = ((y - centerY) / rect.height) * 5; // Max 5 grados
            const yRotation = ((centerX - x) / rect.width) * 5; // Max 5 grados

            // Aplicar transformación
            card.style.transform = `perspective(1000px) rotateX(${xRotation}deg) rotateY(${yRotation}deg) scale(1.02)`;

            // Efecto de luz brillante donde está el cursor
            const shine = card.querySelector('.shine');
            if (!shine) {
                const shineElement = document.createElement('div');
                shineElement.classList.add('shine');
                card.appendChild(shineElement);
            }

            const shineEl = card.querySelector('.shine');
            shineEl.style.background = `radial-gradient(circle at ${x}px ${y}px, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 60%)`;
        });

        card.addEventListener('mouseleave', function() {
            card.style.transform = '';
            const shine = card.querySelector('.shine');
            if (shine) {
                shine.remove();
            }
        });
    });
}

// ===== FUNCIONALIDAD DEL CALENDARIO =====

// Variables globales para el calendario
let currentDate = new Date();
let selectedDate = new Date();
let tasks = []; // Se llenará con datos del servidor, si el usuario está logueado
let displayCompleted = true; // Mostrar tareas completadas

function initCalendar() {
    const calendarDays = document.getElementById('calendar-days');
    const currentMonthElement = document.getElementById('current-month');
    const prevMonthBtn = document.getElementById('prev-month');
    const nextMonthBtn = document.getElementById('next-month');
    const toggleCompletedBtn = document.getElementById('toggle-completed');

    // Verificar elementos necesarios
    if (!calendarDays || !currentMonthElement || !prevMonthBtn || !nextMonthBtn) {
        console.error('Elementos del calendario no encontrados');
        return;
    }

    // Obtener eventos del calendario
    fetchCalendarEvents();

    // Inicializar calendario
    renderCalendar();

    // Eventos para navegación de meses
    prevMonthBtn.addEventListener('click', function() {
        currentDate.setMonth(currentDate.getMonth() - 1);
        renderCalendar();

        // Efecto visual
        this.classList.add('clicked');
        setTimeout(() => {
            this.classList.remove('clicked');
        }, 200);
    });

    nextMonthBtn.addEventListener('click', function() {
        currentDate.setMonth(currentDate.getMonth() + 1);
        renderCalendar();

        // Efecto visual
        this.classList.add('clicked');
        setTimeout(() => {
            this.classList.remove('clicked');
        }, 200);
    });

    // Alternar visibilidad de tareas completadas
    if (toggleCompletedBtn) {
        toggleCompletedBtn.addEventListener('click', function() {
            displayCompleted = !displayCompleted;
            this.innerHTML = displayCompleted
                ? '<span class="material-icons">visibility</span>'
                : '<span class="material-icons">visibility_off</span>';

            updateTodayTasks();
            updateUpcomingTasks();

            // Mostrar notificación
            showNotification(
                displayCompleted ? 'Mostrando tareas completadas' : 'Ocultando tareas completadas',
                'info',
                displayCompleted ? 'visibility' : 'visibility_off'
            );
        });
    }
}

// Obtener eventos del calendario desde el servidor
function fetchCalendarEvents() {
    // Simulación de carga con efecto visual
    const loaders = document.querySelectorAll('.calendar-container, .tasks-card, .stats-card, .upcoming-tasks');
    loaders.forEach(loader => {
        loader.classList.add('loading');
    });

    fetch('/api/calendario/eventos')
        .then(response => {
            if (!response.ok) {
                throw new Error('Error al obtener eventos del calendario');
            }
            return response.json();
        })
        .then(data => {
            // Transformar datos al formato de tareas
            tasks = data.map(event => {
                return {
                    id: event.id,
                    title: event.title,
                    type: event.type_id,
                    date: new Date(event.date + 'T' + event.time),
                    plantId: event.plant_id,
                    plantName: event.plant_name || 'Sin planta',
                    repeat: mapRecurrenceToForm(event.recurrence),
                    notes: event.notes || '',
                    completed: event.completed || false
                };
            });

            // Para demo, agregamos algunas tareas de ejemplo si no hay ninguna
            if (tasks.length === 0) {
                addDemoTasks();
            }

            // Actualizar UI
            renderCalendar();
            updateTodayTasks();
            updateUpcomingTasks();
            updateMonthStats();

            // Quitar clase de carga
            loaders.forEach(loader => {
                loader.classList.remove('loading');
            });
        })
        .catch(error => {
            console.error('Error al obtener eventos:', error);
            // Para usuarios no autenticados, continuamos con un array vacío
            // Agregar tareas de demostración
            addDemoTasks();

            // Actualizar UI
            renderCalendar();
            updateTodayTasks();
            updateUpcomingTasks();
            updateMonthStats();

            // Quitar clase de carga
            loaders.forEach(loader => {
                loader.classList.remove('loading');
            });
        });
}

// Agregar tareas de demostración (para no autenticados o como ejemplo)
function addDemoTasks() {
    const today = new Date();
    const tomorrow = new Date();
    tomorrow.setDate(today.getDate() + 1);

    const nextWeek = new Date();
    nextWeek.setDate(today.getDate() + 7);

    tasks = [
        {
            id: 1,
            title: 'Regar Agave',
            type: 1, // Riego
            date: new Date(today.setHours(9, 0, 0)),
            plantId: 1,
            plantName: 'Agave americana',
            repeat: 'weekly',
            notes: 'Regar moderadamente, evitar encharcamiento',
            completed: false
        },
        {
            id: 2,
            title: 'Fertilizar Nopal',
            type: 2, // Fertilizar
            date: new Date(tomorrow.setHours(17, 30, 0)),
            plantId: 2,
            plantName: 'Opuntia ficus-indica',
            repeat: 'monthly',
            notes: 'Usar fertilizante orgánico',
            completed: false
        },
        {
            id: 3,
            title: 'Podar Yucca',
            type: 3, // Podar
            date: new Date(nextWeek.setHours(10, 15, 0)),
            plantId: 3,
            plantName: 'Yucca filifera',
            repeat: 'none',
            notes: 'Eliminar hojas secas y amarillentas',
            completed: false
        }
    ];
}

// Mapa de recurrencia del backend a formulario
function mapRecurrenceToForm(recurrence) {
    if (!recurrence) return 'none';

    const recurrenceMap = {
        'Diario': 'daily',
        'Semanal': 'weekly',
        'Quincenal': 'biweekly',
        'Mensual': 'monthly'
    };

    return recurrenceMap[recurrence] || 'none';
}

// Mapa de recurrencia del formulario al backend
function mapFormRecurrenceToBackend(recurrence) {
    if (!recurrence || recurrence === 'none') return null;

    const recurrenceMap = {
        'daily': 'Diario',
        'weekly': 'Semanal',
        'biweekly': 'Quincenal',
        'monthly': 'Mensual'
    };

    return recurrenceMap[recurrence] || null;
}

// Renderizar calendario
function renderCalendar() {
    // Limpiar días del calendario
    const calendarDays = document.getElementById('calendar-days');
    if (!calendarDays) return;

    calendarDays.innerHTML = '';

    // Actualizar mes actual
    const monthNames = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio', 'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'];
    const currentMonthElement = document.getElementById('current-month');
    if (currentMonthElement) {
        currentMonthElement.textContent = `${monthNames[currentDate.getMonth()]} ${currentDate.getFullYear()}`;
    }

    // Calcular primer y último día del mes
    const firstDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const lastDay = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);

    // Día de la semana del primer día (0 = Domingo, 6 = Sábado)
    const firstDayIndex = firstDay.getDay();

    // Total de días en el mes
    const totalDays = lastDay.getDate();

    // Último día del mes anterior
    const prevLastDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), 0).getDate();

    // Días del mes anterior a mostrar
    for (let i = firstDayIndex; i > 0; i--) {
        const day = prevLastDay - i + 1;
        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, day);

        // Obtener tareas para este día
        const dayTasks = getTasksForDate(date);

        // Crear elemento de día
        const dayElement = createDayElement(day, date, 'other-month', dayTasks);
        calendarDays.appendChild(dayElement);
    }

    // Días del mes actual
    for (let i = 1; i <= totalDays; i++) {
        const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), i);

        // Obtener tareas para este día
        const dayTasks = getTasksForDate(date);

        // Verificar si es hoy
        const isToday = isSameDay(date, new Date());

        // Verificar si es el día seleccionado
        const isSelected = isSameDay(date, selectedDate);

        // Crear elemento de día
        const dayElement = createDayElement(i, date, isToday ? 'today' : (isSelected ? 'selected' : ''), dayTasks);
        calendarDays.appendChild(dayElement);
    }

    // Calcular días necesarios del próximo mes
    const daysFromNextMonth = 42 - (firstDayIndex + totalDays); // 42 = 6 filas * 7 días

    // Días del próximo mes a mostrar
    for (let i = 1; i <= daysFromNextMonth; i++) {
        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, i);

        // Obtener tareas para este día
        const dayTasks = getTasksForDate(date);

        // Crear elemento de día
        const dayElement = createDayElement(i, date, 'other-month', dayTasks);
        calendarDays.appendChild(dayElement);
    }

    // Actualizar tareas para el día seleccionado
    updateTodayTasks();

    // Actualizar estadísticas del mes
    updateMonthStats();

    // Actualizar próximas tareas
    updateUpcomingTasks();
}

// Crear elemento de día
function createDayElement(day, date, className, tasks) {
    const dayElement = document.createElement('div');
    dayElement.classList.add('calendar-day');

    if (className) {
        dayElement.classList.add(className);
    }

    // Agregar número de día
    const dayNumber = document.createElement('div');
    dayNumber.classList.add('day-number');
    dayNumber.textContent = day;
    dayElement.appendChild(dayNumber);

    // Agregar indicadores de tareas
    if (tasks.length > 0) {
        const taskDotsContainer = document.createElement('div');
        taskDotsContainer.classList.add('day-tasks');

        // Limitar a 3 puntos
        const maxDots = Math.min(tasks.length, 3);

        for (let i = 0; i < maxDots; i++) {
            const taskDot = document.createElement('div');
            taskDot.classList.add('day-task-dot');

            // Agregar clase basada en el tipo de tarea
            if (tasks[i].type) {
                taskDot.classList.add('task-type-' + tasks[i].type);
            }

            taskDotsContainer.appendChild(taskDot);
        }

        dayElement.appendChild(taskDotsContainer);
    }

    // Agregar evento de clic para seleccionar día
    dayElement.addEventListener('click', function() {
        // Quitar clase 'selected' de todos los días
        document.querySelectorAll('.calendar-day').forEach(day => {
            day.classList.remove('selected');
        });

        // Agregar clase 'selected' al día clicado
        dayElement.classList.add('selected');

        // Actualizar fecha seleccionada
        selectedDate = date;

        // Actualizar tareas para el día seleccionado
        updateTodayTasks();
    });

    return dayElement;
}

// Obtener tareas para una fecha específica
function getTasksForDate(date) {
    return tasks.filter(task => {
        return isSameDay(task.date, date);
    });
}

// Verificar si dos fechas son el mismo día
function isSameDay(date1, date2) {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
}

// Actualizar tareas para el día seleccionado
function updateTodayTasks() {
    const todayTasksContainer = document.getElementById('today-tasks');
    if (!todayTasksContainer) return;

    // Verificar si ya existe el mensaje de inicio de sesión (para usuarios no autenticados)
    if (todayTasksContainer.querySelector('.empty-state')) {
        // Mantener el mensaje de inicio de sesión
        return;
    }

    todayTasksContainer.innerHTML = '';

    // Obtener tareas para el día seleccionado
    let selectedTasks = getTasksForDate(selectedDate);

    // Filtrar tareas completadas si está desactivada la opción
    if (!displayCompleted) {
        selectedTasks = selectedTasks.filter(task => !task.completed);
    }

    // Formatear la fecha seleccionada
    const formattedDate = formatDate(selectedDate);

    // Actualizar el título de la sección
    const taskHeader = document.querySelector('.tasks-card .card-header h3');
    if (taskHeader) {
        taskHeader.innerHTML = `<span class="material-icons">today</span> Tareas para ${formattedDate}`;
    }

    if (selectedTasks.length === 0) {
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'empty-state';
        emptyMessage.innerHTML = `
            <span class="material-icons">event_available</span>
            <p>No hay tareas programadas para este día.</p>
        `;
        todayTasksContainer.appendChild(emptyMessage);
        return;
    }

    // Ordenar tareas por hora
    selectedTasks.sort((a, b) => a.date.getTime() - b.date.getTime());

    // Crear elementos de tarea
    selectedTasks.forEach(task => {
        const taskElement = createTaskElement(task);
        todayTasksContainer.appendChild(taskElement);
    });
}

// Actualizar estadísticas del mes
function updateMonthStats() {
    // Obtener todas las tareas del mes actual
    const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const lastDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);

    const monthTasks = tasks.filter(task => {
        return task.date >= firstDayOfMonth && task.date <= lastDayOfMonth;
    });

    // Contar tareas por tipo
    const taskCounts = {
        1: 0, // Riego
        2: 0, // Fertilización
        3: 0, // Poda
        4: 0  // Trasplante
    };

    monthTasks.forEach(task => {
        if (taskCounts[task.type] !== undefined) {
            taskCounts[task.type]++;
        }
    });

    // Actualizar contadores en la UI
    updateTypeCount('watering-count', taskCounts[1]);
    updateTypeCount('fertilizing-count', taskCounts[2]);
    updateTypeCount('pruning-count', taskCounts[3]);
    updateTypeCount('repotting-count', taskCounts[4]);
}

// Actualizar conteo para un tipo de tarea
function updateTypeCount(elementId, count) {
    const element = document.getElementById(elementId);
    if (!element) return;

    element.textContent = `${count} tarea${count !== 1 ? 's' : ''}`;

    // Actualizar opacidad según si hay tareas o no
    element.parentElement.style.opacity = count === 0 ? '0.5' : '1';
}

// Actualizar próximas tareas
function updateUpcomingTasks() {
    const upcomingTasksContainer = document.getElementById('upcoming-tasks');
    if (!upcomingTasksContainer) return;

    // Verificar si ya existe el mensaje de inicio de sesión (para usuarios no autenticados)
    if (upcomingTasksContainer.querySelector('.empty-state')) {
        // Mantener el mensaje de inicio de sesión
        return;
    }

    upcomingTasksContainer.innerHTML = '';

    // Obtener fecha actual
    const now = new Date();

    // Obtener fecha límite (7 días después)
    const limitDate = new Date();
    limitDate.setDate(now.getDate() + 7);

    // Filtrar tareas próximas
    let upcomingTasks = tasks.filter(task => {
        return task.date >= now && task.date <= limitDate && !isSameDay(task.date, selectedDate);
    });

    // Filtrar tareas completadas si está desactivada la opción
    if (!displayCompleted) {
        upcomingTasks = upcomingTasks.filter(task => !task.completed);
    }

    // Ordenar por fecha
    upcomingTasks.sort((a, b) => a.date.getTime() - b.date.getTime());

    // Limitar a las 5 más próximas
    upcomingTasks = upcomingTasks.slice(0, 5);

    if (upcomingTasks.length === 0) {
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'empty-state';
        emptyMessage.innerHTML = `
            <span class="material-icons">event_available</span>
            <p>No hay tareas programadas para los próximos días.</p>
        `;
        upcomingTasksContainer.appendChild(emptyMessage);
        return;
    }

    // Crear elementos de tarea
    upcomingTasks.forEach(task => {
        const taskElement = createTaskElement(task, true);
        upcomingTasksContainer.appendChild(taskElement);
    });
}

// Crear elemento de tarea
function createTaskElement(task, showDate = false) {
    const taskElement = document.createElement('div');
    taskElement.classList.add('task-item');
    taskElement.dataset.id = task.id;

    if (task.completed) {
        taskElement.classList.add('completed');
    }

    // Icono de tarea
    const taskIcon = document.createElement('div');
    taskIcon.classList.add('task-icon', 'task-type-' + task.type);

    const iconSpan = document.createElement('span');
    iconSpan.classList.add('material-icons');

    // Establecer icono según tipo de tarea
    switch (parseInt(task.type)) {
        case 1: // Regar
            iconSpan.textContent = 'opacity';
            break;
        case 2: // Fertilizar
            iconSpan.textContent = 'eco';
            break;
        case 3: // Podar
            iconSpan.textContent = 'content_cut';
            break;
        case 4: // Trasplantar
            iconSpan.textContent = 'yard';
            break;
        default:
            iconSpan.textContent = 'event';
    }

    taskIcon.appendChild(iconSpan);
    taskElement.appendChild(taskIcon);

    // Información de tarea
    const taskInfo = document.createElement('div');
    taskInfo.classList.add('task-info');

    const taskTitle = document.createElement('h4');
    taskTitle.textContent = task.title;
    taskInfo.appendChild(taskTitle);

    // Subtítulo con hora y planta
    const taskSubtitle = document.createElement('p');

    // Crear el texto base con la hora/fecha
    let timeText = '';
    if (showDate) {
        // Mostrar fecha además de hora
        timeText = `${formatDateCompact(task.date)} a las ${formatTime(task.date)}`;
    } else {
        // Solo mostrar hora
        timeText = `${formatTime(task.date)}`;
    }

    // Agregar información de la planta con icono si existe
    if (task.plantId) {
        const plantSpan = document.createElement('span');
        plantSpan.classList.add('plant-info');

        const plantIcon = document.createElement('span');
        plantIcon.classList.add('material-icons');
        plantIcon.textContent = 'spa';

        plantSpan.appendChild(plantIcon);
        plantSpan.appendChild(document.createTextNode(` ${task.plantName}`));

        taskSubtitle.textContent = timeText + ' · ';
        taskSubtitle.appendChild(plantSpan);
    } else {
        taskSubtitle.textContent = timeText;
    }

    // Si es tarea recurrente, mostrar icono
    if (task.repeat !== 'none') {
        const repeatIcon = document.createElement('span');
        repeatIcon.className = 'material-icons repeat-icon';
        repeatIcon.textContent = 'repeat';
        repeatIcon.title = getRepeatText(task.repeat);
        taskSubtitle.appendChild(repeatIcon);
    }

    taskInfo.appendChild(taskSubtitle);
    taskElement.appendChild(taskInfo);

    // Acciones de tarea - solo si hay un modal de tareas (lo que significa que el usuario está autenticado)
    if (document.getElementById('task-modal')) {
        const taskActions = document.createElement('div');
        taskActions.classList.add('task-actions');

        // Botón completar
        const completeButton = document.createElement('button');
        completeButton.classList.add('btn', 'btn-icon');
        completeButton.innerHTML = task.completed
            ? '<span class="material-icons">refresh</span>'
            : '<span class="material-icons">check_circle</span>';
        completeButton.title = task.completed ? 'Marcar como pendiente' : 'Marcar como completada';

        completeButton.addEventListener('click', function(e) {
            e.stopPropagation();

            // Enviar solicitud para marcar tarea como completada
            toggleTaskComplete(task);
        });
        taskActions.appendChild(completeButton);

        // Botón editar
        const editButton = document.createElement('button');
        editButton.classList.add('btn', 'btn-icon');
        editButton.innerHTML = '<span class="material-icons">edit</span>';
        editButton.title = 'Editar tarea';

        editButton.addEventListener('click', function(e) {
            e.stopPropagation();

            // Abrir modal de edición
            openTaskModal(task);
        });
        taskActions.appendChild(editButton);

        // Botón eliminar
        const deleteButton = document.createElement('button');
        deleteButton.classList.add('btn', 'btn-icon');
        deleteButton.innerHTML = '<span class="material-icons">delete</span>';
        deleteButton.title = 'Eliminar tarea';

        deleteButton.addEventListener('click', function(e) {
            e.stopPropagation();

            if (confirm('¿Estás seguro de que deseas eliminar esta tarea?')) {
                // Enviar solicitud para eliminar tarea
                deleteTask(task.id);
            }
        });
        taskActions.appendChild(deleteButton);

        taskElement.appendChild(taskActions);
    }

    // Al hacer clic en la tarea, mostrar detalles en un modal
    taskElement.addEventListener('click', function() {
        if (task.notes) {
            // Mostrar notas en un modal
            showTaskDetails(task);
        }
    });

    return taskElement;
}

// Mostrar notas de tarea en un modal
function showTaskDetails(task) {
    // Crear modal
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'modal active';

    const modalContent = document.createElement('div');
    modalContent.className = 'modal-content';

    const taskTypeNames = {
        1: 'Riego',
        2: 'Fertilización',
        3: 'Poda',
        4: 'Trasplante',
        5: 'Otro'
    };

    const taskTypeName = taskTypeNames[task.type] || 'Tarea';

    modalContent.innerHTML = `
        <div class="modal-header">
            <h3>${task.title}</h3>
            <button id="close-details-modal" class="btn btn-icon">
                <span class="material-icons">close</span>
            </button>
        </div>
        <div class="task-details">
            <div class="task-detail-item">
                <div class="detail-icon task-type-${task.type}">
                    <span class="material-icons">
                        ${task.type == 1 ? 'opacity' : task.type == 2 ? 'eco' : task.type == 3 ? 'content_cut' : 'yard'}
                    </span>
                </div>
                <div class="detail-info">
                    <span class="detail-label">Tipo</span>
                    <span class="detail-value">${taskTypeName}</span>
                </div>
            </div>
            <div class="task-detail-item">
                <div class="detail-icon">
                    <span class="material-icons">schedule</span>
                </div>
                <div class="detail-info">
                    <span class="detail-label">Fecha y hora</span>
                    <span class="detail-value">${formatDate(task.date)} a las ${formatTime(task.date)}</span>
                </div>
            </div>
            <div class="task-detail-item">
                <div class="detail-icon">
                    <span class="material-icons">spa</span>
                </div>
                <div class="detail-info">
                    <span class="detail-label">Planta</span>
                    <span class="detail-value">${task.plantName}</span>
                </div>
            </div>
            ${task.repeat !== 'none' ? `
            <div class="task-detail-item">
                <div class="detail-icon">
                    <span class="material-icons">repeat</span>
                </div>
                <div class="detail-info">
                    <span class="detail-label">Repetición</span>
                    <span class="detail-value">${getRepeatText(task.repeat)}</span>
                </div>
            </div>
            ` : ''}
            <div class="task-notes">
                <h4>Notas</h4>
                <p>${task.notes}</p>
            </div>
        </div>
    `;

    modalOverlay.appendChild(modalContent);
    document.body.appendChild(modalOverlay);

    // Añadir estilos al vuelo para los detalles
    const styleElement = document.createElement('style');
    styleElement.textContent = `
        .task-details {
            padding: 20px;
        }
        .task-detail-item {
            display: flex;
            margin-bottom: 15px;
            align-items: center;
        }
        .detail-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
        }
        .detail-info {
            flex-grow: 1;
        }
        .detail-label {
            display: block;
            font-size: 12px;
            color: var(--text-dark);
            opacity: 0.7;
        }
        .detail-value {
            font-weight: 500;
        }
        .task-notes {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid rgba(0,0,0,0.05);
        }
        .task-notes h4 {
            margin: 0 0 10px;
            font-weight: 500;
            color: var(--primary);
        }
        .task-notes p {
            margin: 0;
            white-space: pre-line;
        }
    `;
    document.head.appendChild(styleElement);

    // Agregar evento para cerrar
    const closeButton = document.getElementById('close-details-modal');
    if (closeButton) {
        closeButton.addEventListener('click', function() {
            modalOverlay.remove();
            styleElement.remove();
        });
    }

    // Cerrar al hacer clic fuera
    modalOverlay.addEventListener('click', function(event) {
        if (event.target === modalOverlay) {
            modalOverlay.remove();
            styleElement.remove();
        }
    });
}

// Obtener texto descriptivo para repetición
function getRepeatText(repeat) {
    switch (repeat) {
        case 'daily':
            return 'Diariamente';
        case 'weekly':
            return 'Semanalmente';
        case 'biweekly':
            return 'Cada dos semanas';
        case 'monthly':
            return 'Mensualmente';
        default:
            return 'No se repite';
    }
}

// Alternar estado de completado de una tarea
function toggleTaskComplete(task) {
    const newStatus = !task.completed;

    // Para demo, actualizamos localmente
    task.completed = newStatus;

    // En un entorno real, enviaríamos al servidor
    /*
    fetch(`/recordatorios/completar/${task.id}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': document.querySelector('input[name="csrf_token"]')?.value || ''
        },
        body: `status=${newStatus ? '1' : '0'}`
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Error al actualizar tarea');
        }

        // Actualizar tarea local
        task.completed = newStatus;

        // Actualizar UI
        renderCalendar();
        updateTodayTasks();
        updateUpcomingTasks();

        // Mostrar notificación
        showNotification(
            newStatus ? 'Tarea completada' : 'Tarea marcada como pendiente',
            'success',
            newStatus ? 'check_circle' : 'refresh'
        );
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error al actualizar la tarea', 'error', 'error');
    });
    */

    // Actualizar UI
    renderCalendar();
    updateTodayTasks();
    updateUpcomingTasks();

    // Mostrar notificación
    showNotification(
        newStatus ? 'Tarea completada' : 'Tarea marcada como pendiente',
        'success',
        newStatus ? 'check_circle' : 'refresh'
    );
}

// Eliminar tarea
function deleteTask(taskId) {
    // Para demo, eliminamos localmente
    tasks = tasks.filter(task => task.id !== taskId);

    // En un entorno real, enviaríamos al servidor
    /*
    fetch(`/recordatorios/eliminar/${taskId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': document.querySelector('input[name="csrf_token"]')?.value || ''
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Error al eliminar tarea');
        }

        // Eliminar tarea de array local
        tasks = tasks.filter(task => task.id !== taskId);

        // Actualizar UI
        renderCalendar();
        updateTodayTasks();
        updateUpcomingTasks();
        updateMonthStats();

        // Mostrar notificación
        showNotification('Tarea eliminada correctamente', 'success', 'delete');
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error al eliminar la tarea', 'error', 'error');
    });
    */

    // Actualizar UI
    renderCalendar();
    updateTodayTasks();
    updateUpcomingTasks();
    updateMonthStats();

    // Mostrar notificación
    showNotification('Tarea eliminada correctamente', 'success', 'delete');
}

// ===== MODAL DE TAREAS =====

function initModal() {
    const taskModal = document.getElementById('task-modal');
    if (!taskModal) return;

    const closeModalBtn = document.getElementById('close-modal');
    const cancelTaskBtn = document.getElementById('cancel-task');
    const addTaskBtn = document.getElementById('add-task-btn');
    const taskForm = document.getElementById('task-form');

    // Abrir modal de tarea
    function openTaskModal(task = null) {
        // Resetear formulario
        taskForm.reset();

        // Establecer título del modal
        const modalTitle = taskModal.querySelector('.modal-header h3');
        modalTitle.textContent = task ? 'Editar Tarea' : 'Agregar Nueva Tarea';

        // Si estamos editando una tarea, llenar formulario con datos de la tarea
        if (task) {
            document.getElementById('task-title').value = task.title;
            document.getElementById('task-type').value = task.type;
            document.getElementById('task-plant').value = task.plantId;

            // Formatear fecha para input
            const year = task.date.getFullYear();
            const month = String(task.date.getMonth() + 1).padStart(2, '0');
            const day = String(task.date.getDate()).padStart(2, '0');
            document.getElementById('task-date').value = `${year}-${month}-${day}`;

            // Formatear hora para input
            const hours = String(task.date.getHours()).padStart(2, '0');
            const minutes = String(task.date.getMinutes()).padStart(2, '0');
            document.getElementById('task-time').value = `${hours}:${minutes}`;

            document.getElementById('task-repeat').value = task.repeat;
            document.getElementById('task-notes').value = task.notes;

            // Actualizar acción del formulario para edición
            taskForm.action = `/recordatorios/editar/${task.id}`;
        } else {
            // Establecer fecha por defecto a fecha seleccionada
            const year = selectedDate.getFullYear();
            const month = String(selectedDate.getMonth() + 1).padStart(2, '0');
            const day = String(selectedDate.getDate()).padStart(2, '0');
            document.getElementById('task-date').value = `${year}-${month}-${day}`;

            // Establecer hora por defecto a hora actual
            const now = new Date();
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            document.getElementById('task-time').value = `${hours}:${minutes}`;

            // Resetear acción del formulario para agregar
            taskForm.action = '/calendario';
        }

        // Mostrar modal con animación
        taskModal.classList.add('active');

        // Animar entrada de formulario
        const modalContent = taskModal.querySelector('.modal-content');
        modalContent.style.transform = 'scale(0.9)';
        setTimeout(() => {
            modalContent.style.transform = 'scale(1)';
        }, 50);
    }

    // Cerrar modal de tarea
    function closeTaskModal() {
        // Animar salida
        const modalContent = taskModal.querySelector('.modal-content');
        modalContent.style.transform = 'scale(0.9)';

        setTimeout(() => {
            taskModal.classList.remove('active');
            modalContent.style.transform = '';
        }, 200);
    }

    // Event listeners para modal
    if (closeModalBtn) {
        closeModalBtn.addEventListener('click', closeTaskModal);
    }

    if (cancelTaskBtn) {
        cancelTaskBtn.addEventListener('click', closeTaskModal);
    }

    // Botón agregar tarea
    if (addTaskBtn) {
        addTaskBtn.addEventListener('click', function() {
            openTaskModal();
        });
    }

    // Manejar envío de formulario
    if (taskForm) {
        taskForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Mostrar estado de carga
            const submitButton = taskForm.querySelector('button[type="submit"]');
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="material-icons spin">refresh</span> Guardando...';

            // Obtener datos del formulario
            const formData = new FormData(taskForm);

            // Para demo, procesamos localmente
            addOrUpdateTask(formData);

            // En un entorno real, enviaríamos al servidor
            /*
            fetch(taskForm.action, {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Error al guardar tarea');
                }
                return response.json();
            })
            .then(data => {
                // Cerrar modal
                closeTaskModal();

                // Actualizar tareas
                fetchCalendarEvents();

                // Mostrar notificación
                showNotification('Tarea guardada correctamente', 'success', 'check_circle');
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Error al guardar la tarea', 'error', 'error');

                // Restaurar botón
                submitButton.disabled = false;
                submitButton.innerHTML = 'Guardar';
            });
            */

            // Simular delay para demo
            setTimeout(() => {
                // Cerrar modal
                closeTaskModal();

                // Mostrar notificación
                showNotification('Tarea guardada correctamente', 'success', 'check_circle');

                // Restaurar botón
                submitButton.disabled = false;
                submitButton.innerHTML = 'Guardar';
            }, 800);
        });
    }

    // Agregar a ámbito global
    window.openTaskModal = openTaskModal;
}

// Agregar o actualizar tarea (demo)
function addOrUpdateTask(formData) {
    const title = formData.get('task-title');
    const type = formData.get('task-type');
    const plantId = formData.get('task-plant');
    const date = formData.get('task-date');
    const time = formData.get('task-time');
    const repeat = formData.get('task-repeat');
    const notes = formData.get('task-notes');

    // Crear objeto fecha
    const taskDate = new Date(`${date}T${time}`);

    // Verificar si estamos editando (url contiene /editar/)
    const taskForm = document.getElementById('task-form');
    const isEditing = taskForm.action.includes('/editar/');

    if (isEditing) {
        // Obtener ID de la tarea
        const taskId = parseInt(taskForm.action.split('/').pop());

        // Buscar tarea en el array
        const task = tasks.find(t => t.id === taskId);

        if (task) {
            // Actualizar tarea
            task.title = title;
            task.type = type;
            task.plantId = plantId;
            task.date = taskDate;
            task.repeat = repeat;
            task.notes = notes;
        }
    } else {
        // Crear nueva tarea
        const newTask = {
            id: Math.max(0, ...tasks.map(t => t.id)) + 1,
            title: title,
            type: type,
            plantId: plantId || '',
            plantName: plantId ? 'Planta seleccionada' : 'Sin planta',
            date: taskDate,
            repeat: repeat,
            notes: notes,
            completed: false
        };

        // Obtener el nombre de la planta del selector
        if (plantId) {
            const plantSelect = document.getElementById('task-plant');
            const selectedOption = plantSelect.options[plantSelect.selectedIndex];
            newTask.plantName = selectedOption.text;
        } else {
            newTask.plantName = 'Sin planta';
        }

        // Agregar a array de tareas
        tasks.push(newTask);
    }

    // Actualizar UI
    renderCalendar();
    updateTodayTasks();
    updateUpcomingTasks();
    updateMonthStats();
}

// ===== UTILIDADES =====

// Formatear hora (HH:MM AM/PM)
function formatTime(date) {
    let hours = date.getHours();
    let minutes = date.getMinutes();
    const ampm = hours >= 12 ? 'PM' : 'AM';

    hours = hours % 12;
    hours = hours ? hours : 12; // la hora '0' debería ser '12'
    minutes = minutes < 10 ? '0' + minutes : minutes;

    return `${hours}:${minutes} ${ampm}`;
}

// Formatear fecha (Lunes, 15 de Enero)
function formatDate(date) {
    const days = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'];
    const months = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio', 'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'];

    const dayName = days[date.getDay()];
    const day = date.getDate();
    const month = months[date.getMonth()];

    return `${dayName}, ${day} de ${month}`;
}

// Formatear fecha compacta (15 Ene)
function formatDateCompact(date) {
    const months = ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'];

    const day = date.getDate();
    const month = months[date.getMonth()];

    return `${day} ${month}`;
}

// Mostrar notificación
function showNotification(message, type = 'info', icon = null) {
    const notificationContainer = document.getElementById('notification-container');
    if (!notificationContainer) return;

    const notification = document.createElement('div');
    notification.className = `notification ${type}`;

    // Icono por defecto según tipo
    let defaultIcon = 'info';
    if (type === 'success') defaultIcon = 'check_circle';
    if (type === 'error') defaultIcon = 'error';
    if (type === 'warning') defaultIcon = 'warning';

    notification.innerHTML = `
        <span class="material-icons">${icon || defaultIcon}</span>
        <div class="notification-content">${message}</div>
    `;

    notificationContainer.appendChild(notification);

    // Efecto de entrada
    notification.classList.add('show');

    // Eliminar después de 3 segundos
    setTimeout(() => {
        notification.classList.add('fade-out');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}