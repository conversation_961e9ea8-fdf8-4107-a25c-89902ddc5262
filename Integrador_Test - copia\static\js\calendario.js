// Variables globales
let currentDate = new Date();
let showAgriculturalCalendar = false;
let userEvents = {};

const monthNames = [
    'Enero', 'Febrero', '<PERSON><PERSON>', '<PERSON>bril', '<PERSON>', '<PERSON><PERSON>',
    '<PERSON>', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
];

// Tareas de ejemplo
const sampleTasks = {
    '2025-01-15': ['Regar Agave', '<PERSON>rt<PERSON><PERSON>'],
    '2025-01-16': ['Podar Yucca'],
    '2025-01-20': ['Trasplantar Biznaga'],
    '2025-01-25': ['Regar Lechuguilla', 'Revisar Mezquite']
};

// Calendario agrícola de Chihuahua
const agriculturalCalendar = {
    1: { // Enero
        activities: ['Preparación de terrenos', 'Poda de frutales', 'Siembra de ajo'],
        plants: ['Ajo', 'Ce<PERSON><PERSON>', 'Espinaca', 'Lechuga']
    },
    2: { // Febrero
        activities: ['Siembra de hortalizas de invierno', 'Injertos', 'Preparación de almácigos'],
        plants: ['Brócoli', 'Coliflor', 'Zanahoria', 'Rábano']
    },
    3: { // Marzo
        activities: ['Siembra de primavera', 'Trasplantes', 'Control de plagas'],
        plants: ['Tomate', 'Chile', 'Calabaza', 'Frijol']
    },
    4: { // Abril
        activities: ['Siembra de cultivos de temporal', 'Riego intensivo', 'Fertilización'],
        plants: ['Maíz', 'Sorgo', 'Girasol', 'Sandía']
    },
    5: { // Mayo
        activities: ['Siembra tardía', 'Control de malezas', 'Cosecha temprana'],
        plants: ['Melón', 'Pepino', 'Okra', 'Quelite']
    },
    6: { // Junio
        activities: ['Inicio de lluvias', 'Siembra de temporal', 'Cosecha de primavera'],
        plants: ['Frijol de temporal', 'Maíz criollo', 'Calabaza de castilla']
    },
    7: { // Julio
        activities: ['Temporada de lluvias', 'Siembra intensiva', 'Control fitosanitario'],
        plants: ['Quelites', 'Verdolagas', 'Chía', 'Amaranto']
    },
    8: { // Agosto
        activities: ['Mantenimiento de cultivos', 'Cosecha de verano', 'Preparación de conservas'],
        plants: ['Elote', 'Calabaza tierna', 'Ejotes', 'Nopales']
    },
    9: { // Septiembre
        activities: ['Cosecha principal', 'Secado de granos', 'Preparación de semillas'],
        plants: ['Maíz', 'Frijol', 'Chile seco', 'Quelites']
    },
    10: { // Octubre
        activities: ['Cosecha de otoño', 'Almacenamiento', 'Siembra de invierno'],
        plants: ['Calabaza de castilla', 'Frijol', 'Avena', 'Cebada']
    },
    11: { // Noviembre
        activities: ['Preparación para invierno', 'Poda', 'Conservación de semillas'],
        plants: ['Trigo', 'Avena', 'Cebolla de cambray', 'Cilantro']
    },
    12: { // Diciembre
        activities: ['Descanso de tierras', 'Planificación', 'Mantenimiento de herramientas'],
        plants: ['Ajo', 'Cebolla', 'Espinaca de invierno', 'Acelga']
    }
};

// Inicializar calendario
function initCalendar() {
    updateCalendarHeader();
    generateCalendarDays();
}

// Actualizar header del calendario
function updateCalendarHeader() {
    const monthYear = document.getElementById('current-month-year');
    monthYear.textContent = `${monthNames[currentDate.getMonth()]} ${currentDate.getFullYear()}`;
}

// Generar días del calendario
function generateCalendarDays() {
    const daysGrid = document.getElementById('days-grid');
    daysGrid.innerHTML = '';

    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    // Primer día del mes
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    // Generar 42 días (6 semanas)
    for (let i = 0; i < 42; i++) {
        const date = new Date(startDate);
        date.setDate(startDate.getDate() + i);

        const dayElement = createDayElement(date, month);
        daysGrid.appendChild(dayElement);
    }
}

// Crear elemento de día
function createDayElement(date, currentMonth) {
    const dayDiv = document.createElement('div');
    dayDiv.className = 'day';
    dayDiv.textContent = date.getDate();

    // Verificar si es del mes actual
    if (date.getMonth() !== currentMonth) {
        dayDiv.classList.add('other-month');
    }

    // Verificar si es hoy
    const today = new Date();
    if (date.toDateString() === today.toDateString()) {
        dayDiv.classList.add('today');
    }

    // Verificar si tiene tareas
    const dateString = date.toISOString().split('T')[0];
    if (sampleTasks[dateString] || userEvents[dateString]) {
        dayDiv.classList.add('has-tasks');
        const tasks = [...(sampleTasks[dateString] || []), ...(userEvents[dateString] || [])];
        dayDiv.title = `Tareas: ${tasks.join(', ')}`;
    }

    // Mostrar información agrícola si está activada
    if (showAgriculturalCalendar && date.getMonth() === currentMonth) {
        const monthData = agriculturalCalendar[date.getMonth() + 1];
        if (monthData && date.getDate() <= 7) { // Mostrar solo en la primera semana
            dayDiv.classList.add('agricultural');
            const activities = monthData.activities.join(', ');
            const plants = monthData.plants.join(', ');
            dayDiv.title = `Actividades agrícolas: ${activities}\nPlantas recomendadas: ${plants}`;
        }
    }

    // Agregar evento de clic para seleccionar fecha
    dayDiv.addEventListener('click', () => {
        document.querySelectorAll('.day.selected').forEach(d => d.classList.remove('selected'));
        dayDiv.classList.add('selected');

        // Establecer fecha en el modal
        const eventDate = document.getElementById('event-date');
        eventDate.value = date.toISOString().split('T')[0];
    });

    return dayDiv;
}

// Navegación de meses
document.getElementById('prev-month').addEventListener('click', () => {
    currentDate.setMonth(currentDate.getMonth() - 1);
    initCalendar();
});

document.getElementById('next-month').addEventListener('click', () => {
    currentDate.setMonth(currentDate.getMonth() + 1);
    initCalendar();
});

// Completar tareas
document.querySelectorAll('.task-complete').forEach(button => {
    button.addEventListener('click', function() {
        const taskItem = this.closest('.task-item');
        taskItem.style.opacity = '0.5';
        taskItem.style.textDecoration = 'line-through';
        this.style.background = 'rgba(76, 175, 80, 0.8)';
        this.textContent = '✓';

        // Actualizar contador
        const taskCard = taskItem.closest('.task-card');
        const countElement = taskCard.querySelector('.task-count');
        let count = parseInt(countElement.textContent);
        countElement.textContent = Math.max(0, count - 1);
    });
});

// Funciones del modal
function openModal() {
    document.getElementById('event-modal').classList.add('show');
}

function closeModal() {
    document.getElementById('event-modal').classList.remove('show');
    document.getElementById('event-form').reset();
}

// Agregar evento
document.getElementById('event-form').addEventListener('submit', function(e) {
    e.preventDefault();

    const title = document.getElementById('event-title').value;
    const type = document.getElementById('event-type').value;
    const plant = document.getElementById('event-plant').value;
    const date = document.getElementById('event-date').value;
    const time = document.getElementById('event-time').value;
    const notes = document.getElementById('event-notes').value;

    // Crear evento
    const eventText = `${title}${plant ? ' - ' + plant : ''}${time ? ' (' + time + ')' : ''}`;

    if (!userEvents[date]) {
        userEvents[date] = [];
    }
    userEvents[date].push(eventText);

    // Actualizar calendario
    generateCalendarDays();
    closeModal();

    // Mostrar notificación
    alert('Evento agregado exitosamente!');
});

// Toggle calendario agrícola
document.getElementById('toggle-agricultural').addEventListener('click', function() {
    showAgriculturalCalendar = !showAgriculturalCalendar;
    this.classList.toggle('active');

    if (showAgriculturalCalendar) {
        this.innerHTML = '<span class="material-icons">agriculture</span>Ocultar Calendario Agrícola';
        showAgriculturalInfo();
    } else {
        this.innerHTML = '<span class="material-icons">agriculture</span>Calendario Agrícola';
        hideAgriculturalInfo();
    }

    generateCalendarDays();
});

// Mostrar información agrícola
function showAgriculturalInfo() {
    const month = currentDate.getMonth() + 1;
    const monthData = agriculturalCalendar[month];

    if (monthData) {
        const infoDiv = document.createElement('div');
        infoDiv.id = 'agricultural-info';
        infoDiv.className = 'agricultural-info';
        infoDiv.innerHTML = `
            <div class="agricultural-card">
                <h3>🌾 Calendario Agrícola - ${monthNames[month - 1]}</h3>
                <div class="agricultural-content">
                    <div class="agricultural-section">
                        <h4>📋 Actividades Recomendadas:</h4>
                        <ul>
                            ${monthData.activities.map(activity => `<li>${activity}</li>`).join('')}
                        </ul>
                    </div>
                    <div class="agricultural-section">
                        <h4>🌱 Plantas de Temporada:</h4>
                        <ul>
                            ${monthData.plants.map(plant => `<li>${plant}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            </div>
        `;

        document.querySelector('.tasks-section').insertBefore(infoDiv, document.querySelector('.tasks-grid'));
    }
}

// Ocultar información agrícola
function hideAgriculturalInfo() {
    const infoDiv = document.getElementById('agricultural-info');
    if (infoDiv) {
        infoDiv.remove();
    }
}

// Event listeners
document.getElementById('add-event-btn').addEventListener('click', openModal);

// Cerrar modal al hacer clic fuera
document.getElementById('event-modal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});

// Inicializar cuando se carga la página
document.addEventListener('DOMContentLoaded', initCalendar);
