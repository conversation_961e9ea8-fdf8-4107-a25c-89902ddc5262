// JavaScript para sistema de clubes
document.addEventListener('DOMContentLoaded', function() {
    // Variables globales
    let joinedClubs = new Set();
    let currentFilter = 'all';

    // Elementos del DOM
    const categoryFilters = document.querySelectorAll('.category-filter');
    const joinButtons = document.querySelectorAll('.join-club');
    const viewButtons = document.querySelectorAll('.view-club');
    const createClubBtn = document.getElementById('create-club-btn');
    const searchInput = document.querySelector('.search-box input');
    const clubCards = document.querySelectorAll('.club-card');

    // Datos de ejemplo de clubes
    const clubsData = {
        1: {
            name: 'Cactus Lovers México',
            members: 1234,
            category: 'cactus',
            description: 'Comunidad dedicada al cultivo y cuidado de cactus y suculentas.',
            features: ['Chat exclusivo', 'Eventos mensuales', 'Biblioteca especializada', 'Intercambio de plantas']
        },
        2: {
            name: 'Orquídeas Exóticas',
            members: 892,
            category: 'orquideas',
            description: 'Para amantes de las orquídeas. Técnicas avanzadas de cultivo.',
            features: ['Webinars semanales', 'Chat de expertos', 'Calendario de floración', 'Foro especializado']
        },
        3: {
            name: 'Plantas Carnívoras',
            members: 567,
            category: 'carnivoras',
            description: 'Fascinante mundo de las plantas carnívoras.',
            features: ['Chat exclusivo', 'Guías de cuidado', 'Eventos de intercambio', 'Identificación de especies']
        }
    };

    // Filtros de categoría
    categoryFilters.forEach(filter => {
        filter.addEventListener('click', function() {
            // Remover clase active de todos los filtros
            categoryFilters.forEach(f => f.classList.remove('active'));
            
            // Agregar clase active al filtro seleccionado
            this.classList.add('active');
            
            // Obtener categoría seleccionada
            currentFilter = this.dataset.category;
            
            // Filtrar clubes
            filterClubs(currentFilter);
            
            // Animación de feedback
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    });

    // Función para filtrar clubes
    function filterClubs(category) {
        clubCards.forEach(card => {
            const cardCategory = card.dataset.category;
            
            if (category === 'all' || cardCategory === category) {
                card.style.display = 'block';
                card.style.animation = 'slideInUp 0.6s ease-out';
            } else {
                card.style.display = 'none';
            }
        });
        
        // Actualizar contador de resultados
        const visibleCards = document.querySelectorAll('.club-card[style*="block"], .club-card:not([style*="none"])');
        console.log(`Mostrando ${visibleCards.length} clubes para la categoría: ${category}`);
    }

    // Búsqueda de clubes
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const searchTerm = this.value.toLowerCase().trim();
        
        searchTimeout = setTimeout(() => {
            searchClubs(searchTerm);
        }, 300);
    });

    function searchClubs(searchTerm) {
        clubCards.forEach(card => {
            const clubName = card.querySelector('h3').textContent.toLowerCase();
            const clubDescription = card.querySelector('.club-description').textContent.toLowerCase();
            const clubTags = Array.from(card.querySelectorAll('.tag')).map(tag => tag.textContent.toLowerCase());
            
            const matchesSearch = searchTerm === '' || 
                                clubName.includes(searchTerm) || 
                                clubDescription.includes(searchTerm) ||
                                clubTags.some(tag => tag.includes(searchTerm));
            
            const matchesFilter = currentFilter === 'all' || card.dataset.category === currentFilter;
            
            if (matchesSearch && matchesFilter) {
                card.style.display = 'block';
                highlightSearchTerm(card, searchTerm);
            } else {
                card.style.display = 'none';
            }
        });
    }

    function highlightSearchTerm(card, searchTerm) {
        if (searchTerm === '') return;
        
        const title = card.querySelector('h3');
        const description = card.querySelector('.club-description');
        
        // Remover highlights anteriores
        title.innerHTML = title.textContent;
        description.innerHTML = description.textContent;
        
        // Agregar nuevos highlights
        if (title.textContent.toLowerCase().includes(searchTerm)) {
            const regex = new RegExp(`(${searchTerm})`, 'gi');
            title.innerHTML = title.textContent.replace(regex, '<mark>$1</mark>');
        }
        
        if (description.textContent.toLowerCase().includes(searchTerm)) {
            const regex = new RegExp(`(${searchTerm})`, 'gi');
            description.innerHTML = description.textContent.replace(regex, '<mark>$1</mark>');
        }
    }

    // Unirse a clubes
    joinButtons.forEach(button => {
        button.addEventListener('click', function() {
            const clubId = this.dataset.clubId;
            const clubData = clubsData[clubId];
            
            if (joinedClubs.has(clubId)) {
                // Salir del club
                leaveClub(clubId, this);
            } else {
                // Unirse al club
                joinClub(clubId, this, clubData);
            }
        });
    });

    function joinClub(clubId, button, clubData) {
        // Animación de carga
        button.disabled = true;
        button.innerHTML = '<span class="material-icons rotating">hourglass_empty</span> Uniéndose...';
        
        setTimeout(() => {
            joinedClubs.add(clubId);
            
            // Actualizar botón
            button.classList.add('joined');
            button.innerHTML = '<span class="material-icons">check</span> Miembro';
            button.disabled = false;
            
            // Mostrar notificación de bienvenida
            showClubWelcome(clubData);
            
            // Actualizar contador de miembros
            updateMemberCount(clubId, 1);
            
            // Agregar a "Mis Clubes" en sidebar
            addToMyClubs(clubId, clubData);
            
        }, 1500);
    }

    function leaveClub(clubId, button) {
        if (confirm('¿Estás seguro de que quieres salir de este club?')) {
            joinedClubs.delete(clubId);
            
            // Actualizar botón
            button.classList.remove('joined');
            button.innerHTML = '<span class="material-icons">group_add</span> Unirse';
            
            // Actualizar contador de miembros
            updateMemberCount(clubId, -1);
            
            // Remover de "Mis Clubes"
            removeFromMyClubs(clubId);
            
            // Mostrar mensaje de confirmación
            showNotification('Has salido del club exitosamente', 'info');
        }
    }

    function showClubWelcome(clubData) {
        const welcomeModal = document.createElement('div');
        welcomeModal.className = 'welcome-modal';
        welcomeModal.innerHTML = `
            <div class="welcome-content">
                <div class="welcome-header">
                    <span class="material-icons">celebration</span>
                    <h3>¡Bienvenido a ${clubData.name}!</h3>
                </div>
                <p>Ahora tienes acceso a:</p>
                <ul>
                    ${clubData.features.map(feature => `<li><span class="material-icons">check_circle</span> ${feature}</li>`).join('')}
                </ul>
                <div class="welcome-actions">
                    <button class="btn btn-primary" onclick="this.parentElement.parentElement.parentElement.remove()">
                        Explorar Club
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(welcomeModal);
        
        // Auto-remover después de 5 segundos
        setTimeout(() => {
            if (welcomeModal.parentElement) {
                welcomeModal.remove();
            }
        }, 5000);
    }

    function updateMemberCount(clubId, change) {
        const clubCard = document.querySelector(`[data-club-id="${clubId}"]`).closest('.club-card');
        const membersStat = clubCard.querySelector('.stat span:last-child');
        
        if (membersStat) {
            const currentText = membersStat.textContent;
            const currentCount = parseInt(currentText.replace(/[^\d]/g, ''));
            const newCount = currentCount + change;
            
            membersStat.textContent = `${newCount.toLocaleString()} miembros`;
            
            // Animación de cambio
            membersStat.style.color = change > 0 ? '#10b981' : '#ef4444';
            setTimeout(() => {
                membersStat.style.color = '';
            }, 1000);
        }
    }

    function addToMyClubs(clubId, clubData) {
        const myClubsContainer = document.querySelector('.my-clubs');
        
        const clubItem = document.createElement('div');
        clubItem.className = 'my-club-item';
        clubItem.dataset.clubId = clubId;
        clubItem.innerHTML = `
            <img src="/static/assets/agave.webp" alt="${clubData.name}" class="club-mini-avatar">
            <div class="club-mini-info">
                <span class="club-mini-name">${clubData.name}</span>
                <span class="club-mini-members">${clubData.members.toLocaleString()} miembros</span>
            </div>
        `;
        
        myClubsContainer.appendChild(clubItem);
        
        // Animación de entrada
        clubItem.style.opacity = '0';
        clubItem.style.transform = 'translateX(-20px)';
        setTimeout(() => {
            clubItem.style.transition = 'all 0.3s ease';
            clubItem.style.opacity = '1';
            clubItem.style.transform = 'translateX(0)';
        }, 100);
    }

    function removeFromMyClubs(clubId) {
        const clubItem = document.querySelector(`.my-club-item[data-club-id="${clubId}"]`);
        if (clubItem) {
            clubItem.style.transition = 'all 0.3s ease';
            clubItem.style.opacity = '0';
            clubItem.style.transform = 'translateX(-20px)';
            setTimeout(() => {
                clubItem.remove();
            }, 300);
        }
    }

    // Ver club (redirigir a página del club)
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const clubId = this.dataset.clubId;
            const clubData = clubsData[clubId];
            
            // Animación de carga
            this.innerHTML = '<span class="material-icons rotating">hourglass_empty</span> Cargando...';
            
            setTimeout(() => {
                // Aquí se redirigiría a la página del club
                console.log(`Navegando al club: ${clubData.name}`);
                showNotification(`Abriendo ${clubData.name}...`, 'success');
                
                // Restaurar botón
                this.innerHTML = 'Ver Club';
            }, 1000);
        });
    });

    // Crear nuevo club
    createClubBtn.addEventListener('click', function() {
        showCreateClubModal();
    });

    function showCreateClubModal() {
        const modal = document.createElement('div');
        modal.className = 'create-club-modal';
        modal.innerHTML = `
            <div class="modal-overlay">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>Crear Nuevo Club</h3>
                        <button class="close-modal">
                            <span class="material-icons">close</span>
                        </button>
                    </div>
                    <form class="create-club-form">
                        <div class="form-group">
                            <label>Nombre del Club</label>
                            <input type="text" placeholder="Ej: Bonsáis Mexicanos" required>
                        </div>
                        <div class="form-group">
                            <label>Categoría</label>
                            <select required>
                                <option value="">Seleccionar categoría</option>
                                <option value="cactus">Cactus y Suculentas</option>
                                <option value="orquideas">Orquídeas</option>
                                <option value="carnivoras">Plantas Carnívoras</option>
                                <option value="huertos">Huertos Caseros</option>
                                <option value="nativas">Plantas Nativas</option>
                                <option value="interior">Plantas de Interior</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Descripción</label>
                            <textarea placeholder="Describe el propósito y enfoque de tu club..." required></textarea>
                        </div>
                        <div class="form-group">
                            <label>Imagen del Club</label>
                            <input type="file" accept="image/*">
                        </div>
                        <div class="form-actions">
                            <button type="button" class="btn btn-outline cancel-create">Cancelar</button>
                            <button type="submit" class="btn btn-primary">Crear Club</button>
                        </div>
                    </form>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Event listeners del modal
        modal.querySelector('.close-modal').addEventListener('click', () => modal.remove());
        modal.querySelector('.cancel-create').addEventListener('click', () => modal.remove());
        modal.querySelector('.modal-overlay').addEventListener('click', (e) => {
            if (e.target === modal.querySelector('.modal-overlay')) {
                modal.remove();
            }
        });
        
        modal.querySelector('.create-club-form').addEventListener('submit', function(e) {
            e.preventDefault();
            // Aquí se procesaría la creación del club
            showNotification('Club creado exitosamente. Pendiente de aprobación.', 'success');
            modal.remove();
        });
    }

    // Función para mostrar notificaciones
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <span class="material-icons">
                ${type === 'success' ? 'check_circle' : type === 'error' ? 'error' : 'info'}
            </span>
            <span>${message}</span>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    // Animaciones de entrada
    function animateCardsOnLoad() {
        clubCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 150);
        });
    }

    // Inicializar
    animateCardsOnLoad();
    
    // Simular algunos clubes ya unidos
    setTimeout(() => {
        joinedClubs.add('1'); // Simular que ya está en Cactus Lovers
        const button1 = document.querySelector('[data-club-id="1"]');
        if (button1) {
            button1.classList.add('joined');
            button1.innerHTML = '<span class="material-icons">check</span> Miembro';
        }
    }, 1000);
});
