document.addEventListener('DOMContentLoaded', function() {
    // Obtener el ID de la planta de la URL
    const urlParams = new URLSearchParams(window.location.search);
    const plantId = urlParams.get('id');
    
    if (!plantId) {
        // Redirigir a la biblioteca si no hay ID de planta
        window.location.href = 'biblioteca.html';
        return;
    }
    
    // Cargar los datos de la planta
    loadPlantDetails(plantId);
    
    // Configurar los tabs
    setupTabs();
    
    // Configurar la galería de imágenes
    setupImageGallery();
    
    // Configurar los botones de acción
    setupActionButtons();
});

// Función para cargar los detalles de la planta
async function loadPlantDetails(plantId) {
    try {
        // En un entorno real, aquí se haría una petición a la API
        // Por ahora, usamos datos de ejemplo
        const plantData = await fetchPlantData(plantId);
        
        // Actualizar la información principal de la planta
        updatePlantMainInfo(plantData);
        
        // Actualizar los atributos de la planta
        updatePlantAttributes(plantData);
        
        // Actualizar las pestañas con la información detallada
        updatePlantTabs(plantData);
        
        // Cargar plantas relacionadas
        loadRelatedPlants(plantData.related);
        
    } catch (error) {
        console.error('Error al cargar los detalles de la planta:', error);
        showErrorMessage('No se pudo cargar la información de la planta. Por favor, inténtalo de nuevo más tarde.');
    }
}

// Simulación de obtención de datos de la planta
async function fetchPlantData(plantId) {
    // En un entorno real, esto sería una llamada a la API
    // Por ahora, devolvemos datos de ejemplo
    
    // Simulamos un pequeño retraso para imitar una llamada a la API
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return {
        id: plantId,
        name: 'Biznaga Partida',
        scientificName: 'Ferocactus hamatacanthus',
        images: [
            'https://upload.wikimedia.org/wikipedia/commons/thumb/3/35/Ferocactus_hamatacanthus_1.jpg/1200px-Ferocactus_hamatacanthus_1.jpg',
            'https://upload.wikimedia.org/wikipedia/commons/thumb/d/d1/Ferocactus_hamatacanthus_3.jpg/1200px-Ferocactus_hamatacanthus_3.jpg',
            'https://upload.wikimedia.org/wikipedia/commons/thumb/2/2e/Ferocactus_hamatacanthus_4.jpg/1200px-Ferocactus_hamatacanthus_4.jpg',
            'https://upload.wikimedia.org/wikipedia/commons/thumb/5/5a/Ferocactus_hamatacanthus_5.jpg/1200px-Ferocactus_hamatacanthus_5.jpg'
        ],
        category: 'cactus',
        attributes: {
            water: 'Bajo',
            sun: 'Pleno sol',
            temperature: '5°C - 40°C',
            difficulty: 'Fácil'
        },
        description: 'La Biznaga Partida (Ferocactus hamatacanthus) es un cactus nativo del desierto de Chihuahua. Se caracteriza por su forma globular que puede llegar a ser columnar con la edad, y por sus espinas prominentes y coloridas que varían del amarillo al rojo.',
        characteristics: [
            'Forma globular que puede crecer hasta 60 cm de altura',
            'Espinas amarillas a rojizas muy llamativas',
            'Flores amarillas brillantes que aparecen en la corona',
            'Frutos comestibles de color verde a amarillo',
            'Longevidad de hasta 50 años en condiciones óptimas'
        ],
        habitat: 'Crece en zonas desérticas y semidesérticas del norte de México, especialmente en el desierto de Chihuahua. Prefiere suelos bien drenados y pedregosos, y puede encontrarse en laderas y planicies expuestas al sol.',
        care: {
            watering: 'Riego muy escaso. En verano, regar solo cuando el sustrato esté completamente seco (aproximadamente cada 2-3 semanas). En invierno, reducir el riego a una vez al mes o menos, dependiendo de las condiciones.',
            light: 'Requiere pleno sol para desarrollarse adecuadamente y mantener su forma compacta. En interiores, colocar en el lugar más luminoso posible, preferiblemente cerca de una ventana orientada al sur.',
            soil: 'Sustrato específico para cactus, muy bien drenado. Se puede preparar mezclando tierra para macetas con arena gruesa y perlita en proporciones iguales. Es fundamental que no retenga humedad.',
            fertilizer: 'Fertilizar moderadamente durante la temporada de crecimiento (primavera y verano) con un fertilizante específico para cactus, diluido a la mitad de la concentración recomendada. No fertilizar en otoño e invierno.',
            pruning: 'No requiere poda. Solo eliminar partes dañadas o con signos de enfermedad utilizando herramientas esterilizadas.'
        },
        seasons: {
            spring: 'Comienza el período de crecimiento activo. Aumentar gradualmente el riego y comenzar con la fertilización. Es la época ideal para trasplantar si es necesario.',
            summer: 'Período de floración. Mantener el riego regular pero siempre dejando que el sustrato se seque completamente entre riegos. Proteger de sol extremadamente intenso en las horas centrales del día si las temperaturas superan los 40°C.',
            fall: 'Reducir gradualmente el riego y suspender la fertilización. Preparar la planta para el período de reposo invernal.',
            winter: 'Período de reposo. Mantener casi seco y en un lugar fresco (pero sin heladas). La temperatura ideal es entre 5°C y 15°C. Evitar el riego si la temperatura es baja.'
        },
        problems: [
            {
                title: 'Pudrición de raíces',
                description: 'Las raíces se vuelven blandas y oscuras, y la base del cactus puede comenzar a ablandarse y cambiar de color.',
                solution: 'Reducir inmediatamente el riego. Desenterrar la planta, cortar las partes afectadas con un cuchillo esterilizado y dejar cicatrizar el corte durante varios días antes de replantar en sustrato fresco y seco.'
            },
            {
                title: 'Cochinillas',
                description: 'Pequeños insectos blancos algodonosos que aparecen en las areolas y hendiduras del cactus.',
                solution: 'Eliminar manualmente con un bastoncillo de algodón empapado en alcohol. Para infestaciones mayores, aplicar un insecticida específico para cochinillas, siguiendo las instrucciones del fabricante.'
            },
            {
                title: 'Decoloración amarillenta',
                description: 'El cactus adquiere un tono amarillento o blanquecino, especialmente en el lado expuesto al sol.',
                solution: 'Suele ser una quemadura solar por exposición repentina a luz intensa. Mover la planta a un lugar con luz más filtrada y aclimatarla gradualmente al sol directo.'
            }
        ],
        tips: [
            'Rotar el cactus periódicamente para asegurar un crecimiento uniforme',
            'En regiones con inviernos húmedos, es preferible mantener la planta bajo techo para evitar el exceso de humedad',
            'Las espinas son una adaptación para protegerse del sol y los depredadores, no las cortes',
            'Los frutos son comestibles y tienen un sabor dulce, similares a una tuna',
            'Si se cultiva en maceta, trasplantar cada 2-3 años a un contenedor ligeramente más grande'
        ],
        related: [1, 2, 3, 4]
    };
}

// Actualizar la información principal de la planta
function updatePlantMainInfo(plantData) {
    // Actualizar título e imagen principal
    document.getElementById('plant-name').textContent = plantData.name;
    document.getElementById('plant-scientific-name').textContent = plantData.scientificName;
    document.getElementById('main-plant-image').src = plantData.images[0];
    document.getElementById('main-plant-image').alt = plantData.name;
    
    // Actualizar badges
    const categoryBadge = document.querySelector('.category-badge');
    categoryBadge.textContent = plantData.category.charAt(0).toUpperCase() + plantData.category.slice(1);
}

// Actualizar los atributos de la planta
function updatePlantAttributes(plantData) {
    document.getElementById('water-needs').textContent = plantData.attributes.water;
    document.getElementById('sun-needs').textContent = plantData.attributes.sun;
    document.getElementById('temperature-needs').textContent = plantData.attributes.temperature;
    document.getElementById('difficulty-level').textContent = plantData.attributes.difficulty;
}

// Actualizar las pestañas con información detallada
function updatePlantTabs(plantData) {
    // Pestaña de descripción
    document.getElementById('plant-description').textContent = plantData.description;
    
    const characteristicsList = document.getElementById('plant-characteristics');
    characteristicsList.innerHTML = '';
    plantData.characteristics.forEach(characteristic => {
        const li = document.createElement('li');
        li.textContent = characteristic;
        characteristicsList.appendChild(li);
    });
    
    document.getElementById('plant-habitat').textContent = plantData.habitat;
    
    // Pestaña de cuidados
    document.getElementById('watering-instructions').textContent = plantData.care.watering;
    document.getElementById('light-instructions').textContent = plantData.care.light;
    document.getElementById('soil-instructions').textContent = plantData.care.soil;
    document.getElementById('fertilizer-instructions').textContent = plantData.care.fertilizer;
    document.getElementById('pruning-instructions').textContent = plantData.care.pruning;
    
    // Pestaña de temporadas
    document.getElementById('spring-care').textContent = plantData.seasons.spring;
    document.getElementById('summer-care').textContent = plantData.seasons.summer;
    document.getElementById('fall-care').textContent = plantData.seasons.fall;
    document.getElementById('winter-care').textContent = plantData.seasons.winter;
    
    // Pestaña de consejos y problemas
    const problemsList = document.getElementById('common-problems');
    problemsList.innerHTML = '';
    
    const problemTemplate = document.getElementById('problem-card-template');
    plantData.problems.forEach(problem => {
        const problemCard = document.importNode(problemTemplate.content, true);
        
        problemCard.querySelector('.problem-title').textContent = problem.title;
        problemCard.querySelector('.problem-description').textContent = problem.description;
        problemCard.querySelector('.solution-text').textContent = problem.solution;
        
        problemsList.appendChild(problemCard);
    });
    
    const tipsList = document.getElementById('expert-tips');
    tipsList.innerHTML = '';
    plantData.tips.forEach(tip => {
        const li = document.createElement('li');
        li.textContent = tip;
        tipsList.appendChild(li);
    });
}

// Configurar los tabs
function setupTabs() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Desactivar todos los tabs
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Activar el tab seleccionado
            button.classList.add('active');
            const tabId = button.getAttribute('data-tab');
            document.getElementById(`${tabId}-tab`).classList.add('active');
        });
    });
}

// Configurar la galería de imágenes
function setupImageGallery() {
    const mainImage = document.getElementById('main-plant-image');
    const thumbnailGallery = document.querySelector('.thumbnail-gallery');
    const thumbnailTemplate = document.getElementById('thumbnail-template');
    
    // Esta función se llamará cuando tengamos los datos de la planta
    window.updateGallery = function(images) {
        thumbnailGallery.innerHTML = '';
        
        images.forEach((imageUrl, index) => {
            const thumbnail = document.importNode(thumbnailTemplate.content, true);
            const img = thumbnail.querySelector('img');
            
            img.src = imageUrl;
            img.alt = `Imagen ${index + 1}`;
            
            const thumbnailDiv = thumbnail.querySelector('.thumbnail');
            if (index === 0) {
                thumbnailDiv.classList.add('active');
            }
            
            thumbnailDiv.addEventListener('click', () => {
                // Actualizar imagen principal
                mainImage.src = imageUrl;
                mainImage.alt = `Imagen ${index + 1}`;
                
                // Actualizar clase activa
                document.querySelectorAll('.thumbnail').forEach(thumb => {
                    thumb.classList.remove('active');
                });
                thumbnailDiv.classList.add('active');
            });
            
            thumbnailGallery.appendChild(thumbnail);
        });
    };
}

// Cargar plantas relacionadas
async function loadRelatedPlants(relatedIds) {
    try {
        // En un entorno real, aquí se haría una petición a la API
        // Por ahora, usamos datos de ejemplo
        const relatedPlants = await fetchRelatedPlants(relatedIds);
        
        const relatedPlantsGrid = document.getElementById('related-plants-grid');
        relatedPlantsGrid.innerHTML = '';
        
        const relatedPlantTemplate = document.getElementById('related-plant-template');
        
        relatedPlants.forEach(plant => {
            const plantCard = document.importNode(relatedPlantTemplate.content, true);
            
            plantCard.querySelector('.related-plant-name').textContent = plant.name;
            plantCard.querySelector('.related-plant-scientific-name').textContent = plant.scientificName;
            plantCard.querySelector('img').src = plant.image;
            plantCard.querySelector('img').alt = plant.name;
            
            const cardElement = plantCard.querySelector('.related-plant-card');
            cardElement.addEventListener('click', () => {
                window.location.href = `detalle.html?id=${plant.id}`;
            });
            
            relatedPlantsGrid.appendChild(plantCard);
        });
        
    } catch (error) {
        console.error('Error al cargar plantas relacionadas:', error);
    }
}

// Simulación de obtención de plantas relacionadas
async function fetchRelatedPlants(relatedIds) {
    // En un entorno real, esto sería una llamada a la API
    // Por ahora, devolvemos datos de ejemplo
    
    // Simulamos un pequeño retraso para imitar una llamada a la API
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const relatedPlantsData = [
        {
            id: 1,
            name: 'Nopal',
            scientificName: 'Opuntia ficus-indica',
            image: 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/57/Opuntia_ficus-indica_fruit9.jpg/1200px-Opuntia_ficus-indica_fruit9.jpg'
        },
        {
            id: 2,
            name: 'Órgano',
            scientificName: 'Pachycereus marginatus',
            image: 'https://upload.wikimedia.org/wikipedia/commons/thumb/a/a7/Pachycereus_marginatus_1.jpg/1200px-Pachycereus_marginatus_1.jpg'
        },
        {
            id: 3,
            name: 'Alicoche',
            scientificName: 'Echinocereus pectinatus',
            image: 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/0b/Echinocereus_pectinatus_1.jpg/1200px-Echinocereus_pectinatus_1.jpg'
        },
        {
            id: 4,
            name: 'Cholla',
            scientificName: 'Cylindropuntia imbricata',
            image: 'https://upload.wikimedia.org/wikipedia/commons/thumb/2/21/Cylindropuntia_imbricata_4.jpg/1200px-Cylindropuntia_imbricata_4.jpg'
        }
    ];
    
    return relatedPlantsData.filter(plant => relatedIds.includes(plant.id));
}

// Configurar los botones de acción
function setupActionButtons() {
    // Botón de añadir a mi jardín
    document.getElementById('add-to-garden').addEventListener('click', function() {
        showMessage('Planta añadida a tu jardín');
    });
    
    // Botón de programar recordatorio
    document.getElementById('add-to-calendar').addEventListener('click', function() {
        window.location.href = '/calendario?action=add&plant=' + encodeURIComponent(document.getElementById('plant-name').textContent);
    });
    
    // Botón de compartir
    document.getElementById('share-plant').addEventListener('click', function() {
        if (navigator.share) {
            navigator.share({
                title: document.getElementById('plant-name').textContent,
                text: 'Mira esta planta en PlantCare: ' + document.getElementById('plant-name').textContent,
                url: window.location.href
            })
            .catch(error => console.log('Error al compartir:', error));
        } else {
            // Fallback para navegadores que no soportan Web Share API
            prompt('Copia este enlace para compartir:', window.location.href);
        }
    });
}

// Función para mostrar mensajes
function showMessage(message) {
    // Crear elemento de mensaje
    const messageElement = document.createElement('div');
    messageElement.className = 'message-toast';
    messageElement.textContent = message;
    
    // Añadir al DOM
    document.body.appendChild(messageElement);
    
    // Mostrar mensaje
    setTimeout(() => {
        messageElement.classList.add('show');
    }, 10);
    
    // Ocultar y eliminar después de 3 segundos
    setTimeout(() => {
        messageElement.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(messageElement);
        }, 300);
    }, 3000);
}

// Función para mostrar mensajes de error
function showErrorMessage(message) {
    // Crear elemento de mensaje
    const messageElement = document.createElement('div');
    messageElement.className = 'message-toast error';
    messageElement.textContent = message;
    
    // Añadir al DOM
    document.body.appendChild(messageElement);
    
    // Mostrar mensaje
    setTimeout(() => {
        messageElement.classList.add('show');
    }, 10);
    
    // Ocultar y eliminar después de 5 segundos
    setTimeout(() => {
        messageElement.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(messageElement);
        }, 300);
    }, 5000);
}

// Cuando se carguen los datos de la planta, actualizar la galería
document.addEventListener('DOMContentLoaded', async function() {
    const urlParams = new URLSearchParams(window.location.search);
    const plantId = urlParams.get('id');
    
    if (plantId) {
        const plantData = await fetchPlantData(plantId);
        if (window.updateGallery) {
            window.updateGallery(plantData.images);
        }
    }
});


