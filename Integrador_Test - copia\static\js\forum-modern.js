// JavaScript para foro moderno con funcionalidades de imágenes
document.addEventListener('DOMContentLoaded', function() {
    // Variables globales
    let selectedImages = [];
    let currentImageIndex = 0;
    let allImages = [];

    // Elementos del DOM
    const postContent = document.getElementById('post-content');
    const publishBtn = document.getElementById('publish-btn');
    const addImageBtn = document.getElementById('add-image-btn');
    const imageUploadArea = document.getElementById('image-upload-area');
    const imageInput = document.getElementById('image-input');
    const uploadZone = document.getElementById('upload-zone');
    const imagePreviewContainer = document.getElementById('image-preview-container');
    const imageModal = document.getElementById('image-modal');
    const modalImage = document.getElementById('modal-image');
    const closeModal = document.querySelector('.close-modal');
    const prevImageBtn = document.getElementById('prev-image');
    const nextImageBtn = document.getElementById('next-image');

    // Habilitar/deshabilitar botón de publicar
    function togglePublishButton() {
        const hasContent = postContent.value.trim().length > 0;
        publishBtn.disabled = !hasContent;
        publishBtn.style.opacity = hasContent ? '1' : '0.5';
    }

    // Event listeners para el textarea
    postContent.addEventListener('input', togglePublishButton);
    postContent.addEventListener('focus', function() {
        this.style.minHeight = '120px';
    });

    // Mostrar/ocultar área de carga de imágenes
    addImageBtn.addEventListener('click', function() {
        const isVisible = imageUploadArea.style.display !== 'none';
        imageUploadArea.style.display = isVisible ? 'none' : 'block';
        
        if (!isVisible) {
            addImageBtn.style.background = '#e5f3f0';
            addImageBtn.style.color = '#059669';
        } else {
            addImageBtn.style.background = '';
            addImageBtn.style.color = '';
        }
    });

    // Manejar clic en zona de carga
    uploadZone.addEventListener('click', function() {
        imageInput.click();
    });

    // Manejar drag and drop
    uploadZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.style.borderColor = '#059669';
        this.style.background = 'rgba(5, 150, 105, 0.1)';
    });

    uploadZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.style.borderColor = '#d1d5db';
        this.style.background = '';
    });

    uploadZone.addEventListener('drop', function(e) {
        e.preventDefault();
        this.style.borderColor = '#d1d5db';
        this.style.background = '';
        
        const files = Array.from(e.dataTransfer.files);
        handleImageFiles(files);
    });

    // Manejar selección de archivos
    imageInput.addEventListener('change', function(e) {
        const files = Array.from(e.target.files);
        handleImageFiles(files);
    });

    // Procesar archivos de imagen
    function handleImageFiles(files) {
        const imageFiles = files.filter(file => file.type.startsWith('image/'));
        
        if (selectedImages.length + imageFiles.length > 5) {
            alert('Máximo 5 imágenes permitidas');
            return;
        }

        imageFiles.forEach(file => {
            if (file.size > 10 * 1024 * 1024) { // 10MB
                alert(`La imagen ${file.name} es demasiado grande (máximo 10MB)`);
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                const imageData = {
                    file: file,
                    url: e.target.result,
                    id: Date.now() + Math.random()
                };
                
                selectedImages.push(imageData);
                displayImagePreview(imageData);
            };
            reader.readAsDataURL(file);
        });
    }

    // Mostrar preview de imagen
    function displayImagePreview(imageData) {
        const previewDiv = document.createElement('div');
        previewDiv.className = 'preview-image';
        previewDiv.dataset.imageId = imageData.id;

        previewDiv.innerHTML = `
            <img src="${imageData.url}" alt="Preview">
            <button class="remove-image" onclick="removeImage(${imageData.id})">
                <span class="material-icons">close</span>
            </button>
        `;

        imagePreviewContainer.appendChild(previewDiv);
    }

    // Remover imagen (función global)
    window.removeImage = function(imageId) {
        selectedImages = selectedImages.filter(img => img.id !== imageId);
        const previewElement = document.querySelector(`[data-image-id="${imageId}"]`);
        if (previewElement) {
            previewElement.remove();
        }
    };

    // Funcionalidad de likes
    document.addEventListener('click', function(e) {
        if (e.target.closest('.like-btn')) {
            const likeBtn = e.target.closest('.like-btn');
            const icon = likeBtn.querySelector('.material-icons');
            const count = likeBtn.querySelector('.count');
            const isLiked = likeBtn.classList.contains('liked');
            
            if (isLiked) {
                likeBtn.classList.remove('liked');
                icon.textContent = 'favorite_border';
                count.textContent = parseInt(count.textContent) - 1;
            } else {
                likeBtn.classList.add('liked');
                icon.textContent = 'favorite';
                count.textContent = parseInt(count.textContent) + 1;
                
                // Animación de like
                likeBtn.style.transform = 'scale(1.2)';
                setTimeout(() => {
                    likeBtn.style.transform = 'scale(1)';
                }, 200);
            }
        }
    });

    // Funcionalidad de guardar
    document.addEventListener('click', function(e) {
        if (e.target.closest('.save-btn')) {
            const saveBtn = e.target.closest('.save-btn');
            const icon = saveBtn.querySelector('.material-icons');
            const isSaved = saveBtn.classList.contains('saved');
            
            if (isSaved) {
                saveBtn.classList.remove('saved');
                icon.textContent = 'bookmark_border';
            } else {
                saveBtn.classList.add('saved');
                icon.textContent = 'bookmark';
            }
        }
    });

    // Mostrar/ocultar comentarios
    document.addEventListener('click', function(e) {
        if (e.target.closest('.comment-btn')) {
            const commentBtn = e.target.closest('.comment-btn');
            const postId = commentBtn.dataset.postId;
            const commentsSection = document.getElementById(`comments-${postId}`);
            
            if (commentsSection) {
                const isVisible = commentsSection.style.display !== 'none';
                commentsSection.style.display = isVisible ? 'none' : 'block';
            }
        }
    });

    // Abrir modal de imagen
    window.openImageModal = function(imgElement) {
        const postCard = imgElement.closest('.post-card');
        allImages = Array.from(postCard.querySelectorAll('.post-image'));
        currentImageIndex = allImages.indexOf(imgElement);
        
        modalImage.src = imgElement.src;
        imageModal.style.display = 'block';
        document.body.style.overflow = 'hidden';
        
        // Actualizar botones de navegación
        updateNavigationButtons();
    };

    // Cerrar modal
    function closeImageModal() {
        imageModal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    closeModal.addEventListener('click', closeImageModal);
    imageModal.addEventListener('click', function(e) {
        if (e.target === imageModal) {
            closeImageModal();
        }
    });

    // Navegación de imágenes en modal
    function updateNavigationButtons() {
        prevImageBtn.style.display = allImages.length > 1 ? 'flex' : 'none';
        nextImageBtn.style.display = allImages.length > 1 ? 'flex' : 'none';
    }

    prevImageBtn.addEventListener('click', function() {
        if (currentImageIndex > 0) {
            currentImageIndex--;
        } else {
            currentImageIndex = allImages.length - 1;
        }
        modalImage.src = allImages[currentImageIndex].src;
    });

    nextImageBtn.addEventListener('click', function() {
        if (currentImageIndex < allImages.length - 1) {
            currentImageIndex++;
        } else {
            currentImageIndex = 0;
        }
        modalImage.src = allImages[currentImageIndex].src;
    });

    // Navegación con teclado
    document.addEventListener('keydown', function(e) {
        if (imageModal.style.display === 'block') {
            if (e.key === 'Escape') {
                closeImageModal();
            } else if (e.key === 'ArrowLeft') {
                prevImageBtn.click();
            } else if (e.key === 'ArrowRight') {
                nextImageBtn.click();
            }
        }
    });

    // Filtros de publicaciones
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('filter-tab')) {
            // Remover clase active de todos los tabs
            document.querySelectorAll('.filter-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Agregar clase active al tab clickeado
            e.target.classList.add('active');
            
            // Aquí se podría implementar la lógica de filtrado
            const filter = e.target.dataset.filter;
            console.log('Filtro seleccionado:', filter);
        }
    });

    // Enviar comentario
    document.addEventListener('click', function(e) {
        if (e.target.closest('.send-comment')) {
            const sendBtn = e.target.closest('.send-comment');
            const input = sendBtn.parentElement.querySelector('input');
            const commentText = input.value.trim();
            
            if (commentText) {
                // Aquí se enviaría el comentario al servidor
                console.log('Enviando comentario:', commentText);
                input.value = '';
                
                // Mostrar feedback visual
                sendBtn.style.background = '#10b981';
                setTimeout(() => {
                    sendBtn.style.background = '#059669';
                }, 300);
            }
        }
    });

    // Publicar nueva publicación
    publishBtn.addEventListener('click', function() {
        const content = postContent.value.trim();
        const category = document.getElementById('post-category').value;
        
        if (content) {
            // Aquí se enviaría la publicación al servidor
            console.log('Publicando:', {
                content: content,
                category: category,
                images: selectedImages
            });
            
            // Limpiar formulario
            postContent.value = '';
            selectedImages = [];
            imagePreviewContainer.innerHTML = '';
            imageUploadArea.style.display = 'none';
            addImageBtn.style.background = '';
            addImageBtn.style.color = '';
            togglePublishButton();
            
            // Mostrar feedback
            publishBtn.textContent = '¡Publicado!';
            publishBtn.style.background = '#10b981';
            setTimeout(() => {
                publishBtn.innerHTML = '<span class="material-icons">send</span> Publicar';
                publishBtn.style.background = '';
            }, 2000);
        }
    });

    // Inicializar
    togglePublishButton();
});
