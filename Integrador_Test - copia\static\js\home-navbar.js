// JavaScript para navegación atractiva del inicio
document.addEventListener('DOMContentLoaded', function() {
    const homeNavbar = document.querySelector('.home-navbar');
    const homeNavLinks = document.querySelectorAll('.home-nav-link');
    
    // Efecto de scroll para la navegación
    function handleScroll() {
        if (window.scrollY > 50) {
            homeNavbar.classList.add('scrolled');
        } else {
            homeNavbar.classList.remove('scrolled');
        }
    }
    
    // Agregar listener de scroll
    window.addEventListener('scroll', handleScroll);
    
    // Efecto de hover mejorado para los enlaces
    homeNavLinks.forEach(link => {
        link.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.home-nav-icon');
            if (icon) {
                // Agregar animación de rotación sutil
                icon.style.transform = 'scale(1.1) rotate(5deg)';
            }
        });
        
        link.addEventListener('mouseleave', function() {
            const icon = this.querySelector('.home-nav-icon');
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0deg)';
            }
        });
    });
    
    // Efecto de click con ondas
    homeNavLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Crear efecto de onda
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(5, 150, 105, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s ease-out;
                pointer-events: none;
                z-index: 1;
            `;
            
            this.style.position = 'relative';
            this.appendChild(ripple);
            
            // Remover el efecto después de la animación
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
    
    // Agregar animación de entrada
    setTimeout(() => {
        homeNavbar.style.transform = 'translateY(0)';
        homeNavbar.style.opacity = '1';
    }, 100);
    
    // Efecto parallax sutil en el logo
    const homeLogo = document.querySelector('.home-logo');
    if (homeLogo) {
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.1;
            homeLogo.style.transform = `translateY(${rate}px)`;
        });
    }
    
    // Animación de carga para los elementos de navegación
    const navItems = document.querySelectorAll('.home-nav-item');
    navItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(-20px)';
        
        setTimeout(() => {
            item.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, 200 + (index * 100));
    });
    
    // Animación de carga para los botones
    const authButtons = document.querySelectorAll('.home-btn');
    authButtons.forEach((button, index) => {
        button.style.opacity = '0';
        button.style.transform = 'translateX(20px)';
        
        setTimeout(() => {
            button.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            button.style.opacity = '1';
            button.style.transform = 'translateX(0)';
        }, 600 + (index * 150));
    });
});

// Agregar estilos CSS para las animaciones
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(2);
            opacity: 0;
        }
    }
    
    .home-navbar {
        transform: translateY(-100%);
        opacity: 0;
        transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    .home-nav-icon {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }
    
    .home-logo-icon {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }
`;
document.head.appendChild(style);
