// Código JavaScript para el home minimalista y dinámico
document.addEventListener('DOMContentLoaded', function() {
    // Elementos flotantes en el hero
    createFloatingElements();

    // Navegación sticky al hacer scroll
    initStickyNavigation();

    // Animaciones al hacer scroll
    initScrollReveal();

    // Carrusel de plantas populares
    initCarousel();

    // Menú móvil
    initMobileMenu();

    // Menú de usuario
    initUserMenu();
});

// Crear elementos flotantes en el hero para efecto dinámico
function createFloatingElements() {
    const heroSection = document.querySelector('.hero-section');
    if (!heroSection) return;

    const floatingElements = document.createElement('div');
    floatingElements.className = 'floating-elements';

    // Crear 3 elementos flotantes
    for (let i = 1; i <= 3; i++) {
        const floatItem = document.createElement('div');
        floatItem.className = `float-item float-${i}`;
        floatingElements.appendChild(floatItem);
    }

    heroSection.appendChild(floatingElements);
}

// Navegación sticky al hacer scroll
function initStickyNavigation() {
    const nav = document.querySelector('.main-nav');
    if (!nav) return;

    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            nav.classList.add('scrolled');
        } else {
            nav.classList.remove('scrolled');
        }
    });
}

// Animaciones al hacer scroll
function initScrollReveal() {
    // Agregar clase scroll-reveal a los elementos que deben animarse al hacer scroll
    const elementsToReveal = document.querySelectorAll('.feature-card, .plant-card, .section-title');
    elementsToReveal.forEach((element, index) => {
        element.classList.add('scroll-reveal');
        element.classList.add(`fade-in-delay-${index % 4 + 1}`);
    });

    // Detector de scroll para animar los elementos
    const revealOnScroll = function() {
        const reveals = document.querySelectorAll('.scroll-reveal');

        reveals.forEach(element => {
            const elementTop = element.getBoundingClientRect().top;
            const windowHeight = window.innerHeight;

            if (elementTop < windowHeight - 100) {
                element.classList.add('revealed');
            }
        });
    };

    // Ejecutar al cargar la página y al hacer scroll
    revealOnScroll();
    window.addEventListener('scroll', revealOnScroll);
}

// Carrusel de plantas populares
function initCarousel() {
    const carousel = document.querySelector('.plants-carousel');
    const dots = document.querySelectorAll('.dot');
    const prevBtn = document.getElementById('prev-plant');
    const nextBtn = document.getElementById('next-plant');

    if (!carousel || !dots.length || !prevBtn || !nextBtn) return;

    let currentSlide = 0;
    const slides = document.querySelectorAll('.plant-card');
    const totalSlides = slides.length;

    // Configuración del carrusel para dispositivos móviles
    function updateCarousel() {
        // En móviles, mostrar un elemento a la vez
        if (window.innerWidth < 768) {
            slides.forEach((slide, index) => {
                slide.style.display = index === currentSlide ? 'block' : 'none';
            });

            dots.forEach((dot, index) => {
                dot.classList.toggle('active', index === currentSlide);
            });
        } else {
            // En desktop, mostrar todos
            slides.forEach(slide => {
                slide.style.display = 'block';
            });
        }
    }

    // Eventos para los botones
    prevBtn.addEventListener('click', () => {
        currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
        updateCarousel();
    });

    nextBtn.addEventListener('click', () => {
        currentSlide = (currentSlide + 1) % totalSlides;
        updateCarousel();
    });

    // Permitir selección directa con los dots
    dots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
            currentSlide = index;
            updateCarousel();
        });
    });

    // Inicializar carrusel
    updateCarousel();

    // Actualizar al cambiar el tamaño de la ventana
    window.addEventListener('resize', updateCarousel);
}

// Menú móvil
function initMobileMenu() {
    const menuBtn = document.createElement('button');
    menuBtn.className = 'mobile-menu-btn icon-button';
    menuBtn.innerHTML = '<span class="material-icons">menu</span>';

    const navLinks = document.querySelector('.nav-links');
    if (!navLinks) return;

    // Configuración para móviles
    if (window.innerWidth <= 576) {
        const navContainer = document.querySelector('.main-nav .container');
        if (navContainer) {
            navContainer.appendChild(menuBtn);

            menuBtn.addEventListener('click', () => {
                navLinks.classList.toggle('mobile-visible');
                menuBtn.innerHTML = navLinks.classList.contains('mobile-visible')
                    ? '<span class="material-icons">close</span>'
                    : '<span class="material-icons">menu</span>';
            });
        }
    }

    // Actualizar al cambiar el tamaño de la ventana
    window.addEventListener('resize', () => {
        if (window.innerWidth <= 576) {
            if (!document.querySelector('.mobile-menu-btn')) {
                const navContainer = document.querySelector('.main-nav .container');
                if (navContainer) {
                    navContainer.appendChild(menuBtn);
                }
            }
        } else {
            const mobileBtn = document.querySelector('.mobile-menu-btn');
            if (mobileBtn) {
                mobileBtn.remove();
            }
            navLinks.classList.remove('mobile-visible');
        }
    });
}

// Inicializar menú de usuario
function initUserMenu() {
    const userMenuButton = document.getElementById('user-menu-button');
    const userDropdown = document.getElementById('user-dropdown');

    if (userMenuButton && userDropdown) {
        // Agregar efecto de ripple al botón de usuario
        userMenuButton.addEventListener('click', function(e) {
            // Alternar clase active en el dropdown
            userDropdown.classList.toggle('active');

            // Efecto visual en el avatar
            this.classList.add('clicked');
            setTimeout(() => {
                this.classList.remove('clicked');
            }, 300);

            e.stopPropagation(); // Evitar que el clic se propague al documento
        });

        // Cerrar menú al hacer clic fuera
        document.addEventListener('click', function(event) {
            if (!userMenuButton.contains(event.target) && !userDropdown.contains(event.target)) {
                userDropdown.classList.remove('active');
            }
        });

        // Agregar efectos a los elementos del menú
        const menuItems = userDropdown.querySelectorAll('a');
        menuItems.forEach(item => {
            item.addEventListener('mouseenter', function() {
                // Agregar efecto de hover
                this.querySelector('.material-icons')?.classList.add('icon-hover');
            });

            item.addEventListener('mouseleave', function() {
                // Quitar efecto de hover
                this.querySelector('.material-icons')?.classList.remove('icon-hover');
            });
        });
    }

    // Botón de cerrar sesión
    const logoutButton = document.getElementById('logout-button');

    if (logoutButton) {
        logoutButton.addEventListener('click', function(e) {
            e.preventDefault();

            if (confirm('¿Estás seguro de que deseas cerrar sesión?')) {
                window.location.href = logoutButton.getAttribute('href');
            }
        });
    }
}

// Efecto para tarjetas hover 3D
document.addEventListener('mousemove', function(e) {
    const cards = document.querySelectorAll('.feature-card, .plant-card');

    cards.forEach(card => {
        // Verificar si el mouse está sobre la tarjeta
        const rect = card.getBoundingClientRect();
        if (
            e.clientX >= rect.left &&
            e.clientX <= rect.right &&
            e.clientY >= rect.top &&
            e.clientY <= rect.bottom
        ) {
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // Calcular rotación basada en la posición del mouse
            const xRotation = ((y - rect.height / 2) / rect.height) * 10; // Max 10 grados
            const yRotation = ((rect.width / 2 - x) / rect.width) * 10; // Max 10 grados

            // Aplicar transformación
            card.style.transform = `perspective(1000px) rotateX(${xRotation}deg) rotateY(${yRotation}deg) scale(1.05)`;
        } else {
            // Restaurar cuando el mouse no está sobre la tarjeta
            card.style.transform = '';
        }
    });
});