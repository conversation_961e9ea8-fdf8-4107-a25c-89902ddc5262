/**
 * Este archivo simula la carga de imágenes para la aplicación PlantCare
 * En una aplicación real, estas imágenes se cargarían desde un servidor
 */

// Función para generar un color aleatorio en formato hexadecimal
function getRandomColor() {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
        color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
}

// Función para generar un avatar de color con iniciales
function generateAvatar(text, size = 200) {
    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    const context = canvas.getContext('2d');
    
    // Dibujar fondo
    context.fillStyle = getRandomColor();
    context.fillRect(0, 0, canvas.width, canvas.height);
    
    // Dibujar texto
    context.font = `bold ${size / 2}px Arial`;
    context.fillStyle = 'white';
    context.textAlign = 'center';
    context.textBaseline = 'middle';
    context.fillText(text, size / 2, size / 2);
    
    return canvas.toDataURL('image/png');
}

// Función para generar una imagen de planta simulada
function generatePlantImage(name, size = 200) {
    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    const context = canvas.getContext('2d');
    
    // Dibujar fondo
    const gradient = context.createLinearGradient(0, 0, 0, size);
    gradient.addColorStop(0, '#e8f5e9');
    gradient.addColorStop(1, '#c8e6c9');
    context.fillStyle = gradient;
    context.fillRect(0, 0, canvas.width, canvas.height);
    
    // Dibujar maceta
    context.fillStyle = '#795548';
    context.fillRect(size / 4, size * 0.7, size / 2, size * 0.3);
    
    // Dibujar planta (forma simplificada)
    context.fillStyle = '#4CAF50';
    context.beginPath();
    context.moveTo(size / 2, size * 0.2);
    context.bezierCurveTo(size * 0.3, size * 0.5, size * 0.7, size * 0.5, size / 2, size * 0.7);
    context.fill();
    
    // Dibujar texto
    context.font = `bold ${size / 10}px Arial`;
    context.fillStyle = 'white';
    context.textAlign = 'center';
    context.textBaseline = 'middle';
    context.fillText(name, size / 2, size * 0.85);
    
    return canvas.toDataURL('image/png');
}

// Cargar avatares predeterminados cuando se cargue la página
document.addEventListener('DOMContentLoaded', function() {
    // Generar avatares predeterminados
    const defaultAvatars = [
        { initial: 'A', color: '#4CAF50' },
        { initial: 'B', color: '#2196F3' },
        { initial: 'C', color: '#FFC107' },
        { initial: 'D', color: '#9C27B0' },
        { initial: 'E', color: '#F44336' },
        { initial: 'F', color: '#009688' },
        { initial: 'G', color: '#673AB7' },
        { initial: 'H', color: '#FF5722' },
        { initial: 'I', color: '#795548' },
        { initial: 'J', color: '#607D8B' }
    ];
    
    // Obtener elementos de avatar
    const avatarOptions = document.querySelectorAll('.avatar-option');
    const avatarPreview = document.getElementById('avatar-preview-img');
    const profileAvatars = document.querySelectorAll('.profile-avatar img, .avatar img');
    
    // Asignar imágenes a los avatares
    avatarOptions.forEach((option, index) => {
        if (index < defaultAvatars.length) {
            const avatar = defaultAvatars[index];
            const avatarImg = generateAvatar(avatar.initial);
            
            // Crear imagen y asignarla al elemento
            const img = option.querySelector('img') || document.createElement('img');
            img.src = avatarImg;
            
            if (!option.querySelector('img')) {
                option.appendChild(img);
            }
            
            // Asignar atributo data-avatar
            option.dataset.avatar = avatarImg;
            
            // Si es el primer avatar, asignarlo como avatar predeterminado
            if (index === 0 && avatarPreview) {
                avatarPreview.src = avatarImg;
                
                // Asignar a todos los avatares de perfil
                profileAvatars.forEach(img => {
                    img.src = avatarImg;
                });
            }
        }
    });
    
    // Generar imágenes de plantas
    const plantImages = document.querySelectorAll('.plant-card-img img, #plant-detail-img');
    const plantNames = ['Agave', 'Nopal', 'Gobernadora', 'Biznaga', 'Yuca'];
    
    plantImages.forEach((img, index) => {
        const plantName = plantNames[index % plantNames.length];
        img.src = generatePlantImage(plantName);
        
        // Si la imagen tiene un atributo alt vacío, asignarle el nombre de la planta
        if (!img.alt) {
            img.alt = plantName;
        }
    });
});
