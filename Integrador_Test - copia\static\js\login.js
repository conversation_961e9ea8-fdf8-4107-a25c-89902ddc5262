document.addEventListener('DOMContentLoaded', function() {
    // Inicializar componentes
    initToggleContainer();
    initPasswordValidation();
//    initFormHandlers();
    initFlashMessages();
    initParticleBackground();
    addMobileToggleLinks();
    initInteractiveFlower();
});

// Toggle entre Inicio de Sesión y Registro
function initToggleContainer() {
    const container = document.getElementById('container');
    const registerBtn = document.getElementById('register');
    const loginBtn = document.getElementById('login');

    if (registerBtn && loginBtn && container) {
        // Animación para el botón de registro
        registerBtn.addEventListener('click', () => {
            container.classList.add('active');

            // Efecto de partículas al cambiar
            createParticleEffect(registerBtn, '#81C784');
        });

        // Animación para el botón de login
        loginBtn.addEventListener('click', () => {
            container.classList.remove('active');

            // Efecto de partículas al cambiar
            createParticleEffect(loginBtn, '#81C784');
        });
    }
}

// Validación de contraseñas
function initPasswordValidation() {
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');
    const passwordValidation = document.querySelector('.password-validation');
    const confirmValidation = document.querySelector('.confirm-validation');
    const strengthMeter = document.querySelector('.password-strength-meter');

    if (!password || !confirmPassword) return;

    // Ocultar mensajes de validación inicialmente
    if (passwordValidation) passwordValidation.style.display = 'none';
    if (confirmValidation) confirmValidation.style.display = 'none';

    // Validación de fortaleza de contraseña
    password.addEventListener('input', function() {
        const value = this.value;

        // Verificar requisitos
        const hasMinLength = value.length >= 8;
        const hasNumbers = /[0-9]/.test(value);
        const hasLetters = /[a-zA-Z]/.test(value);

        // Determinar fortaleza
        let strength = '';

        if (value.length === 0) {
            strength = '';
        } else if (hasMinLength && hasNumbers && hasLetters) {
            strength = 'strong';
        } else if (hasMinLength && (hasNumbers || hasLetters)) {
            strength = 'medium';
        } else {
            strength = 'weak';
        }

        // Actualizar indicador visual
        if (strengthMeter) {
            strengthMeter.className = 'password-strength-meter ' + strength;
        }

        // Mostrar/ocultar mensaje de validación
        if (passwordValidation) {
            passwordValidation.style.display = value.length > 0 && (!hasMinLength || !hasNumbers || !hasLetters) ? 'flex' : 'none';
        }

        // Validar confirmación si ya tiene valor
        if (confirmPassword.value.length > 0) {
            validateConfirmPassword();
        }
    });

    // Validar coincidencia de contraseñas
    function validateConfirmPassword() {
        if (password.value !== confirmPassword.value) {
            if (confirmValidation) confirmValidation.style.display = 'flex';
            return false;
        } else {
            if (confirmValidation) confirmValidation.style.display = 'none';
            return true;
        }
    }

    // Validar en tiempo real
    if (confirmPassword) {
        confirmPassword.addEventListener('input', validateConfirmPassword);
    }
}

// // Manejo de formularios
// function initFormHandlers() {
//     const registerForm = document.getElementById('register-form');
//     const loginForm = document.getElementById('login-form');

//     if (registerForm) {
//         registerForm.addEventListener('submit', function(event) {
//             event.preventDefault();

//             // Validar contraseñas
//             const password = document.getElementById('password');
//             const confirmPassword = document.getElementById('confirm_password');

//             if (password.value !== confirmPassword.value) {
//                 showNotification('Las contraseñas no coinciden', 'error', 'error');
//                 return;
//             }

//             // Validar fortaleza
//             const hasMinLength = password.value.length >= 8;
//             const hasNumbers = /[0-9]/.test(password.value);
//             const hasLetters = /[a-zA-Z]/.test(password.value);

//             if (!hasMinLength || !hasNumbers || !hasLetters) {
//                 showNotification('La contraseña no cumple con los requisitos mínimos', 'error', 'error');
//                 return;
//             }

//             // Validar términos y condiciones
//             const terms = document.getElementById('terms');
//             if (!terms.checked) {
//                 showNotification('Debes aceptar los términos y condiciones', 'error', 'gavel');
//                 return;
//             }

//             // Mostrar estado de carga en botón
//             const submitButton = registerForm.querySelector('button[type="submit"]');
//             const originalButtonText = submitButton.innerHTML;
//             submitButton.disabled = true;
//             submitButton.innerHTML = '<span class="material-icons spin">refresh</span> Registrando...';

//             // Enviar formulario
//             const formData = new FormData(registerForm);

//             fetch(registerForm.action, {
//                 method: 'POST',
//                 body: formData
//             })
//             .then(response => {
//                 if (response.redirected) {
//                     window.location.href = response.url;
//                 } else {
//                     return response.json();
//                 }
//             })
//             .then(data => {
//                 if (data && data.error) {
//                     showNotification(data.error, 'error', 'error');

//                     // Restaurar botón
//                     submitButton.disabled = false;
//                     submitButton.innerHTML = originalButtonText;
//                 }
//             })
//             .catch(error => {
//                 console.error('Error durante el registro:', error);
//                 showNotification('Error durante el registro. Intenta nuevamente.', 'error', 'error');

//                 // Restaurar botón
//                 submitButton.disabled = false;
//                 submitButton.innerHTML = originalButtonText;
//             });
//         });
//     }

//     if (loginForm) {
//         loginForm.addEventListener('submit', function(event) {
//             event.preventDefault();

//             // Mostrar estado de carga en botón
//             const submitButton = loginForm.querySelector('button[type="submit"]');
//             const originalButtonText = submitButton.innerHTML;
//             submitButton.disabled = true;
//             submitButton.innerHTML = '<span class="material-icons spin">refresh</span> Iniciando sesión...';

//             // Enviar formulario
//             const formData = new FormData(loginForm);

//             fetch(loginForm.action, {
//                 method: 'POST',
//                 body: formData
//             })
//             .then(response => {
//                 if (response.redirected) {
//                     window.location.href = response.url;
//                 } else {
//                     return response.json();
//                 }
//             })
//             .then(data => {
//                 if (data && data.error) {
//                     showNotification(data.error, 'error', 'error');

//                     // Restaurar botón
//                     submitButton.disabled = false;
//                     submitButton.innerHTML = originalButtonText;
//                 }
//             })
//             .catch(error => {
//                 console.error('Error durante el inicio de sesión:', error);
//                 showNotification('Error durante el inicio de sesión. Intenta nuevamente.', 'error', 'error');

//                 // Restaurar botón
//                 submitButton.disabled = false;
//                 submitButton.innerHTML = originalButtonText;
//             });
//         });
//     }
// }

// Gestión de mensajes flash
function initFlashMessages() {
    const flashMessages = document.querySelectorAll('.flash-message');
    const dismissButtons = document.querySelectorAll('.dismiss-btn');

    // Configura cada mensaje flash para auto-eliminarse
    flashMessages.forEach(message => {
        setTimeout(() => {
            message.style.opacity = '0';
            setTimeout(() => {
                message.remove();
            }, 500);
        }, 5000);
    });

    // Configura los botones para cerrar mensajes
    dismissButtons.forEach(button => {
        button.addEventListener('click', function() {
            const message = this.closest('.flash-message');
            message.style.opacity = '0';
            setTimeout(() => {
                message.remove();
            }, 500);
        });
    });
}

// Mostrar notificación
function showNotification(message, type = 'info', icon = null) {
    const notificationContainer = document.getElementById('notification-container');
    if (!notificationContainer) return;

    const notification = document.createElement('div');
    notification.className = `notification ${type}`;

    // Icono por defecto según tipo
    let defaultIcon = 'info';
    if (type === 'success') defaultIcon = 'check_circle';
    if (type === 'error') defaultIcon = 'error';
    if (type === 'warning') defaultIcon = 'warning';

    notification.innerHTML = `
        <span class="material-icons">${icon || defaultIcon}</span>
        <div class="notification-content">${message}</div>
    `;

    notificationContainer.appendChild(notification);

    // Eliminar después de 5 segundos
    setTimeout(() => {
        notification.classList.add('fade-out');
        setTimeout(() => {
            notification.remove();
        }, 500);
    }, 5000);
}

// Efecto de fondo de partículas
function initParticleBackground() {
    // Crear canvas para el fondo
    const canvas = document.createElement('canvas');
    canvas.id = 'particles-canvas';
    canvas.style.position = 'fixed';
    canvas.style.top = '0';
    canvas.style.left = '0';
    canvas.style.width = '100%';
    canvas.style.height = '100%';
    canvas.style.pointerEvents = 'none';
    canvas.style.zIndex = '-1';

    document.body.appendChild(canvas);

    const ctx = canvas.getContext('2d');

    // Ajustar tamaño del canvas
    function resizeCanvas() {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
    }

    // Crear partículas
    const particles = [];
    const particleCount = Math.min(window.innerWidth / 10, 100); // Ajustar cantidad según tamaño de pantalla

    function Particle() {
        this.x = Math.random() * canvas.width;
        this.y = Math.random() * canvas.height;
        this.size = Math.random() * 5 + 1;
        this.speedX = Math.random() * 2 - 1;
        this.speedY = Math.random() * 2 - 1;
        this.color = '#4CAF50';
        this.opacity = Math.random() * 0.2;
    }

    Particle.prototype.update = function() {
        this.x += this.speedX;
        this.y += this.speedY;

        // Rebotar en los bordes
        if (this.x > canvas.width || this.x < 0) {
            this.speedX = -this.speedX;
        }

        if (this.y > canvas.height || this.y < 0) {
            this.speedY = -this.speedY;
        }
    };

    Particle.prototype.draw = function() {
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fillStyle = this.color;
        ctx.globalAlpha = this.opacity;
        ctx.fill();
        ctx.globalAlpha = 1;
    };

    // Inicializar partículas
    function initParticles() {
        for (let i = 0; i < particleCount; i++) {
            particles.push(new Particle());
        }
    }

    // Animar partículas
    function animateParticles() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        for (let i = 0; i < particles.length; i++) {
            particles[i].update();
            particles[i].draw();

            // Conectar partículas cercanas
            connectParticles(particles[i]);
        }

        requestAnimationFrame(animateParticles);
    }

    // Conectar partículas cercanas
    function connectParticles(p) {
        for (let i = 0; i < particles.length; i++) {
            const dx = p.x - particles[i].x;
            const dy = p.y - particles[i].y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance < 100) {
                ctx.beginPath();
                ctx.strokeStyle = '#4CAF50';
                ctx.globalAlpha = 0.1 * (1 - distance / 100);
                ctx.lineWidth = 1;
                ctx.moveTo(p.x, p.y);
                ctx.lineTo(particles[i].x, particles[i].y);
                ctx.stroke();
                ctx.globalAlpha = 1;
            }
        }
    }

    // Inicializar
    window.addEventListener('resize', resizeCanvas);
    resizeCanvas();
    initParticles();
    animateParticles();
}

// Efecto de partículas al hacer clic
function createParticleEffect(element, color) {
    // Crear 20 partículas en la posición del elemento
    const rect = element.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    for (let i = 0; i < 20; i++) {
        createParticle(centerX, centerY, color);
    }
}

function createParticle(x, y, color) {
    const particle = document.createElement('div');
    document.body.appendChild(particle);

    const size = Math.floor(Math.random() * 10 + 5);
    const destinationX = x + (Math.random() - 0.5) * 2 * 100;
    const destinationY = y + (Math.random() - 0.5) * 2 * 100;
    const rotation = Math.random() * 520;
    const delay = Math.random() * 200;

    particle.style.width = `${size}px`;
    particle.style.height = `${size}px`;
    particle.style.background = color;
    particle.style.borderRadius = '50%';
    particle.style.position = 'fixed';
    particle.style.top = `${y}px`;
    particle.style.left = `${x}px`;
    particle.style.zIndex = '9999';
    particle.style.pointerEvents = 'none';
    particle.style.opacity = 0;

    setTimeout(() => {
        particle.style.transition = 'all 1s ease-out';
        particle.style.transform = `translate(${destinationX - x}px, ${destinationY - y}px) rotate(${rotation}deg)`;
        particle.style.opacity = 0;

        setTimeout(() => {
            particle.remove();
        }, 1000);

        requestAnimationFrame(() => {
            particle.style.opacity = 0.8;
        });
    }, delay);
}

// Agregar enlaces de alternancia para móviles
function addMobileToggleLinks() {
    // Verificar si estamos en vista móvil (ancho < 768px)
    if (window.innerWidth < 768) {
        const registerForm = document.querySelector('.sign-up form');
        const loginForm = document.querySelector('.sign-in form');
        const container = document.getElementById('container');

        if (registerForm && loginForm && container) {
            // Agregar enlace "¿Ya tienes cuenta? Inicia sesión" al formulario de registro
            const registerToggle = document.createElement('div');
            registerToggle.className = 'mobile-toggle';
            registerToggle.innerHTML = '¿Ya tienes cuenta? <a href="#" id="mobile-login">Inicia sesión</a>';
            registerForm.appendChild(registerToggle);

            // Agregar enlace "¿No tienes cuenta? Regístrate" al formulario de login
            const loginToggle = document.createElement('div');
            loginToggle.className = 'mobile-toggle';
            loginToggle.innerHTML = '¿No tienes cuenta? <a href="#" id="mobile-register">Regístrate</a>';
            loginForm.appendChild(loginToggle);

            // Agregar funcionalidad a los enlaces
            document.getElementById('mobile-register').addEventListener('click', function(e) {
                e.preventDefault();
                container.classList.add('active');
            });

            document.getElementById('mobile-login').addEventListener('click', function(e) {
                e.preventDefault();
                container.classList.remove('active');
            });
        }
    }

    // Ajustar al cambiar tamaño de ventana
    window.addEventListener('resize', function() {
        // Eliminar enlaces existentes
        const mobileToggles = document.querySelectorAll('.mobile-toggle');
        mobileToggles.forEach(toggle => toggle.remove());

        // Volver a agregar si es necesario
        if (window.innerWidth < 768) {
            addMobileToggleLinks();
        }
    });
}

// Inicializar la flor interactiva
function initInteractiveFlower() {
    const flower = document.getElementById('interactive-flower');
    const weatherEffect = document.getElementById('weather-effect');

    if (!flower || !weatherEffect) return;

    // Emojis para los efectos climáticos
    const weatherEmojis = {
        rain: '🌧️',
        sun: '☀️'
    };

    // Manejar clic en la flor
    flower.addEventListener('click', function() {
        // Evitar múltiples clics mientras la animación está en curso
        if (weatherEffect.classList.contains('active')) return;

        // Probabilidad muy pequeña (1%) de que lluevan gatitos y flores
        const isSpecialEvent = Math.random() < 0.01;

        if (isSpecialEvent) {
            // ¡Sorpresa! Lluvia de gatitos y flores
            createCatFlowerRain();

            // Cambiar la flor temporalmente a un emoji especial
            const originalFlower = flower.querySelector('.flower-emoji').textContent;
            flower.querySelector('.flower-emoji').textContent = '✨';

            // Restaurar la flor después de la animación
            setTimeout(() => {
                flower.querySelector('.flower-emoji').textContent = originalFlower;
            }, 5000);

            return;
        }

        // Comportamiento normal (99% de probabilidad)
        // Determinar aleatoriamente si mostrar lluvia o sol (50/50)
        const isRain = Math.random() < 0.5;
        const emoji = isRain ? weatherEmojis.rain : weatherEmojis.sun;

        // Establecer el emoji y activar la animación
        weatherEffect.textContent = emoji;
        weatherEffect.classList.add('active');

        // Cambiar la flor temporalmente
        const originalFlower = flower.querySelector('.flower-emoji').textContent;
        flower.querySelector('.flower-emoji').textContent = isRain ? '🌱' : '🌻';

        // Restaurar la flor después de la animación
        setTimeout(() => {
            weatherEffect.classList.remove('active');
            setTimeout(() => {
                flower.querySelector('.flower-emoji').textContent = originalFlower;
            }, 500);
        }, 2000);
    });
}

// Función para crear la lluvia de gatitos y flores
function createCatFlowerRain() {
    // Emojis para la lluvia especial
    const specialEmojis = ['🐱', '🐈', '🌸', '🌹', '🌷', '🌺', '🌻', '🌼', '😺', '😸', '😻'];

    // Crear contenedor para la lluvia si no existe
    let rainContainer = document.getElementById('special-rain-container');
    if (!rainContainer) {
        rainContainer = document.createElement('div');
        rainContainer.id = 'special-rain-container';
        rainContainer.style.position = 'fixed';
        rainContainer.style.top = '0';
        rainContainer.style.left = '0';
        rainContainer.style.width = '100%';
        rainContainer.style.height = '100%';
        rainContainer.style.pointerEvents = 'none';
        rainContainer.style.zIndex = '9999';
        rainContainer.style.overflow = 'hidden';
        document.body.appendChild(rainContainer);
    }

    // Crear 100 elementos de lluvia
    for (let i = 0; i < 100; i++) {
        setTimeout(() => {
            // Crear un elemento para cada emoji
            const rainDrop = document.createElement('div');
            rainDrop.className = 'special-rain-drop';

            // Seleccionar un emoji aleatorio
            const randomEmoji = specialEmojis[Math.floor(Math.random() * specialEmojis.length)];
            rainDrop.textContent = randomEmoji;

            // Posición inicial aleatoria (solo en el eje X)
            const posX = Math.random() * window.innerWidth;

            // Tamaño aleatorio
            const size = Math.random() * 20 + 20;

            // Velocidad aleatoria
            const duration = Math.random() * 3 + 2;

            // Aplicar estilos
            rainDrop.style.position = 'absolute';
            rainDrop.style.left = `${posX}px`;
            rainDrop.style.top = '-50px';
            rainDrop.style.fontSize = `${size}px`;
            rainDrop.style.opacity = '0';
            rainDrop.style.animation = `fallDown ${duration}s linear forwards, fadeInOut ${duration}s ease-in-out forwards`;

            // Añadir al contenedor
            rainContainer.appendChild(rainDrop);

            // Eliminar después de la animación
            setTimeout(() => {
                rainDrop.remove();
            }, duration * 1000);
        }, i * 50); // Escalonar la creación para que no aparezcan todos a la vez
    }

    // Reproducir un sonido divertido (opcional)
    const audio = new Audio('/static/sounds/meow.mp3');
    audio.volume = 0.3;
    audio.play().catch(e => console.log('No se pudo reproducir el sonido:', e));

    // Eliminar el contenedor después de que termine la lluvia
    setTimeout(() => {
        rainContainer.remove();
    }, 8000);
}