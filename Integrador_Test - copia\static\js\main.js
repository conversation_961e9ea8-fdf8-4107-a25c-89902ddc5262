/**
 * PlantCare - Script principal
 * Contiene funcionalidades comunes para toda la aplicación
 */

document.addEventListener('DOMContentLoaded', function() {
    // Inicializar componentes comunes
    initializeThemeToggle();
    initializeUserMenu();
    initializeAlerts();
    setupCSRFToken();
});

/**
 * Inicializa el cambio de tema (oscuro/claro)
 */
function initializeThemeToggle() {
    const themeToggle = document.getElementById('theme-toggle');
    if (!themeToggle) return;
    
    const themeIcon = themeToggle.querySelector('.material-icons');
    
    // Check if user has a theme preference stored
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
        document.body.classList.add('dark-theme');
        if (themeIcon) themeIcon.textContent = 'light_mode';
    }
    
    themeToggle.addEventListener('click', function() {
        document.body.classList.toggle('dark-theme');
        
        if (document.body.classList.contains('dark-theme')) {
            localStorage.setItem('theme', 'dark');
            if (themeIcon) themeIcon.textContent = 'light_mode';
        } else {
            localStorage.setItem('theme', 'light');
            if (themeIcon) themeIcon.textContent = 'dark_mode';
        }
    });
}

/**
 * Inicializa el menú desplegable de usuario
 */
function initializeUserMenu() {
    const userMenuButton = document.getElementById('user-menu-button');
    const userDropdown = document.getElementById('user-dropdown');
    
    if (!userMenuButton || !userDropdown) return;
    
    userMenuButton.addEventListener('click', function() {
        userDropdown.classList.toggle('active');
    });
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        if (!userMenuButton.contains(event.target) && !userDropdown.contains(event.target)) {
            userDropdown.classList.remove('active');
        }
    });
}

/**
 * Inicializa las alertas para que se puedan cerrar
 */
function initializeAlerts() {
    const alerts = document.querySelectorAll('.alert');
    
    alerts.forEach(alert => {
        // Add close button if it doesn't exist
        if (!alert.querySelector('.close-btn')) {
            const closeBtn = document.createElement('button');
            closeBtn.className = 'close-btn';
            closeBtn.innerHTML = '&times;';
            closeBtn.style.float = 'right';
            closeBtn.style.background = 'none';
            closeBtn.style.border = 'none';
            closeBtn.style.fontSize = '20px';
            closeBtn.style.cursor = 'pointer';
            closeBtn.style.marginLeft = '10px';
            
            alert.insertBefore(closeBtn, alert.firstChild);
            
            closeBtn.addEventListener('click', function() {
                alert.style.opacity = '0';
                setTimeout(() => {
                    alert.style.display = 'none';
                }, 300);
            });
        }
    });
    
    // Auto-hide alerts after 5 seconds
    setTimeout(() => {
        alerts.forEach(alert => {
            if (alert.classList.contains('alert-success') || alert.classList.contains('alert-info')) {
                alert.style.opacity = '0';
                setTimeout(() => {
                    alert.style.display = 'none';
                }, 300);
            }
        });
    }, 5000);
}

/**
 * Configura el token CSRF para solicitudes AJAX
 */
function setupCSRFToken() {
    // Get CSRF token from meta tag
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    
    // Add CSRF token to all AJAX requests
    document.addEventListener('DOMContentLoaded', function() {
        const xhr = window.XMLHttpRequest;
        const send = xhr.prototype.send;
        
        xhr.prototype.send = function(data) {
            this.setRequestHeader('X-CSRFToken', csrfToken);
            return send.apply(this, arguments);
        };
        
        // For fetch API
        const originalFetch = window.fetch;
        window.fetch = function(url, options = {}) {
            if (options.method && options.method.toLowerCase() !== 'get') {
                if (!options.headers) {
                    options.headers = {};
                }
                
                if (!(options.headers instanceof Headers)) {
                    options.headers['X-CSRFToken'] = csrfToken;
                } else {
                    options.headers.append('X-CSRFToken', csrfToken);
                }
            }
            
            return originalFetch(url, options);
        };
    });
}
