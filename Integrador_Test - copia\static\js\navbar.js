// Navegación minimalista JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Toggle menú móvil
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const navbarNav = document.querySelector('.navbar-nav');
    
    if (mobileToggle && navbarNav) {
        mobileToggle.addEventListener('click', function() {
            navbarNav.classList.toggle('show');
            
            // Cambiar icono
            const icon = this.querySelector('.material-icons');
            if (navbarNav.classList.contains('show')) {
                icon.textContent = 'close';
            } else {
                icon.textContent = 'menu';
            }
        });
        
        // Cerrar menú al hacer click en un enlace
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                navbarNav.classList.remove('show');
                const icon = mobileToggle.querySelector('.material-icons');
                icon.textContent = 'menu';
            });
        });
        
        // Cerrar menú al hacer click fuera
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.navbar-nav') && !e.target.closest('.mobile-menu-toggle')) {
                navbarNav.classList.remove('show');
                const icon = mobileToggle.querySelector('.material-icons');
                icon.textContent = 'menu';
            }
        });
    }
    
    // Marcar enlace activo
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });
    
    // Si estamos en la página principal
    if (currentPath === '/' || currentPath === '') {
        const homeLink = document.querySelector('.nav-link[href="/"]');
        if (homeLink) {
            homeLink.classList.add('active');
        }
    }
});
