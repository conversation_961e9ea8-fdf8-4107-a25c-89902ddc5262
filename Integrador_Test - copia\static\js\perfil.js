document.addEventListener('DOMContentLoaded', () => {
    // Configuración del menú de usuario
    setupUserMenu();

    // Configuración de pestañas del perfil
    setupProfileTabs();

    // Configuración de modales
    setupModals();

    // Configuración de la biblioteca de plantas para añadir
    setupPlantLibrary();
});

// Gestión del menú de usuario y perfil
function setupUserMenu() {
    const userMenuButton = document.getElementById('user-menu-button');
    const userDropdown = document.getElementById('user-dropdown');
    const logoutButton = document.getElementById('logout-button');

    // Cargar imagen de perfil del almacenamiento local (si existe)
    const userAvatar = document.querySelector('.avatar img');
    const savedAvatar = localStorage.getItem('userAvatar');

    if (savedAvatar) {
        userAvatar.src = savedAvatar;
    }

    // Mostrar/ocultar menú de usuario
    userMenuButton.addEventListener('click', () => {
        userDropdown.classList.toggle('active');
    });

    // Cerrar menú al hacer clic fuera
    document.addEventListener('click', (event) => {
        if (!userMenuButton.contains(event.target) && !userDropdown.contains(event.target)) {
            userDropdown.classList.remove('active');
        }
    });

    // Funcionalidad de cerrar sesión
    logoutButton.addEventListener('click', (event) => {
        event.preventDefault();
        // Implementar lógica de cierre de sesión aquí
        localStorage.removeItem('userLoggedIn');
        window.location.href = 'login.html';
    });
}

// Configuración de pestañas del perfil
function setupProfileTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tabId = button.getAttribute('data-tab');

            // Desactivar todas las pestañas
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // Activar la pestaña seleccionada
            button.classList.add('active');
            document.getElementById(tabId).classList.add('active');
        });
    });
}

// Configuración de modales
function setupModals() {
    // Referencias a los modales
    const modals = document.querySelectorAll('.modal');
    const closeButtons = document.querySelectorAll('.close-modal');

    // Botones para abrir modales específicos
    const editProfileBtn = document.querySelector('.profile-actions .btn-primary');
    const changeAvatarBtn = document.getElementById('change-avatar');
    const addPlantBtn = document.getElementById('add-plant-btn');
    const plantDetailBtns = document.querySelectorAll('.plant-actions .btn-text:first-child');

    // Configuración del modal de edición de perfil
    if (editProfileBtn) {
        editProfileBtn.addEventListener('click', () => {
            document.getElementById('edit-profile-modal').classList.add('active');
        });
    }

    // Configuración del modal de cambio de avatar
    if (changeAvatarBtn) {
        changeAvatarBtn.addEventListener('click', () => {
            document.getElementById('change-avatar-modal').classList.add('active');
        });
    }

    // Configuración del modal de añadir planta
    if (addPlantBtn) {
        addPlantBtn.addEventListener('click', () => {
            document.getElementById('add-plant-modal').classList.add('active');
            loadPlantsFromLibrary();
        });
    }

    // Configuración del modal de detalles de planta
    plantDetailBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            document.getElementById('plant-details-modal').classList.add('active');
            // Aquí se cargarían los detalles específicos de la planta
        });
    });

    // Configuración del modal de registro de cuidados
    const registerCareBtns = document.querySelectorAll('.plant-actions .btn-text:nth-child(2)');
    registerCareBtns.forEach((btn, index) => {
        btn.addEventListener('click', () => {
            // Obtener el ID de la planta (en un entorno real, esto vendría de la base de datos)
            const plantId = index + 1;

            // Establecer el ID de la planta en el formulario
            document.getElementById('care-plant-id').value = plantId;

            // Establecer la fecha y hora actual por defecto
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            document.getElementById('care-date').value = `${year}-${month}-${day}T${hours}:${minutes}`;

            // Mostrar el modal
            document.getElementById('register-care-modal').classList.add('active');
        });
    });

    // Cerrar modales con botones de cierre
    closeButtons.forEach(button => {
        button.addEventListener('click', () => {
            button.closest('.modal').classList.remove('active');
        });
    });

    // Cerrar modales haciendo clic fuera del contenido
    modals.forEach(modal => {
        modal.addEventListener('click', (event) => {
            if (event.target === modal) {
                modal.classList.remove('active');
            }
        });
    });

    // Botones de cancelar en los modales
    const cancelButtons = document.querySelectorAll('#cancel-edit, #cancel-avatar, #cancel-add-plant, #cancel-care');
    cancelButtons.forEach(button => {
        button.addEventListener('click', () => {
            button.closest('.modal').classList.remove('active');
        });
    });

    // Manejar el envío del formulario de cuidados
    const careForm = document.getElementById('care-form');
    if (careForm) {
        careForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Obtener los datos del formulario
            const plantId = document.getElementById('care-plant-id').value;
            const careType = document.getElementById('care-type').value;
            const careDate = document.getElementById('care-date').value;
            const careNotes = document.getElementById('care-notes').value;

            // En un entorno real, aquí enviaríamos los datos al servidor
            // Para este ejemplo, simplemente actualizaremos la interfaz

            // Formatear la fecha para mostrarla
            const formattedDate = formatDateTime(careDate);

            // Encontrar la planta correspondiente
            const plantCards = document.querySelectorAll('.plant-card');
            if (plantCards[plantId - 1]) {
                const plantCard = plantCards[plantId - 1];

                // Actualizar la información según el tipo de cuidado
                if (careType === 'watering') {
                    // Actualizar el último riego
                    const wateringValue = plantCard.querySelector('.care-item:first-child .care-value');
                    if (wateringValue) {
                        wateringValue.textContent = formattedDate;
                    }
                } else if (careType === 'fertilizing') {
                    // Actualizar el último abono
                    const fertilizingValue = plantCard.querySelector('.care-item:nth-child(2) .care-value');
                    if (fertilizingValue) {
                        fertilizingValue.textContent = formattedDate;
                    }
                }

                // Mostrar notificación de éxito
                showNotification(`Cuidado registrado correctamente`, 'success');
            }

            // Cerrar el modal
            document.getElementById('register-care-modal').classList.remove('active');
        });
    }
}

// Datos de ejemplo para la biblioteca de plantas (normalmente se cargarían desde una API)
// Los mismos datos que en biblioteca.js para mantener consistencia
const plantasData = [
    {
        id: 1,
        nombre: "Biznaga",
        nombreCientifico: "Echinocactus platyacanthus",
        imagen: "/assets/biznaga.jpg",
        categoria: "cactus",
        riego: "bajo",
        sol: "pleno",
        dificultad: "facil",
        descripcion: "Cactus nativo de Chihuahua que puede crecer hasta 2 metros de altura."
    },
    {
        id: 2,
        nombre: "Gobernadora",
        nombreCientifico: "Larrea tridentata",
        imagen: "/assets/gobernadora.jpg",
        categoria: "arbustos",
        riego: "bajo",
        sol: "pleno",
        dificultad: "facil",
        descripcion: "Arbusto perenne resistente a la sequía, muy común en el desierto chihuahuense."
    },
    {
        id: 3,
        nombre: "Yuca",
        nombreCientifico: "Yucca elata",
        imagen: "/assets/yuca.jpg",
        categoria: "suculentas",
        riego: "bajo",
        sol: "pleno",
        dificultad: "moderada",
        descripcion: "Planta icónica del desierto chihuahuense con hojas puntiagudas y flores blancas."
    },
    {
        id: 4,
        nombre: "Mezquite",
        nombreCientifico: "Prosopis glandulosa",
        imagen: "/assets/mezquite.jpg",
        categoria: "arboles",
        riego: "moderado",
        sol: "pleno",
        dificultad: "moderada",
        descripcion: "Árbol resistente que proporciona sombra y es fundamental en el ecosistema del desierto."
    },
    {
        id: 5,
        nombre: "Lechuguilla",
        nombreCientifico: "Agave lechuguilla",
        imagen: "/assets/agave.jpg",
        categoria: "suculentas",
        riego: "bajo",
        sol: "pleno",
        dificultad: "facil",
        descripcion: "Agave pequeño con hojas en roseta y bordes espinosos."
    },
    {
        id: 6,
        nombre: "Ocotillo",
        nombreCientifico: "Fouquieria splendens",
        imagen: "/assets/ocotillo.jpg",
        categoria: "arbustos",
        riego: "bajo",
        sol: "pleno",
        dificultad: "moderada",
        descripcion: "Arbusto con tallos largos y espinosos que florece con llamativas flores rojas."
    },
    // Resto de plantas...
];

// Configuración del modal de la biblioteca de plantas
function setupPlantLibrary() {
    const librarySearchInput = document.getElementById('library-search-input');
    const libraryCategoryFilter = document.getElementById('library-category-filter');

    if (librarySearchInput) {
        librarySearchInput.addEventListener('input', filterLibraryPlants);
    }

    if (libraryCategoryFilter) {
        libraryCategoryFilter.addEventListener('change', filterLibraryPlants);
    }
}

// Cargar plantas de la biblioteca
function loadPlantsFromLibrary() {
    const libraryPlantsGrid = document.getElementById('library-plants-grid');
    if (!libraryPlantsGrid) return;

    // Limpiar el contenedor
    libraryPlantsGrid.innerHTML = '';

    // Crear tarjetas de plantas
    plantasData.forEach(plant => {
        const plantCard = createPlantCard(plant);
        libraryPlantsGrid.appendChild(plantCard);
    });
}

// Crear una tarjeta de planta para la biblioteca
function createPlantCard(plant) {
    const template = document.getElementById('library-plant-template');
    const plantCard = template.content.cloneNode(true);

    // Llenar los datos de la planta
    const img = plantCard.querySelector('.plant-image img');
    img.src = plant.imagen;
    img.alt = plant.nombre;

    plantCard.querySelector('.plant-name').textContent = plant.nombre;
    plantCard.querySelector('.plant-scientific-name').textContent = plant.nombreCientifico;
    plantCard.querySelector('.water-needs').textContent = getWaterText(plant.riego);
    plantCard.querySelector('.sun-needs').textContent = getSunText(plant.sol);

    // Añadir funcionalidad al botón de seleccionar
    const selectButton = plantCard.querySelector('.select-plant');
    selectButton.addEventListener('click', () => {
        addPlantToMyCollection(plant);
        document.getElementById('add-plant-modal').classList.remove('active');
    });

    return plantCard;
}

// Filtrar plantas en la biblioteca
function filterLibraryPlants() {
    const searchTerm = document.getElementById('library-search-input').value.toLowerCase();
    const category = document.getElementById('library-category-filter').value;
    const libraryPlantsGrid = document.getElementById('library-plants-grid');

    // Limpiar el contenedor
    libraryPlantsGrid.innerHTML = '';

    // Filtrar y mostrar plantas
    const filteredPlants = plantasData.filter(plant => {
        const matchesSearch = plant.nombre.toLowerCase().includes(searchTerm) ||
                              plant.nombreCientifico.toLowerCase().includes(searchTerm);
        const matchesCategory = category === '' || plant.categoria === category;
        return matchesSearch && matchesCategory;
    });

    // Crear tarjetas para las plantas filtradas
    filteredPlants.forEach(plant => {
        const plantCard = createPlantCard(plant);
        libraryPlantsGrid.appendChild(plantCard);
    });

    // Mostrar mensaje si no hay resultados
    if (filteredPlants.length === 0) {
        const noResults = document.createElement('div');
        noResults.className = 'no-results';
        noResults.innerHTML = `
            <span class="material-icons">search_off</span>
            <p>No se encontraron plantas con estos criterios.</p>
        `;
        libraryPlantsGrid.appendChild(noResults);
    }
}

// Añadir planta seleccionada a mi colección
function addPlantToMyCollection(plant) {
    const myPlantsGrid = document.querySelector('#my-plants .plants-grid');
    if (!myPlantsGrid) return;

    // Crear una nueva tarjeta de planta para mi colección
    const plantCard = document.createElement('div');
    plantCard.className = 'plant-card';

    // Fecha actual formateada para "Añadida el"
    const today = new Date();
    const dateOptions = { year: 'numeric', month: 'long', day: 'numeric' };
    const addedDate = today.toLocaleDateString('es-ES', dateOptions);

    // Formatear la información de cuidados
    const waterSchedule = getWaterSchedule(plant.riego);

    plantCard.innerHTML = `
        <div class="plant-image">
            <img src="${plant.imagen}" alt="${plant.nombre}">
            <span class="plant-status healthy">Nueva</span>
        </div>
        <div class="plant-info">
            <h4>${plant.nombre}</h4>
            <p class="scientific-name">${plant.nombreCientifico}</p>
            <div class="plant-care-info">
                <span class="care-item">
                    <span class="material-icons">opacity</span>
                    ${waterSchedule}
                </span>
                <span class="care-item">
                    <span class="material-icons">wb_sunny</span>
                    ${getSunText(plant.sol)}
                </span>
            </div>
            <div class="plant-actions">
                <button class="btn btn-sm btn-text">Ver Detalles</button>
                <button class="btn btn-sm btn-text">Registrar Cuidado</button>
            </div>
        </div>
    `;

    // Añadir la tarjeta al grid
    myPlantsGrid.appendChild(plantCard);

    // Configurar el botón "Ver Detalles"
    const viewDetailsBtn = plantCard.querySelector('.plant-actions .btn-text:first-child');
    viewDetailsBtn.addEventListener('click', () => {
        showPlantDetails(plant, addedDate);
    });

    // Mostrar mensaje de confirmación
    const toast = document.createElement('div');
    toast.className = 'toast-notification';
    toast.innerHTML = `
        <div class="toast-content">
            <span class="material-icons">check_circle</span>
            <span>${plant.nombre} añadida a tu colección</span>
        </div>
    `;
    document.body.appendChild(toast);

    // Eliminar la notificación después de 3 segundos
    setTimeout(() => {
        toast.remove();
    }, 3000);

    // Guardar en almacenamiento local (simulando una base de datos)
    saveMyPlant(plant);
}

// Mostrar detalles de una planta
function showPlantDetails(plant, addedDate) {
    const modal = document.getElementById('plant-details-modal');
    if (!modal) return;

    // Actualizar el modal con los detalles de la planta
    modal.querySelector('#plant-detail-title').textContent = `Detalles de ${plant.nombre}`;
    modal.querySelector('#plant-detail-img').src = plant.imagen;
    modal.querySelector('#plant-detail-name').textContent = plant.nombre;
    modal.querySelector('#plant-detail-scientific').textContent = plant.nombreCientifico;
    modal.querySelector('#plant-detail-water').textContent = getWaterSchedule(plant.riego);
    modal.querySelector('#plant-detail-light').textContent = getSunText(plant.sol);
    modal.querySelector('#plant-detail-added').textContent = addedDate;

    // Mostrar el modal
    modal.classList.add('active');
}

// Guardar planta en el almacenamiento local
function saveMyPlant(plant) {
    let myPlants = JSON.parse(localStorage.getItem('myPlants')) || [];

    // Añadir la fecha de adición
    const plantWithDate = {
        ...plant,
        addedDate: new Date().toISOString(),
        status: 'healthy'
    };

    myPlants.push(plantWithDate);
    localStorage.setItem('myPlants', JSON.stringify(myPlants));

    // Actualizar el contador de plantas
    updatePlantCounter();
}

// Actualizar el contador de plantas
function updatePlantCounter() {
    const myPlants = JSON.parse(localStorage.getItem('myPlants')) || [];
    const plantCountElement = document.querySelector('.profile-stats .stat:first-child .stat-value');
    if (plantCountElement) {
        plantCountElement.textContent = myPlants.length;
    }
}

// Obtener texto descriptivo para necesidades de agua
function getWaterText(water) {
    switch(water) {
        case 'bajo': return 'Riego bajo';
        case 'moderado': return 'Riego moderado';
        case 'alto': return 'Riego frecuente';
        default: return 'Desconocido';
    }
}

// Obtener texto descriptivo para necesidades de sol
function getSunText(sun) {
    switch(sun) {
        case 'sombra': return 'Sombra';
        case 'parcial': return 'Sol parcial';
        case 'pleno': return 'Pleno sol';
        default: return 'Desconocido';
    }
}

// Obtener programación de riego basada en el nivel
function getWaterSchedule(water) {
    switch(water) {
        case 'bajo': return 'Cada 14 días';
        case 'moderado': return 'Cada 7 días';
        case 'alto': return 'Cada 3-4 días';
        default: return 'Programa personalizado';
    }
}

// Formatear fecha y hora para mostrar
function formatDateTime(dateTimeString) {
    const date = new Date(dateTimeString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${day}/${month}/${year} ${hours}:${minutes}`;
}

// Mostrar notificación
function showNotification(message, type = 'info') {
    // Crear elemento de notificación
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;

    // Determinar el icono según el tipo
    let icon = 'info';
    if (type === 'success') icon = 'check_circle';
    else if (type === 'error') icon = 'error';
    else if (type === 'warning') icon = 'warning';

    // Establecer el contenido
    notification.innerHTML = `
        <span class="material-icons">${icon}</span>
        <span class="message">${message}</span>
        <button class="close-notification">
            <span class="material-icons">close</span>
        </button>
    `;

    // Añadir al DOM
    document.body.appendChild(notification);

    // Mostrar con animación (pequeño retraso para que la transición funcione)
    setTimeout(() => {
        notification.classList.add('visible');
    }, 10);

    // Configurar el botón de cierre
    const closeButton = notification.querySelector('.close-notification');
    if (closeButton) {
        closeButton.addEventListener('click', () => {
            notification.classList.remove('visible');
            setTimeout(() => {
                notification.remove();
            }, 300); // Tiempo para la animación de salida
        });
    }

    // Auto-cerrar después de 5 segundos
    setTimeout(() => {
        if (document.body.contains(notification)) {
            notification.classList.remove('visible');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }
    }, 5000);
}
