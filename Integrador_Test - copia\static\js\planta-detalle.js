document.addEventListener('DOMContentLoaded', function() {
    // Get plant ID from URL parameter
    const urlParams = new URLSearchParams(window.location.search);
    const plantId = urlParams.get('id');

    // Initialize page functionality
    initTabs();
    initImageGallery();
    initCalendarButton();
    initSaveButton();
    initUserMenu();
    initThemeToggle();
    initAddToProfileButton();

    // Load plant data based on ID
    loadPlantData(plantId);

    // Set current date as default in calendar modal
    document.getElementById('start-date').valueAsDate = new Date();
});

// Tab functionality
function initTabs() {
    const tabButtons = document.querySelectorAll('.tab-button');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Remove active class from all tabs and contents
            document.querySelectorAll('.tab-button').forEach(tab => {
                tab.classList.remove('active');
            });

            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // Add active class to clicked tab and corresponding content
            button.classList.add('active');
            const tabId = button.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
        });
    });
}

// Image gallery functionality
function initImageGallery() {
    const thumbnails = document.querySelectorAll('.thumbnail');
    const mainImage = document.getElementById('main-plant-image');

    thumbnails.forEach(thumbnail => {
        thumbnail.addEventListener('click', () => {
            // Update main image source
            mainImage.src = thumbnail.src;
            mainImage.alt = thumbnail.alt;

            // Update active thumbnail
            document.querySelectorAll('.thumbnail').forEach(thumb => {
                thumb.classList.remove('active');
            });
            thumbnail.classList.add('active');
        });
    });
}

// Calendar modal functionality
function initCalendarButton() {
    const calendarButton = document.querySelector('.action-buttons .btn-primary');
    const modal = document.getElementById('calendar-modal');
    const closeButton = document.getElementById('close-modal');
    const cancelButton = document.getElementById('cancel-calendar');
    const form = document.getElementById('calendar-form');

    // Open modal when calendar button is clicked
    calendarButton.addEventListener('click', () => {
        modal.classList.add('active');
    });

    // Close modal when close button is clicked
    closeButton.addEventListener('click', () => {
        modal.classList.remove('active');
    });

    // Close modal when cancel button is clicked
    cancelButton.addEventListener('click', () => {
        modal.classList.remove('active');
    });

    // Handle form submission
    form.addEventListener('submit', (event) => {
        event.preventDefault();

        // Get form data
        const formData = new FormData(form);
        const careData = {
            plantId: getPlantId(),
            plantName: document.getElementById('plant-name').textContent,
            careType: formData.get('careType'),
            frequency: formData.get('frequency'),
            startDate: formData.get('startDate'),
            reminder: formData.get('reminder'),
            notes: formData.get('notes')
        };

        // Save to localStorage (in a real app, this would be sent to a server)
        saveToCalendar(careData);

        // Show success message
        showNotification('Recordatorio añadido al calendario');

        // Close modal
        modal.classList.remove('active');
    });

    // Close modal if clicked outside
    window.addEventListener('click', (event) => {
        if (event.target === modal) {
            modal.classList.remove('active');
        }
    });
}

// Save/Favorite button functionality
function initSaveButton() {
    const saveButton = document.querySelector('.action-buttons .btn-outline');
    const saveIcon = saveButton.querySelector('.material-icons');

    // Check if plant is already saved
    const plantId = getPlantId();
    const savedPlants = getSavedPlants();

    if (savedPlants.includes(plantId)) {
        saveIcon.textContent = 'favorite';
        saveButton.classList.add('active');
    }

    // Toggle save status when button is clicked
    saveButton.addEventListener('click', () => {
        const isSaved = saveButton.classList.contains('active');

        if (isSaved) {
            // Remove from saved plants
            removePlantFromSaved(plantId);
            saveIcon.textContent = 'favorite_border';
            saveButton.classList.remove('active');
            showNotification('Planta eliminada de favoritos');
        } else {
            // Add to saved plants
            addPlantToSaved(plantId);
            saveIcon.textContent = 'favorite';
            saveButton.classList.add('active');
            showNotification('Planta añadida a favoritos');
        }
    });
}

// User menu dropdown
function initUserMenu() {
    const userMenuButton = document.getElementById('user-menu-button');
    const userDropdown = document.getElementById('user-dropdown');

    if (userMenuButton && userDropdown) {
        userMenuButton.addEventListener('click', () => {
            userDropdown.classList.toggle('active');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (event) => {
            if (!event.target.closest('.user-menu') && userDropdown.classList.contains('active')) {
                userDropdown.classList.remove('active');
            }
        });
    }
}

// Theme toggle
function initThemeToggle() {
    const themeToggle = document.getElementById('theme-toggle');
    const themeIcon = themeToggle?.querySelector('.material-icons');

    if (themeToggle) {
        // Check current theme
        const darkMode = localStorage.getItem('darkMode') === 'true';

        if (darkMode) {
            document.body.classList.add('dark-theme');
            themeIcon.textContent = 'light_mode';
        }

        themeToggle.addEventListener('click', () => {
            document.body.classList.toggle('dark-theme');
            const isDark = document.body.classList.contains('dark-theme');

            // Update icon
            themeIcon.textContent = isDark ? 'light_mode' : 'dark_mode';

            // Save preference
            localStorage.setItem('darkMode', isDark);
        });
    }
}

// Load plant data
function loadPlantData(plantId) {
    // Get plant ID from URL path if not provided
    if (!plantId) {
        const pathParts = window.location.pathname.split('/');
        plantId = pathParts[pathParts.length - 1];
    }

    // Fetch plant data from API
    fetch(`/api/plants/${plantId}`)
        .then(response => response.json())
        .then(data => {
            if (!data.success) {
                showNotification('Error al cargar los datos de la planta', 'error');
                return;
            }

            const plantData = data.plant;

            if (!plantData) {
                showNotification('Planta no encontrada', 'error');
                return;
            }

            // Update page title
            document.title = `${plantData.name} - PlantCare`;

            // Update breadcrumb
            document.getElementById('plant-name-breadcrumb').textContent = plantData.name;

            // Update basic info
            document.getElementById('plant-name').textContent = plantData.name;
            document.getElementById('plant-scientific-name').textContent = plantData.scientificName;

            // Update plant image
            const mainImage = document.getElementById('main-plant-image');
            mainImage.src = plantData.imageUrl;
            mainImage.alt = plantData.name;

            // Update thumbnails
            document.querySelectorAll('.thumbnail').forEach(thumbnail => {
                thumbnail.src = plantData.imageUrl;
                thumbnail.alt = plantData.name;
                thumbnail.setAttribute('data-src', plantData.imageUrl);
            });

            // Update care overview
            document.getElementById('plant-watering').textContent = plantData.waterRequirement || 'No especificado';
            document.getElementById('plant-sunlight').textContent = plantData.sunlightRequirement || 'No especificado';
            document.getElementById('plant-temperature').textContent = 'Temperatura ambiente (18-25°C)';
            document.getElementById('plant-soil').textContent = plantData.soilRequirement || 'No especificado';

            // Update description tab
            document.getElementById('plant-description').textContent = plantData.description || 'No hay descripción disponible';
            document.getElementById('plant-height').textContent = 'Variable según la especie';
            document.getElementById('plant-bloom').textContent = 'Según temporada';
            document.getElementById('plant-leaf-type').textContent = 'Característico de la especie';
            document.getElementById('plant-leaf-size').textContent = 'Variable';

            // Actualizar biografía y distribución en Chihuahua según el tipo de planta
            updatePlantBiography(plantData.name);
            updatePlantDistribution(plantData.name);

            // Update care tab
            document.getElementById('plant-watering-details').textContent =
                plantData.careInstructions?.watering || `Frecuencia de riego: ${plantData.waterFrequency || 'No especificado'}`;
            document.getElementById('plant-light-details').textContent =
                plantData.careInstructions?.sunlight || `Requerimientos de luz: ${plantData.sunlightRequirement || 'No especificado'}`;
            document.getElementById('plant-temperature-details').textContent =
                plantData.careInstructions?.temperature || 'Mantener a temperatura ambiente, evitando cambios bruscos de temperatura.';
            document.getElementById('plant-soil-details').textContent =
                plantData.careInstructions?.soil || `Tipo de suelo: ${plantData.soilRequirement || 'No especificado'}`;
            document.getElementById('plant-pruning-details').textContent =
                plantData.careInstructions?.fertilizer || 'Podar según sea necesario para mantener la forma y eliminar hojas muertas o dañadas.';

            // Update uses tab
            const uses = [];
            if (plantData.benefits && plantData.benefits.length > 0) {
                uses.push(...plantData.benefits);
            } else {
                uses.push('Planta ornamental');
                if (plantData.isNative) uses.push('Planta nativa de Chihuahua');
                if (plantData.isPurifying) uses.push('Purifica el aire');
                if (plantData.isEdible) uses.push('Partes comestibles');
            }

            document.getElementById('plant-uses').innerHTML =
                uses.length > 0 ? uses.map(use => `<p>${use}</p>`).join('') : '<p>No hay información disponible sobre los usos de esta planta.</p>';

            // Update problems tab
            if (plantData.commonProblems && plantData.commonProblems.length > 0) {
                document.getElementById('plant-pests').innerHTML = plantData.commonProblems
                    .map(problem => `<strong>${problem.problem}:</strong> ${problem.cause}. <em>Solución:</em> ${problem.solution}`)
                    .join('<br><br>');
                document.getElementById('plant-diseases').textContent = 'Ver problemas comunes arriba';
                document.getElementById('plant-other-issues').textContent = 'Ver problemas comunes arriba';
            } else {
                document.getElementById('plant-pests').textContent = 'Información no disponible';
                document.getElementById('plant-diseases').textContent = 'Información no disponible';
                document.getElementById('plant-other-issues').textContent = 'Información no disponible';
            }

            // Update plant badges
            const badgesContainer = document.querySelector('.plant-badges');
            badgesContainer.innerHTML = '';

            if (plantData.difficulty === 'Fácil') {
                badgesContainer.innerHTML += '<span class="badge easy-badge">Fácil de cuidar</span>';
            } else if (plantData.difficulty === 'Moderado') {
                badgesContainer.innerHTML += '<span class="badge medium-badge">Cuidado moderado</span>';
            } else if (plantData.difficulty === 'Difícil') {
                badgesContainer.innerHTML += '<span class="badge hard-badge">Cuidado avanzado</span>';
            }

            if (plantData.isNative) {
                badgesContainer.innerHTML += '<span class="badge native-badge">Nativa</span>';
            }

            if (plantData.isPurifying) {
                badgesContainer.innerHTML += '<span class="badge purifying-badge">Purificadora</span>';
            }

            if (plantData.isEdible) {
                badgesContainer.innerHTML += '<span class="badge edible-badge">Comestible</span>';
            }
        })
        .catch(error => {
            console.error('Error loading plant data:', error);
            showNotification('Error al cargar los datos de la planta', 'error');
        });
}

// Load related plants
function loadRelatedPlants(relatedPlantIds) {
    const plantsGrid = document.querySelector('.plants-grid');
    plantsGrid.innerHTML = '';

    relatedPlantIds.forEach(id => {
        const plantData = getPlantDataById(id);
        if (plantData) {
            const plantCard = createPlantCard(plantData);
            plantsGrid.appendChild(plantCard);
        }
    });
}

// Create a plant card element
function createPlantCard(plant) {
    const card = document.createElement('div');
    card.className = 'plant-card';

    card.innerHTML = `
        <div class="plant-image">
            <img src="${plant.images[0]}" alt="${plant.name}">
            <div class="plant-badges">
                <span class="badge native-badge">Nativa</span>
            </div>
        </div>
        <div class="plant-info">
            <h3 class="plant-name">${plant.name}</h3>
            <p class="plant-scientific-name">${plant.scientificName}</p>
            <a href="/plants/planta/${plant.id}" class="btn btn-primary view-details">Ver Detalles</a>
        </div>
    `;

    return card;
}

// Helper functions for saved plants
function getPlantId() {
    // Intentar obtener el ID de la URL (formato: /plants/planta/123)
    const pathParts = window.location.pathname.split('/');

    // Buscar el ID en la última parte de la URL
    if (pathParts.length > 0) {
        const lastPart = pathParts[pathParts.length - 1];
        if (!isNaN(parseInt(lastPart))) {
            return lastPart;
        }
    }

    // Intentar encontrar 'planta' en la URL y tomar el siguiente segmento
    for (let i = 0; i < pathParts.length; i++) {
        if (pathParts[i] === 'planta' && i + 1 < pathParts.length) {
            return pathParts[i + 1];
        }
    }

    // Si no se encuentra en la URL, intentar obtenerlo de los parámetros de consulta
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('id');
}

function getSavedPlants() {
    const savedPlants = localStorage.getItem('savedPlants');
    return savedPlants ? JSON.parse(savedPlants) : [];
}

function addPlantToSaved(plantId) {
    const savedPlants = getSavedPlants();
    if (!savedPlants.includes(plantId)) {
        savedPlants.push(plantId);
        localStorage.setItem('savedPlants', JSON.stringify(savedPlants));
    }
}

function removePlantFromSaved(plantId) {
    let savedPlants = getSavedPlants();
    savedPlants = savedPlants.filter(id => id !== plantId);
    localStorage.setItem('savedPlants', JSON.stringify(savedPlants));
}

// Inicializar botón para añadir planta al perfil
function initAddToProfileButton() {
    const addToProfileBtn = document.getElementById('add-to-profile-btn');

    if (addToProfileBtn) {
        addToProfileBtn.addEventListener('click', function() {
            const plantId = getPlantId();
            if (!plantId) {
                showNotification('Error: No se pudo identificar la planta', 'error');
                return;
            }

            // Depuración
            console.log('URL actual:', window.location.href);
            console.log('ID de planta obtenido:', plantId);
            console.log('URL de la solicitud:', `/plants/planta/${plantId}/agregar-a-perfil`);

            // Cambiar estado del botón mientras se procesa
            const originalText = addToProfileBtn.innerHTML;
            addToProfileBtn.innerHTML = '<span class="material-icons loading">sync</span> Añadiendo...';
            addToProfileBtn.disabled = true;

            // Enviar solicitud al servidor
            fetch(`/plants/planta/${plantId}/agregar-a-perfil`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.content || ''
                }
            })
            .then(response => {
                if (!response.ok) {
                    if (response.status === 404) {
                        throw new Error('Ruta no encontrada. Verifica la URL.');
                    }
                    return response.text().then(text => {
                        throw new Error(`Error ${response.status}: ${text || response.statusText}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Mostrar mensaje de éxito
                    showNotification(data.message, 'success');

                    // Actualizar botón
                    addToProfileBtn.innerHTML = '<span class="material-icons">check_circle</span> Añadida a mi perfil';
                    addToProfileBtn.classList.add('added');
                } else {
                    // Mostrar mensaje de error
                    showNotification(data.message, 'error');
                    addToProfileBtn.innerHTML = originalText;
                    addToProfileBtn.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Error al añadir la planta a tu perfil', 'error');
                addToProfileBtn.innerHTML = originalText;
                addToProfileBtn.disabled = false;
            });
        });
    }
}

// Helper function for calendar events
function saveToCalendar(careData) {
    // Get existing calendar events
    const calendarEvents = localStorage.getItem('calendarEvents');
    const events = calendarEvents ? JSON.parse(calendarEvents) : [];

    // Add new event
    events.push({
        id: Date.now().toString(),
        ...careData
    });

    // Save back to localStorage
    localStorage.setItem('calendarEvents', JSON.stringify(events));
}

// Function to show notifications
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <span class="material-icons">${type === 'error' ? 'error' : type === 'success' ? 'check_circle' : 'info'}</span>
        <span class="message">${message}</span>
        <button class="close"><span class="material-icons">close</span></button>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Show with animation
    setTimeout(() => notification.classList.add('visible'), 10);

    // Configure close button
    notification.querySelector('.close').addEventListener('click', () => {
        notification.classList.remove('visible');
        setTimeout(() => notification.remove(), 300);
    });

    // Auto-close after 5 seconds
    setTimeout(() => {
        if (document.body.contains(notification)) {
            notification.classList.remove('visible');
            setTimeout(() => notification.remove(), 300);
        }
    }, 5000);
}

// Función para actualizar la biografía de la planta
function updatePlantBiography(plantName) {
    const biographyContainer = document.getElementById('plant-biography');

    // Biografías específicas para cada planta
    const biographies = {
        'Biznaga': `
            <p>La Biznaga (Echinocactus platyacanthus) es una planta icónica del desierto chihuahuense con una historia fascinante. Ha sido parte del ecosistema local durante miles de años, desarrollando adaptaciones únicas para sobrevivir en condiciones extremas de sequía.</p>
            <p>Conocida como "asiento de suegra" por su forma, esta planta ha sido utilizada tradicionalmente por los pueblos indígenas de Chihuahua para diversos propósitos. Su pulpa se ha empleado en la elaboración del dulce tradicional "acitrón", aunque actualmente esta práctica está restringida debido a su estado de conservación.</p>
            <p>La Biznaga es considerada una especie en peligro de extinción debido a la sobreexplotación y la destrucción de su hábitat natural. Actualmente existen programas de conservación en Chihuahua para proteger esta valiosa especie nativa.</p>
        `,
        'Gobernadora': `
            <p>La Gobernadora (Larrea tridentata) es quizás la planta más emblemática del desierto de Chihuahua. Su nombre proviene de su capacidad para "gobernar" o dominar grandes extensiones de terreno, creando comunidades casi monoespecíficas.</p>
            <p>Los pueblos originarios de Chihuahua la han utilizado durante siglos con fines medicinales. La infusión de sus hojas se ha empleado tradicionalmente para tratar problemas renales, dolores reumáticos y como antiséptico natural.</p>
            <p>Esta planta puede vivir cientos de años y posee una química única que le permite inhibir el crecimiento de otras plantas a su alrededor, fenómeno conocido como alelopatía. Su aroma característico después de la lluvia es uno de los olores distintivos del desierto chihuahuense.</p>
        `,
        'Yuca': `
            <p>La Yuca (Yucca elata) ha sido una planta de gran importancia cultural y práctica para los habitantes de Chihuahua durante siglos. Los pueblos rarámuri y otros grupos indígenas han aprovechado prácticamente todas sus partes.</p>
            <p>Sus fibras se han utilizado tradicionalmente para elaborar cuerdas, cestas y sandalias. Las flores son comestibles y se incorporan en diversos platillos tradicionales de la región. Las raíces contienen saponinas que se han empleado como jabón natural.</p>
            <p>En la cultura local, la yuca también tiene significado espiritual, siendo considerada una planta que conecta el mundo terrenal con el espiritual debido a su inflorescencia que se eleva hacia el cielo.</p>
        `,
        'Mezquite': `
            <p>El Mezquite (Prosopis glandulosa) es considerado "el árbol de la vida" en el desierto chihuahuense. Ha sido un recurso fundamental para los habitantes de la región desde tiempos prehispánicos.</p>
            <p>Sus vainas dulces han sido un alimento básico para los pueblos indígenas, quienes las molían para hacer harina. Su madera extremadamente dura y resistente ha sido utilizada en la construcción de viviendas, herramientas y como combustible de alta calidad.</p>
            <p>En la medicina tradicional de Chihuahua, diversas partes del mezquite se han empleado para tratar problemas digestivos, infecciones oculares y heridas. Sus raíces, que pueden extenderse hasta 50 metros buscando agua, han ayudado a los conocedores del desierto a encontrar agua en tiempos de sequía.</p>
        `,
        'Lechuguilla': `
            <p>La Lechuguilla (Agave lechuguilla) es una planta endémica del desierto chihuahuense que ha sido fundamental en la economía local durante generaciones. Es considerada una planta indicadora de este ecosistema.</p>
            <p>Sus fibras, conocidas como "ixtle", han sido la base de una importante industria artesanal en Chihuahua, utilizándose para elaborar cuerdas, cepillos, bolsas y otros productos. Esta actividad sigue siendo una fuente de ingresos para muchas comunidades rurales.</p>
            <p>Los compuestos de sus raíces se han utilizado tradicionalmente como jabón natural (amole) y tienen propiedades medicinales. Estudios recientes han identificado compuestos en la lechuguilla con potencial farmacéutico para el tratamiento de cáncer y otras enfermedades.</p>
        `
    };

    // Obtener la biografía específica o usar una genérica
    const biography = biographies[plantName] || `
        <p>Esta planta tiene una historia fascinante en el estado de Chihuahua. Ha sido parte del ecosistema local durante siglos y ha desarrollado adaptaciones únicas para sobrevivir en este clima.</p>
        <p>Los habitantes locales la han utilizado tradicionalmente para diversos propósitos, desde medicinales hasta decorativos, formando parte importante de la cultura regional.</p>
        <p>Su presencia en el paisaje chihuahuense es un testimonio de la rica biodiversidad del estado y de la capacidad de adaptación de las especies vegetales a condiciones ambientales desafiantes.</p>
    `;

    biographyContainer.innerHTML = biography;
}

// Función para actualizar la distribución de la planta en Chihuahua
function updatePlantDistribution(plantName) {
    const regionsContainer = document.getElementById('plant-regions');
    const bestRegionSpan = document.getElementById('plant-best-region');

    // Distribuciones específicas para cada planta
    const distributions = {
        'Biznaga': {
            regions: [
                'Desierto de Chihuahua - Principalmente en planicies y laderas rocosas',
                'Valle de Casas Grandes - En suelos pedregosos con buen drenaje',
                'Zona de Ojinaga - En terrenos áridos con exposición solar directa'
            ],
            bestRegion: 'zonas áridas con suelos rocosos y excelente drenaje, con plena exposición solar'
        },
        'Gobernadora': {
            regions: [
                'Desierto de Chihuahua - Forma extensos matorrales en planicies desérticas',
                'Bolsón de Mapimí - Domina grandes extensiones de terreno',
                'Zona de Ciudad Juárez - En suelos arenosos y arcillosos'
            ],
            bestRegion: 'planicies desérticas con suelos alcalinos y exposición solar completa'
        },
        'Yuca': {
            regions: [
                'Sierra Tarahumara - En zonas de transición entre bosque y desierto',
                'Desierto de Chihuahua - En laderas y planicies arenosas',
                'Cañón del Cobre - En terrenos rocosos con buen drenaje'
            ],
            bestRegion: 'zonas de transición entre ecosistemas, con suelos arenosos y buena exposición solar'
        },
        'Mezquite': {
            regions: [
                'Valles centrales de Chihuahua - En áreas cercanas a cauces de agua temporales',
                'Desierto de Chihuahua - En depresiones donde se acumula agua ocasionalmente',
                'Zona de Delicias - En suelos profundos de valles agrícolas'
            ],
            bestRegion: 'valles con acceso ocasional a agua y suelos profundos donde sus raíces pueden desarrollarse'
        },
        'Lechuguilla': {
            regions: [
                'Desierto de Chihuahua - En laderas rocosas y pedregosas',
                'Sierra de Samalayuca - En suelos arenosos con buen drenaje',
                'Zona de Aldama - En terrenos calcáreos con pendiente'
            ],
            bestRegion: 'laderas rocosas con suelos pobres y excelente drenaje, con exposición solar directa'
        }
    };

    // Obtener la distribución específica o usar una genérica
    const distribution = distributions[plantName] || {
        regions: [
            'Sierra Tarahumara - Condiciones ideales por su clima templado',
            'Desierto de Chihuahua - Adaptada a condiciones de extrema sequía',
            'Valle de Casas Grandes - Suelos ricos en minerales'
        ],
        bestRegion: 'zonas con exposición solar directa y suelos bien drenados'
    };

    // Actualizar el contenido
    regionsContainer.innerHTML = distribution.regions.map(region => `<li>${region}</li>`).join('');
    bestRegionSpan.textContent = distribution.bestRegion;
}

// Show notification
function showNotification(message, type = 'success') {
    // Crear contenedor de notificaciones si no existe
    let container = document.getElementById('notification-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'notification-container';
        container.className = 'notification-container';
        document.body.appendChild(container);
    }

    // Crear notificación
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;

    // Añadir icono según tipo
    let icon = 'check_circle';
    if (type === 'error') icon = 'error';
    if (type === 'warning') icon = 'warning';
    if (type === 'info') icon = 'info';

    notification.innerHTML = `
        <span class="material-icons">${icon}</span>
        <p>${message}</p>
    `;

    // Añadir al contenedor
    container.appendChild(notification);

    // Mostrar con animación
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    // Ocultar después de un tiempo
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}

// Sample plant data (in a real app, this would come from an API)
function getPlantDataById(id) {
    const plantsData = {
        '1': {
            id: '1',
            name: 'Biznaga',
            scientificName: 'Echinocactus platyacanthus',
            images: ['/assets/plants/biznaga.jpg', '/assets/plants/biznaga.jpg', '/assets/plants/biznaga.jpg'],
            care: {
                watering: 'Escaso',
                sunlight: 'Pleno sol',
                temperature: 'Semiárido',
                soil: 'Bien drenado'
            },
            careDetails: {
                watering: 'Regar solo cuando el suelo esté completamente seco. Durante el invierno, reducir casi por completo el riego.',
                light: 'Necesita al menos 6 horas de luz solar directa diariamente para un desarrollo óptimo.',
                temperature: 'Tolera bien el calor y la sequía. No resiste heladas intensas prolongadas.',
                soil: 'Suelo arenoso o pedregoso con excelente drenaje. No tolera encharcamientos.',
                pruning: 'Eliminar tejido dañado. No requiere poda regular.'
            },
            description: 'La Biznaga es un cactus nativo de México que puede crecer hasta 2 metros de altura. Su forma es globosa con costillas prominentes y espinas rígidas. Es una especie protegida debido a la sobreexplotación.',
            characteristics: {
                height: 'Hasta 2 metros',
                bloom: 'Flores amarillas en primavera y verano',
                leafType: 'Sin hojas, tallo suculento con costillas',
                leafSize: 'No aplica'
            },
            uses: [
                'Ornamental en jardines xerofíticos',
                'Tradicional para elaborar dulce de acitrón (actualmente prohibido por ser especie protegida)',
                'Propiedades medicinales en la medicina tradicional mexicana',
                'Importancia ecológica como hábitat para diversas especies'
            ],
            problems: {
                pests: 'Cochinilla algodonosa y ácaros.',
                diseases: 'Pudrición por exceso de humedad, hongos en caso de heridas.',
                other: 'Crecimiento muy lento. Proteger de heladas prolongadas.'
            },
            relatedPlants: ['3', '5', '7']
        },
        '2': {
            id: '2',
            name: 'Gobernadora',
            scientificName: 'Larrea tridentata',
            images: ['/assets/plants/gobernadora.jpg', '/assets/plants/gobernadora.jpg', '/assets/plants/gobernadora.jpg'],
            care: {
                watering: 'Muy escaso',
                sunlight: 'Pleno sol',
                temperature: 'Cálido',
                soil: 'Arenoso, bien drenado'
            },
            careDetails: {
                watering: 'Extremadamente resistente a la sequía. Regar muy ocasionalmente, solo cuando el suelo esté completamente seco por periodos prolongados.',
                light: 'Requiere exposición completa al sol para prosperar. Mínimo 8 horas de sol directo.',
                temperature: 'Adaptada a temperaturas extremas, tanto altas como relativamente bajas. Ideal para climas desérticos.',
                soil: 'Prefiere suelos pobres, arenosos y con excelente drenaje. No tolera suelos húmedos o encharcados.',
                pruning: 'No requiere poda regular. Eliminar solo ramas muertas o dañadas cuando sea necesario.'
            },
            description: 'La Gobernadora es un arbusto perenne extremadamente resistente, emblemático del desierto chihuahuense. De ramificación densa y hojas pequeñas resinosas con un característico olor intenso. Puede vivir cientos de años y es considerada una de las plantas más adaptadas al desierto.',
            characteristics: {
                height: '1-3 metros',
                bloom: 'Flores amarillas pequeñas en primavera tras lluvias',
                leafType: 'Pequeñas, resinosas y aromáticas',
                leafSize: '5-10 mm'
            },
            uses: [
                'Medicinal en la medicina tradicional para problemas renales, cálculos y como antimicrobiano',
                'Barrera natural contra la erosión en zonas desérticas',
                'Los antiguos pobladores la utilizaban para teñir cuero y textiles',
                'Estudios recientes investigan sus compuestos para aplicaciones farmacéuticas'
            ],
            problems: {
                pests: 'Extremadamente resistente, raramente afectada por plagas.',
                diseases: 'Susceptible a daños por hongos solo en condiciones de humedad excesiva.',
                other: 'Puede inhibir el crecimiento de otras plantas cercanas mediante compuestos alelopáticos que libera en el suelo.'
            },
            relatedPlants: ['3', '6', '8']
        },
        '3': {
            id: '3',
            name: 'Yuca',
            scientificName: 'Yucca elata',
            images: ['/assets/plants/yuca.jpg', '/assets/plants/yuca.jpg', '/assets/plants/yuca.jpg'],
            care: {
                watering: 'Bajo',
                sunlight: 'Pleno sol',
                temperature: 'Templado a cálido',
                soil: 'Arenoso, bien drenado'
            },
            careDetails: {
                watering: 'Tolerante a la sequía. Regar profundamente pero con poca frecuencia, permitiendo que el suelo se seque entre riegos.',
                light: 'Prefiere exposición solar completa pero puede tolerar sombra parcial.',
                temperature: 'Resiste bien el calor y tolerante a temperaturas moderadamente bajas.',
                soil: 'Prefiere suelos arenosos o pedregosos con buen drenaje. Evitar suelos arcillosos o que retengan mucha humedad.',
                pruning: 'Retirar hojas secas de la base. Cortar los tallos florales después de la floración.'
            },
            description: 'La Yuca elata, también conocida como soaptree yucca, es una planta icónica del desierto chihuahuense. Forma una roseta de hojas rígidas y puntiagudas sobre un tallo que puede crecer considerablemente con los años. Produce espectaculares inflorescencias con flores blancas en forma de campana.',
            characteristics: {
                height: '2-4 metros (incluyendo la inflorescencia)',
                bloom: 'Grandes panículas de flores blancas en primavera/verano',
                leafType: 'Lineales, rígidas y puntiagudas',
                leafSize: '30-90 cm de largo'
            },
            uses: [
                'Ornamental en jardines de bajo mantenimiento y xeriscaping',
                'Las flores son comestibles y se usan en ensaladas',
                'Las fibras de las hojas se usaban tradicionalmente para cestería y cuerdas',
                'Algunas comunidades indígenas usaban las raíces como jabón natural'
            ],
            problems: {
                pests: 'Barrenadores de yucca, cochinillas y ácaros.',
                diseases: 'Pudrición de la corona en condiciones de humedad excesiva.',
                other: 'Las hojas son muy puntiagudas y pueden causar lesiones. Ubicar lejos de áreas de paso frecuente.'
            },
            relatedPlants: ['1', '4', '5']
        },
        '4': {
            id: '4',
            name: 'Mezquite',
            scientificName: 'Prosopis glandulosa',
            images: ['/assets/plants/mezquite.jpg', '/assets/plants/mezquite.jpg', '/assets/plants/mezquite.jpg'],
            care: {
                watering: 'Moderado',
                sunlight: 'Pleno sol',
                temperature: 'Cálido',
                soil: 'Adaptable, preferiblemente bien drenado'
            },
            careDetails: {
                watering: 'Muy tolerante a la sequía gracias a su profundo sistema radicular. Establecido, requiere riego ocasional solo en sequías prolongadas.',
                light: 'Requiere exposición solar completa para un óptimo desarrollo.',
                temperature: 'Extremadamente resistente al calor. Puede soportar heladas moderadas una vez establecido.',
                soil: 'Muy adaptable a diferentes tipos de suelo, incluso salinos o alcalinos. Prefiere buen drenaje.',
                pruning: 'Poda de formación en árboles jóvenes. En ejemplares adultos, eliminar ramas cruzadas o dañadas.'
            },
            description: 'El Mezquite es un árbol o arbusto espinoso nativo del desierto chihuahuense, extremadamente resistente y longevo. Sus raíces pueden extenderse hasta 50 metros buscando agua, lo que le permite sobrevivir en condiciones de extrema aridez. Proporciona sombra valiosa en el desierto y es fundamental en el ecosistema.',
            characteristics: {
                height: '3-9 metros',
                bloom: 'Espigas amarillentas fragantes en primavera',
                leafType: 'Compuestas, bipinnadas, caducifolias en sequía extrema',
                leafSize: '5-10 cm de largo'
            },
            uses: [
                'Su madera es excelente para construcción y muebles por su dureza y resistencia',
                'Leña y carbón de alta calidad por su lento y calórico quemado',
                'Las vainas son forrajeras y comestibles, tradicionalmente molidas para harina',
                'Excelente para reforestación de zonas degradadas por su resistencia y mejora del suelo'
            ],
            problems: {
                pests: 'Broca del mezquite y gorgojos que afectan semillas y vainas.',
                diseases: 'Generalmente resistente, puede desarrollar problemas fungosos en condiciones de humedad excesiva.',
                other: 'Espinas prominentes que requieren manejo cuidadoso. Sistema radicular invasivo que puede afectar infraestructura cercana.'
            },
            relatedPlants: ['2', '6', '8']
        },
        '5': {
            id: '5',
            name: 'Lechuguilla',
            scientificName: 'Agave lechuguilla',
            images: ['/assets/plants/lechuguilla.jpg', '/assets/plants/lechuguilla.jpg', '/assets/plants/lechuguilla.jpg'],
            care: {
                watering: 'Muy bajo',
                sunlight: 'Pleno sol',
                temperature: 'Cálido a semiárido',
                soil: 'Rocoso, bien drenado'
            },
            careDetails: {
                watering: 'Extremadamente resistente a la sequía. En cultivo, regar muy ocasionalmente y solo cuando el suelo esté completamente seco.',
                light: 'Requiere exposición completa al sol para un desarrollo óptimo.',
                temperature: 'Adaptada al calor intenso y temperaturas frías moderadas. No tolera heladas severas prolongadas.',
                soil: 'Prefiere suelos pobres, pedregosos o arenosos con excelente drenaje.',
                pruning: 'Mínima. Retirar hojas secas o dañadas desde la base cuando sea necesario.'
            },
            description: 'La Lechuguilla es una pequeña agavácea endémica del desierto chihuahuense, formando rosetas compactas de hojas rígidas con espinas marginales y una terminal particularmente afilada. Es una planta indicadora del ecosistema del desierto chihuahuense y ha sido de gran importancia económica y cultural.',
            characteristics: {
                height: '30-40 cm (roseta), hasta 2 metros con inflorescencia',
                bloom: 'Una vez en su vida produce un tallo floral alto con flores amarillo-verdosas',
                leafType: 'Suculentas, rígidas con espina terminal prominente',
                leafSize: '30-50 cm de largo'
            },
            uses: [
                'Sus fibras (ixtle) son altamente valoradas para la elaboración de cuerdas, cepillos y textiles',
                'Los compuestos de sus raíces se utilizan como jabón natural (amole)',
                'Valor ornamental en jardines xerofíticos',
                'Importancia ecológica como planta nativa que provee refugio y alimento a la fauna local'
            ],
            problems: {
                pests: 'Picudo del agave y cochinillas ocasionalmente.',
                diseases: 'Pudrición por hongos en condiciones de exceso de humedad.',
                other: 'Las espinas son extremadamente afiladas y pueden causar heridas dolorosas. Manipular siempre con guantes gruesos.'
            },
            relatedPlants: ['1', '3', '7']
        },
        '6': {
            id: '6',
            name: 'Ocotillo',
            scientificName: 'Fouquieria splendens',
            images: ['/assets/plants/ocotillo.jpg', '/assets/plants/ocotillo.jpg', '/assets/plants/ocotillo.jpg'],
            care: {
                watering: 'Bajo',
                sunlight: 'Pleno sol',
                temperature: 'Cálido a semiárido',
                soil: 'Arenoso, bien drenado'
            },
            careDetails: {
                watering: 'Muy resistente a la sequía. Regar ocasionalmente en periodos secos prolongados. Con lluvia o riego suficiente desarrolla hojas pequeñas que caen en sequía.',
                light: 'Exposición total al sol para desarrollo óptimo y floración.',
                temperature: 'Adaptado a temperaturas altas extremas. Sensible a heladas prolongadas.',
                soil: 'Prefiere suelo arenoso o pedregoso con drenaje excelente. No tolera encharcamientos.',
                pruning: 'Generalmente no requiere poda. Eliminar solo tallos dañados o muertos si es necesario.'
            },
            description: 'El Ocotillo es una planta fascinante del desierto chihuahuense con una estructura única: largos tallos espinosos que crecen desde una base central formando un arbusto alto con apariencia de candelabro invertido. Tras las lluvias se cubre rápidamente de pequeñas hojas verdes que pierde al secarse el suelo, y en primavera produce espectaculares flores rojas en las puntas de sus ramas.',
            characteristics: {
                height: '2-6 metros',
                bloom: 'Racimos de flores tubulares rojas brillantes en las puntas de los tallos en primavera',
                leafType: 'Pequeñas, ovaladas, de vida corta (aparecen tras la lluvia)',
                leafSize: '2-4 cm de largo'
            },
            uses: [
                'Ornamental en paisajismo de zonas áridas por su estructura dramática y floración espectacular',
                'Tradicionalmente usado para crear cercas vivas en zonas rurales',
                'Las flores son ricas en néctar y atraen colibríes y otros polinizadores',
                'En medicina tradicional, la corteza y flores tienen aplicaciones para problemas digestivos'
            ],
            problems: {
                pests: 'Generalmente resistente a plagas.',
                diseases: 'Susceptible a pudrición de raíz en suelos mal drenados.',
                other: 'Los tallos tienen espinas afiladas que requieren manejo cuidadoso. Puede tardar en establecerse tras el trasplante.'
            },
            relatedPlants: ['2', '7', '8']
        },
        '7': {
            id: '7',
            name: 'Nopal',
            scientificName: 'Opuntia engelmannii',
            images: ['/assets/plants/nopal.jpg', '/assets/plants/nopal.jpg', '/assets/plants/nopal.jpg'],
            care: {
                watering: 'Bajo',
                sunlight: 'Pleno sol',
                temperature: 'Cálido',
                soil: 'Bien drenado'
            },
            careDetails: {
                watering: 'Regar cada 2-3 semanas en verano, casi nada en invierno.',
                light: 'Luz solar directa, mínimo 6 horas diarias.',
                temperature: 'Resiste calor extremo. Proteger de heladas prolongadas.',
                soil: 'Suelo arenoso o pedregoso con buen drenaje.',
                pruning: 'Eliminar cladodios (pencas) dañados o para controlar tamaño.'
            },
            description: 'El nopal es una planta suculenta característica del paisaje chihuahuense, particularmente adaptada a las zonas áridas. Sus cladodios (pencas) aplanados y carnosos están cubiertos de pequeñas espinas. Produce flores amarillas o rojas seguidas de frutos rojizos comestibles (tunas).',
            characteristics: {
                height: '1-3 metros',
                bloom: 'Primavera a verano',
                leafType: 'Cladodios (pencas) suculentos',
                leafSize: '15-30 cm de largo'
            },
            uses: [
                'Comestible: tanto las pencas jóvenes (nopalitos) como los frutos (tunas)',
                'Cercos vivos en zonas rurales',
                'Medicinal: propiedades para controlar diabetes, colesterol y problemas digestivos',
                'Control de erosión en terrenos áridos'
            ],
            problems: {
                pests: 'Cochinilla, grana cochinilla, chinche roja.',
                diseases: 'Pudrición bacteriana, especialmente en condiciones húmedas.',
                other: 'Manipular con cuidado debido a las gloquidios (espinas muy pequeñas).'
            },
            relatedPlants: ['1', '5', '6']
        },
        '8': {
            id: '8',
            name: 'Palo Verde',
            scientificName: 'Parkinsonia aculeata',
            images: ['/assets/plants/palo_verde.jpg', '/assets/plants/palo_verde.jpg', '/assets/plants/palo_verde.jpg'],
            care: {
                watering: 'Moderado',
                sunlight: 'Pleno sol',
                temperature: 'Cálido',
                soil: 'Adaptable, bien drenado'
            },
            careDetails: {
                watering: 'Tolerante a la sequía una vez establecido. Regar profundamente pero con poca frecuencia.',
                light: 'Requiere exposición solar completa para óptimo desarrollo y floración abundante.',
                temperature: 'Adaptado a climas cálidos. Sensible a heladas fuertes cuando es joven.',
                soil: 'Versátil en cuanto a suelos, prefiere los bien drenados. Tolera suelos alcalinos.',
                pruning: 'Poda de formación en ejemplares jóvenes. En adultos, eliminar ramas secas o dañadas.'
            },
            description: 'El Palo Verde es un pequeño árbol o arbusto grande distintivo por su corteza y ramas de color verde que realizan fotosíntesis, una adaptación que le permite sobrevivir perdiendo sus pequeñas hojas durante periodos de sequía. Presenta espinas en las ramas y produce abundantes y vistosas flores amarillas.',
            characteristics: {
                height: '4-8 metros',
                bloom: 'Racimos de flores amarillas fragantes en primavera',
                leafType: 'Compuestas, bipinnadas, pequeñas que caen durante la sequía',
                leafSize: '5-20 cm de largo'
            },
            uses: [
                'Ornamental en paisajismo por su estructura, corteza verde y espectacular floración',
                'Excelente árbol de sombra para zonas áridas por su tolerancia a la sequía',
                'Sus flores atraen polinizadores beneficiosos',
                'Utilizado en proyectos de reforestación en zonas degradadas por su resistencia'
            ],
            problems: {
                pests: 'Psilidos, áfidos y cochinillas pueden afectar nuevos brotes.',
                diseases: 'Generalmente resistente, puede desarrollar problemas de hongos foliar en condiciones de humedad excesiva.',
                other: 'Presencia de espinas que requieren manejo cuidadoso. Sus semillas pueden ser invasivas en algunas áreas.'
            },
            relatedPlants: ['2', '4', '6']
        },
        '9': {
            id: '9',
            name: 'Naranjo',
            scientificName: 'Citrus sinensis',
            images: ['/assets/plants/naranjo.jpg', '/assets/plants/naranjo_flor.jpg', '/assets/plants/naranjo_fruto.jpg'],
            care: {
                watering: 'Moderado',
                sunlight: 'Pleno sol',
                temperature: 'Templado',
                soil: 'Rico en nutrientes, bien drenado'
            },
            careDetails: {
                watering: 'Regar regularmente manteniendo el sustrato ligeramente húmedo. Evitar encharcamientos. Incrementar el riego durante la floración y formación de frutos.',
                light: 'Necesita al menos 6-8 horas de luz solar directa. En Chihuahua, proteger del sol intenso del mediodía durante el verano.',
                temperature: 'Prefiere climas templados. En zonas frías de Chihuahua puede requerir protección contra heladas, especialmente cuando son árboles jóvenes.',
                soil: 'Sustrato rico en materia orgánica, ligeramente ácido (pH 6-6.5), con buen drenaje para evitar pudrición de raíces.',
                pruning: 'Poda de formación en árboles jóvenes. Eliminar ramas muertas, enfermas o cruzadas. Podar ligeramente después de la cosecha.'
            },
            description: 'El naranjo es un árbol frutal perennifolio de tamaño mediano con copa redondeada y densa. Si bien no es nativo de Chihuahua, puede cultivarse con éxito en zonas protegidas del estado. Sus flores blancas (azahares) son muy aromáticas y los frutos aportan un valioso recurso de vitamina C. Requiere cuidados especiales para prosperar en el clima local.',
            characteristics: {
                height: '3-6 metros',
                bloom: 'Primavera, con posibles floraciones menores durante el año',
                leafType: 'Perennes, ovaladas, brillantes y aromáticas',
                leafSize: '5-10 cm de largo'
            },
            uses: [
                'Producción de frutos comestibles ricos en vitamina C',
                'Ornamental por su follaje siempre verde y flores aromáticas',
                'Las flores se utilizan en perfumería y para infusiones',
                'La piel de la naranja contiene aceites esenciales utilizados en aromaterapia'
            ],
            problems: {
                pests: 'Pulgones, mosca blanca, ácaros y cochinillas. En Chihuahua vigilar especialmente por trips.',
                diseases: 'Hongos como Phytophthora, virus de la tristeza de los cítricos. El clima seco de Chihuahua reduce algunos problemas fúngicos.',
                other: 'Sensible a heladas fuertes. Puede sufrir clorosis por deficiencias de hierro en suelos muy alcalinos comunes en la región.'
            },
            relatedPlants: ['10', '12', '14']
        },
        '10': {
            id: '10',
            name: 'Rosal',
            scientificName: 'Rosa sp.',
            images: ['/assets/plants/rosal.jpg', '/assets/plants/rosal_rojo.jpg', '/assets/plants/rosal_flor.jpg'],
            care: {
                watering: 'Moderado',
                sunlight: 'Pleno sol a parcial',
                temperature: 'Templado',
                soil: 'Rico en nutrientes, bien drenado'
            },
            careDetails: {
                watering: 'Riego profundo 2-3 veces por semana en clima seco. Evitar mojar las hojas para prevenir enfermedades fungosas. Reducir en invierno.',
                light: 'Mínimo 6 horas de sol directo para floración óptima. En el intenso sol de Chihuahua, mejor con sol de mañana y sombra parcial en tardes de verano.',
                temperature: 'Tolera bien el frío. Muchas variedades resisten las heladas de Chihuahua. Puede necesitar protección en inviernos extremos.',
                soil: 'Suelo rico en materia orgánica, bien drenado y ligeramente ácido (pH 6-6.5). Añadir compost regularmente.',
                pruning: 'Poda anual a finales de invierno antes de la brotación. Eliminar ramas muertas, débiles o cruzadas. Cortar flores marchitas para estimular nueva floración.'
            },
            description: 'Los rosales son arbustos florales icónicos que pueden adaptarse bien al clima de Chihuahua eligiendo variedades resistentes. Producen las famosas rosas, flores emblemáticas de belleza y fragancia incomparables. Existen numerosas variedades que difieren en tamaño, color y forma de flor. Aunque requieren atención regular, recompensan con floraciones espectaculares.',
            characteristics: {
                height: '0.5-3 metros según la variedad',
                bloom: 'Principalmente primavera y verano, algunas variedades remontantes florecen repetidamente',
                leafType: 'Compuestas, con 5-7 foliolos, generalmente dentados y brillantes',
                leafSize: '5-10 cm (hoja completa)'
            },
            uses: [
                'Ornamental en jardines como ejemplar individual o en grupos',
                'Flores cortadas para ramos y arreglos florales',
                'Setos floridos y barreras con variedades trepadoras o de cobertura',
                'Producción de esencias, agua de rosas y productos cosméticos'
            ],
            problems: {
                pests: 'Pulgones, trips, araña roja y escarabajos. El clima seco de Chihuahua favorece ácaros que requieren monitoreo constante.',
                diseases: 'Oídio, roya, mancha negra y mildiu. El aire seco de Chihuahua reduce problemas de hongos pero requiere vigilancia.',
                other: 'Sensibles a clorosis férrica en suelos alcalinos típicos de la región. Las espinas pueden causar lesiones durante su manipulación.'
            },
            relatedPlants: ['12', '13', '14']
        },
        '11': {
            id: '11',
            name: 'Pata de Elefante',
            scientificName: 'Beaucarnea recurvata',
            images: ['/assets/plants/pata_elefante.jpg', '/assets/plants/pata_elefante2.jpg', '/assets/plants/pata_elefante3.jpg'],
            care: {
                watering: 'Bajo',
                sunlight: 'Parcial a pleno',
                temperature: 'Cálido a templado',
                soil: 'Arenoso, bien drenado'
            },
            careDetails: {
                watering: 'Muy resistente a la sequía. Regar solo cuando el sustrato esté completamente seco. En interiores, aproximadamente cada 2-3 semanas en verano, mucho menos en invierno.',
                light: 'Ideal: luz brillante indirecta o sol filtrado. Tolera pleno sol en clima de Chihuahua si se aclimata gradualmente. En interiores, ubicar cerca de ventanas luminosas.',
                temperature: 'Prefiere clima cálido pero tolera temperaturas hasta cerca de 0°C si está seco. Proteger de heladas. Ideal para interiores en zonas más frías de Chihuahua.',
                soil: 'Mezcla muy bien drenada tipo cactus: arena, perlita y tierra. No tolera encharcamientos que causan pudrición del tronco.',
                pruning: 'Mínimo mantenimiento. Retirar hojas secas de la base. No requiere poda estructural regular.'
            },
            description: 'La Pata de Elefante o Nolina es una planta singular originaria de México caracterizada por su base ensanchada que almacena agua, similar a un pie de elefante. Sus largas hojas arqueadas crecen desde la copa del tallo formando una fuente verde. De crecimiento lento pero muy longeva, es extremadamente resistente a la sequía y perfecta para cultivo en maceta tanto en interiores como en patios de Chihuahua.',
            characteristics: {
                height: '1-10 metros (muy lento crecimiento)',
                bloom: 'Raramente florece en cultivo, especialmente en interior',
                leafType: 'Largas, delgadas y arqueadas, similares a listones',
                leafSize: '0.5-2 metros de largo, 1-2 cm de ancho'
            },
            uses: [
                'Planta de interior por su bajo mantenimiento y aspecto escultórico',
                'Ejemplar focal en jardines xerofíticos y paisajismo de bajo consumo hídrico',
                'Ideal para grandes contenedores en patios y terrazas',
                'Planta adaptada para oficinas y espacios públicos por su resistencia'
            ],
            problems: {
                pests: 'Relativamente resistente. Ocasionalmente cochinillas y ácaros, especialmente en ambientes muy secos.',
                diseases: 'Pudrición del tronco por exceso de humedad, principal problema a evitar.',
                other: 'Crecimiento extremadamente lento. Los daños en el tronco son permanentes y no se recuperan, manipular con cuidado.'
            },
            relatedPlants: ['1', '3', '5']
        },
        '12': {
            id: '12',
            name: 'Ojo de Niño',
            scientificName: 'Thunbergia alata',
            images: ['/assets/plants/ojo_nino.jpg', '/assets/plants/ojo_nino2.jpg', '/assets/plants/ojo_nino3.jpg'],
            care: {
                watering: 'Moderado',
                sunlight: 'Parcial',
                temperature: 'Templado',
                soil: 'Fértil, bien drenado'
            },
            careDetails: {
                watering: 'Mantener el sustrato uniformemente húmedo pero no empapado. Regar cuando la superficie comience a secarse. En Chihuahua, riego más frecuente durante el calor estival.',
                light: 'Prefiere luz brillante con sol parcial. En Chihuahua, ubicar donde reciba sol de mañana y sombra en las horas más calurosas de la tarde.',
                temperature: 'Planta de clima templado. Sensible a heladas, cultivar como anual en Chihuahua o proteger durante el invierno.',
                soil: 'Sustrato rico en materia orgánica, fértil y con buen drenaje. Agregar compost para mejorar la retención de humedad.',
                pruning: 'Recortar para controlar el crecimiento y estimular ramificación. Podar tras la temporada de floración para rejuvenecer la planta.'
            },
            description: 'El Ojo de Niño es una atractiva enredadera de crecimiento rápido, originaria de África Oriental. Sus distintivas flores en forma de trompeta, generalmente naranjas o amarillas con centro oscuro, recuerdan a ojos infantiles, de ahí su nombre. Ideal para cubrir rápidamente pérgolas, enrejados o cercas en jardines chihuahuenses durante primavera y verano.',
            characteristics: {
                height: '1.5-2 metros de longitud',
                bloom: 'Continua durante primavera y verano',
                leafType: 'Acorazonadas a triangulares con bordes ligeramente dentados',
                leafSize: '4-8 cm de largo'
            },
            uses: [
                'Enredadera ornamental para cubrir estructuras, pérgolas y cercas',
                'Excelente para cestas colgantes y contenedores',
                'Pantalla estacional para proporcionar privacidad o sombra ligera',
                'Atrae polinizadores como mariposas y colibríes'
            ],
            problems: {
                pests: 'Pulgones, ácaros y mosca blanca, especialmente en climas secos como Chihuahua.',
                diseases: 'Susceptible a oídio y podredumbre de raíz si hay exceso de humedad.',
                other: 'Puede volverse invasiva en algunas zonas por su rápido crecimiento y fácil autosemillado. Controlar podando regularmente.'
            },
            relatedPlants: ['9', '10', '13']
        },
        '13': {
            id: '13',
            name: 'Lavanda',
            scientificName: 'Lavandula angustifolia',
            images: ['/assets/plants/lavanda.jpg', '/assets/plants/lavanda2.jpg', '/assets/plants/lavanda3.jpg'],
            care: {
                watering: 'Bajo',
                sunlight: 'Pleno sol',
                temperature: 'Templado a semiárido',
                soil: 'Pobre, bien drenado'
            },
            careDetails: {
                watering: 'Una vez establecida, muy resistente a la sequía. Riego profundo pero infrecuente, dejando secar el suelo entre riegos. Exceso de agua es su peor enemigo.',
                light: 'Requiere pleno sol, mínimo 6 horas diarias para óptima floración y producción de aceites esenciales. Ideal en las soleadas condiciones de Chihuahua.',
                temperature: 'Tolera bien el calor de Chihuahua y las heladas moderadas. Algunas variedades son más resistentes al frío que otras.',
                soil: 'Prefiere suelos pobres, calcáreos y muy bien drenados. Añadir arena o grava mejora el drenaje. Evitar suelos arcillosos y encharcadizos.',
                pruning: 'Podar ligeramente después de la floración, eliminando tallos florales. Poda más severa a fines de invierno para mantener forma compacta y rejuvenecer.'
            },
            description: 'La lavanda es un arbusto aromático perenne originario de la región mediterránea, perfectamente adaptable al clima de Chihuahua. Sus características espigas florales púrpuras son conocidas mundialmente por su fragancia y propiedades. Forma montículos ordenados de follaje gris-verdoso que aportan textura y color durante todo el año. Extremadamente beneficiosa para jardines sostenibles por su resistencia a la sequía y atractivo para polinizadores.',
            characteristics: {
                height: '30-60 cm',
                bloom: 'Final de primavera a mediados de verano',
                leafType: 'Lineales a lanceoladas, color gris-verdoso, aromáticas',
                leafSize: '2-6 cm de largo'
            },
            uses: [
                'Ornamental en borduras, macizos y jardines de rocalla',
                'Planta aromática para extracción de aceites esenciales',
                'Repelente natural de insectos como mosquitos y polillas',
                'Flores secas para potpourri, sachets y aplicaciones culinarias selectas'
            ],
            problems: {
                pests: 'Generalmente resistente. Ocasionalmente áfidos o escamas en climas húmedos.',
                diseases: 'Pudrición de raíz por exceso de humedad, principal problema en suelos pesados.',
                other: 'Tiende a volverse leñosa y menos productiva después de 5-6 años, requiriendo renovación o reemplazo. Puede sufrir con humedad ambiental excesiva.'
            },
            relatedPlants: ['2', '6', '10']
        },
        '14': {
            id: '14',
            name: 'Bugambilia',
            scientificName: 'Bougainvillea spectabilis',
            images: ['/assets/plants/bugambilia.jpg', '/assets/plants/bugambilia2.jpg', '/assets/plants/bugambilia3.jpg'],
            care: {
                watering: 'Bajo',
                sunlight: 'Pleno sol',
                temperature: 'Cálido a templado',
                soil: 'Bien drenado'
            },
            careDetails: {
                watering: 'Resistente a sequía una vez establecida. Riego moderado durante el establecimiento, luego reducir. El estrés hídrico controlado estimula la floración.',
                light: 'Requiere pleno sol para floración abundante, mínimo 6 horas diarias. Ideal para las condiciones soleadas de Chihuahua.',
                temperature: 'Prefiere climas cálidos. Sensible a heladas fuertes, proteger durante el invierno chihuahuense o plantar en ubicaciones abrigadas.',
                soil: 'Tolera diversos tipos de suelo siempre que tengan buen drenaje. Prefiere suelos ligeramente ácidos a neutros, no muy fértiles.',
                pruning: 'Poda de formación en ejemplares jóvenes. Poda de mantenimiento después de cada ciclo de floración para controlar tamaño y estimular nueva floración.'
            },
            description: 'La Bugambilia es un arbusto trepador semileñoso, originario de Brasil pero perfectamente adaptado a regiones áridas como Chihuahua. Sus espectaculares "flores" son realmente brácteas coloridas (hojas modificadas) que rodean las pequeñas flores verdaderas. Disponible en múltiples colores desde púrpura intenso hasta blanco, rosa, naranja o rojo. Extremadamente resistente y longeva, puede transformar muros, pérgolas o cercas en explosiones de color.',
            characteristics: {
                height: '3-10 metros según se guíe o contenga',
                bloom: 'Principalmente primavera y verano, pero puede mostrar color casi todo el año en climas favorables',
                leafType: 'Ovaladas, verdes brillantes, alternas',
                leafSize: '5-10 cm de largo'
            },
            uses: [
                'Ornamental para cubrir muros, pérgolas o formar setos informales',
                'Espécimen focal en macetas grandes o jardineras',
                'Pantalla de privacidad en forma de seto o cubierta para cercas',
                'Las brácteas secas se utilizan en arreglos florales permanentes'
            ],
            problems: {
                pests: 'Relativamente resistente. Puede sufrir ataques de pulgones, cochinillas o ácaros en condiciones de estrés.',
                diseases: 'Susceptible a hongos foliares solo en condiciones de humedad excesiva, poco común en Chihuahua.',
                other: 'Posee espinas que pueden causar lesiones durante su manipulación. Algunas variedades pueden tornarse invasivas si no se controlan.'
            },
            relatedPlants: ['6', '10', '13']
        }
    };

    return plantsData[id];
}
