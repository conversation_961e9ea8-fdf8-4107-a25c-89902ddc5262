// Sistema de autenticación
const authManager = {
    getToken: function() {
        return localStorage.getItem('auth_token');
    },
    isAuthenticated: function() {
        return !!this.getToken();
    },
    logout: function() {
        localStorage.removeItem('auth_token');
        // No redirigir a login.html
        // window.location.href = '/login.html';
    },
    refreshToken: async function() {
        try {
            const response = await fetch('/api/auth/refresh', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getToken()}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                localStorage.setItem('auth_token', data.token);
                return true;
            } else {
                this.logout();
                return false;
            }
        } catch (error) {
            console.error('Error al refrescar token:', error);
            return false;
        }
    }
};

// Función para realizar peticiones (sin autenticación para simplificar)
async function fetchAuth(url, options = {}) {
    // Asegurar que tenemos headers
    if (!options.headers) {
        options.headers = {};
    }

    try {
        let response = await fetch(url, options);

        // Manejar errores de forma consistente
        if (!response.ok) {
            const errorData = await response.json();
            showErrorNotification(errorData.message || 'Error en la operación');
            throw new Error(errorData.message || 'Error en la solicitud');
        }

        return response;
    } catch (error) {
        console.error('Error en la petición:', error);
        throw error;
    }
}

// Sistema de notificaciones
function showErrorNotification(message) {
    // Crear elemento de notificación
    const notification = document.createElement('div');
    notification.className = 'notification error';
    notification.innerHTML = `
        <span class="material-icons">error</span>
        <span class="message">${message}</span>
        <button class="close"><span class="material-icons">close</span></button>
    `;

    // Añadir a la página
    document.body.appendChild(notification);

    // Mostrar con animación
    setTimeout(() => notification.classList.add('visible'), 10);

    // Configurar cierre
    notification.querySelector('.close').addEventListener('click', () => {
        notification.classList.remove('visible');
        setTimeout(() => notification.remove(), 300);
    });

    // Auto-cerrar después de 5 segundos
    setTimeout(() => {
        if (document.body.contains(notification)) {
            notification.classList.remove('visible');
            setTimeout(() => notification.remove(), 300);
        }
    }, 5000);
}

// Variables
let currentPage = 1;
let currentView = 'grid';
let currentSort = 'match';
let plantData = []; // Esto almacenará los datos de plantas (en una aplicación real, vendría de la API)

// Inicializar la página
document.addEventListener('DOMContentLoaded', function() {
    // Añadir estilos para notificaciones
    const notificationStyles = document.createElement('style');
    notificationStyles.textContent = `
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            padding: 12px 16px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transform: translateX(120%);
            transition: transform 0.3s ease;
            z-index: 1000;
        }

        .notification.visible {
            transform: translateX(0);
        }

        .notification.error {
            border-left: 4px solid #dc3545;
        }

        .notification .material-icons {
            margin-right: 8px;
            color: #dc3545;
        }

        .notification .close {
            margin-left: 16px;
            background: none;
            border: none;
            cursor: pointer;
            color: #6c757d;
        }

        .dark-theme .notification {
            background-color: #333;
            color: #fff;
        }

        .error-state, .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: var(--text-light);
        }

        .error-state .material-icons, .empty-state .material-icons {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.7;
        }
    `;
    document.head.appendChild(notificationStyles);

    // No verificar autenticación
    // if (!authManager.isAuthenticated()) {
    //     window.location.href = '/login.html?redirect=' + encodeURIComponent(window.location.href);
    //     return;
    // }

    // Inicializar la página si la autenticación es correcta
    initPage();
});

// Función de inicialización principal
function initPage() {
    // Toggle del tema
    const themeToggle = document.getElementById('theme-toggle');
    const themeIcon = themeToggle.querySelector('.material-icons');

    // Verificar si hay una preferencia de tema guardada
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
        document.body.classList.add('dark-theme');
        themeIcon.textContent = 'light_mode';
    }

    // Evento para cambiar el tema
    themeToggle.addEventListener('click', function() {
        document.body.classList.toggle('dark-theme');

        if (document.body.classList.contains('dark-theme')) {
            localStorage.setItem('theme', 'dark');
            themeIcon.textContent = 'light_mode';
        } else {
            localStorage.setItem('theme', 'light');
            themeIcon.textContent = 'dark_mode';
        }
    });

    // Menú de usuario
    const userMenuButton = document.getElementById('user-menu-button');
    const userDropdown = document.getElementById('user-dropdown');

    userMenuButton.addEventListener('click', function() {
        userDropdown.classList.toggle('active');
    });

    // Cerrar menú cuando se hace clic fuera
    document.addEventListener('click', function(event) {
        if (!userMenuButton.contains(event.target) && !userDropdown.contains(event.target)) {
            userDropdown.classList.remove('active');
        }
    });

    // Evento para cerrar sesión
    const logoutButton = document.getElementById('logout-button');
    if (logoutButton) {
        logoutButton.addEventListener('click', function(e) {
            e.preventDefault();

            // Realizar cierre de sesión en el servidor
            fetchAuth('/api/auth/logout', { method: 'POST' })
                .then(() => {
                    // Limpiar datos locales y redirigir
                    authManager.logout();
                })
                .catch(error => {
                    console.error('Error al cerrar sesión:', error);
                    // Intentar cerrar sesión localmente aunque falle en el servidor
                    authManager.logout();
                });
        });
    }

    // Configuración de vistas: cuadrícula vs lista
    const gridViewButton = document.getElementById('grid-view');
    const listViewButton = document.getElementById('list-view');
    const plantsContainer = document.getElementById('plant-recommendations');

    gridViewButton.addEventListener('click', function() {
        if (currentView !== 'grid') {
            currentView = 'grid';
            plantsContainer.className = 'plants-grid';
            gridViewButton.classList.add('active');
            listViewButton.classList.remove('active');
        }
    });

    listViewButton.addEventListener('click', function() {
        if (currentView !== 'list') {
            currentView = 'list';
            plantsContainer.className = 'plants-list';
            listViewButton.classList.add('active');
            gridViewButton.classList.remove('active');

            // Agregar clases adicionales para estilo de lista
            document.querySelectorAll('.plant-card').forEach(card => {
                card.classList.add('list-view');
            });
        }
    });

    // Ordenar recomendaciones
    const sortSelect = document.getElementById('sort-recommendations');

    sortSelect.addEventListener('change', function() {
        currentSort = this.value;
        loadPlantPage(currentPage);
    });

    // Paginación
    const prevPageButton = document.getElementById('prev-page');
    const nextPageButton = document.getElementById('next-page');
    const currentPageSpan = document.getElementById('current-page');

    if (prevPageButton && nextPageButton) {
        prevPageButton.addEventListener('click', function() {
            if (currentPage > 1) {
                currentPage--;
                updatePagination();
            }
        });

        nextPageButton.addEventListener('click', function() {
            if (currentPage < 3) { // Se actualizará dinámicamente con la respuesta de la API
                currentPage++;
                updatePagination();
            }
        });
    }

    // Formulario de filtro
    const filterForm = document.getElementById('recommendations-filter');

    filterForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Mostrar un pequeño indicador de carga
        const plantsContainer = document.getElementById('plant-recommendations');
        plantsContainer.innerHTML = '<div class="loading-indicator">Buscando plantas que coincidan con tus preferencias...</div>';

        // Reiniciar a la primera página cuando se aplican filtros
        currentPage = 1;

        // Cargar plantas con los nuevos filtros
        loadPlantPage(currentPage);
    });

    // Añadir efectos de hover para guardar plantas
    document.addEventListener('click', function(event) {
        const saveButton = event.target.closest('.plant-actions .btn-outline');
        if (saveButton) {
            const icon = saveButton.querySelector('.material-icons');
            const plantId = saveButton.closest('.plant-card').dataset.plantId;

            if (icon.textContent === 'bookmark_border') {
                // Guardar planta
                savePlant(plantId, saveButton);
            } else {
                // Quitar de guardados
                unsavePlant(plantId, saveButton);
            }
        }
    });

    // Añadir estilo para la vista de lista si es necesario
    const style = document.createElement('style');
    style.textContent = `
        .plants-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .plants-list .plant-card {
            display: grid;
            grid-template-columns: 180px 1fr;
            height: auto;
        }

        .plants-list .plant-image {
            height: 100%;
        }

        .plants-list .plant-info {
            display: flex;
            flex-direction: column;
        }

        .plants-list .plant-attributes {
            margin-top: auto;
            margin-bottom: 16px;
        }

        .loading-indicator {
            text-align: center;
            padding: 32px;
            color: var(--text-light);
        }

        .btn.saved {
            background-color: var(--background-light);
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        @media (max-width: 768px) {
            .plants-list .plant-card {
                grid-template-columns: 120px 1fr;
            }
        }

        @media (max-width: 576px) {
            .plants-list .plant-card {
                grid-template-columns: 1fr;
            }

            .plants-list .plant-image {
                height: 180px;
            }
        }
    `;
    document.head.appendChild(style);

    // Inicializar la carga de plantas
    loadPlantPage(currentPage);
}

// Función para actualizar la paginación
function updatePagination() {
    const currentPageSpan = document.getElementById('current-page');
    const prevPageButton = document.getElementById('prev-page');
    const nextPageButton = document.getElementById('next-page');

    if (currentPageSpan) currentPageSpan.textContent = currentPage;
    if (prevPageButton) prevPageButton.disabled = currentPage === 1;
    if (nextPageButton) nextPageButton.disabled = currentPage === 3; // Se actualizará dinámicamente

    loadPlantPage(currentPage);
}

// Función para actualizar los controles de paginación
function updatePaginationControls(pagination) {
    const currentPageSpan = document.getElementById('current-page');
    const prevPageButton = document.getElementById('prev-page');
    const nextPageButton = document.getElementById('next-page');
    const totalPagesSpan = document.getElementById('total-pages');

    if (currentPageSpan) currentPageSpan.textContent = pagination.currentPage;
    if (totalPagesSpan) totalPagesSpan.textContent = pagination.totalPages;
    if (prevPageButton) prevPageButton.disabled = pagination.currentPage <= 1;
    if (nextPageButton) nextPageButton.disabled = pagination.currentPage >= pagination.totalPages;
}

// Función para obtener filtros activos
function getActiveFilters() {
    return {
        experienceLevel: document.getElementById('experience-level').value,
        sunlight: document.getElementById('sunlight').value,
        wateringFrequency: document.getElementById('watering-frequency').value,
        space: document.getElementById('space').value,
        purposes: {
            decoration: document.getElementById('purpose-decoration').checked,
            purification: document.getElementById('purpose-purification').checked,
            garden: document.getElementById('purpose-garden').checked,
            native: document.getElementById('purpose-native').checked
        }
    };
}

// Función para mostrar plantas
function displayPlants(plants) {
    const plantsContainer = document.getElementById('plant-recommendations');

    if (!plants || plants.length === 0) {
        plantsContainer.innerHTML = `
            <div class="empty-state">
                <span class="material-icons">eco</span>
                <h3>No se encontraron plantas</h3>
                <p>Intenta ajustar los filtros para ver más opciones.</p>
            </div>
        `;
        return;
    }

    plantsContainer.innerHTML = '';

    plants.forEach(plant => {
        const plantCard = createPlantCard(plant);
        plantsContainer.appendChild(plantCard);
    });

    // Restaurar la vista correcta
    if (currentView === 'list') {
        plantsContainer.className = 'plants-list';
        document.querySelectorAll('.plant-card').forEach(card => {
            card.classList.add('list-view');
        });
    } else {
        plantsContainer.className = 'plants-grid';
    }
}

// Función para crear tarjeta de planta
function createPlantCard(plant) {
    const card = document.createElement('div');
    card.className = 'plant-card';
    card.dataset.plantId = plant.id;

    card.innerHTML = `
        <div class="match-badge">
            ${plant.matchPercentage}% Match
        </div>
        <div class="plant-image">
            <img src="${plant.imageUrl}" alt="${plant.name}">
            <div class="plant-badges">
                ${plant.isNative ? '<span class="badge native-badge">Nativa</span>' : ''}
                ${plant.isPurifying ? '<span class="badge purifying-badge">Purificadora</span>' : ''}
                ${plant.isEdible ? '<span class="badge edible-badge">Comestible</span>' : ''}
            </div>
        </div>
        <div class="plant-info">
            <h3 class="plant-name">${plant.name}</h3>
            <p class="plant-scientific-name">${plant.scientificName}</p>
            <div class="plant-attributes">
                <div class="attribute">
                    <span class="material-icons">opacity</span>
                    <span>${plant.waterRequirement}</span>
                </div>
                <div class="attribute">
                    <span class="material-icons">wb_sunny</span>
                    <span>${plant.sunlightRequirement}</span>
                </div>
                <div class="attribute">
                    <span class="material-icons">equalizer</span>
                    <span>${plant.difficulty}</span>
                </div>
            </div>
            <div class="plant-actions">
                <button class="btn btn-outline btn-sm ${plant.isSaved ? 'saved' : ''}">
                    <span class="material-icons">${plant.isSaved ? 'bookmark' : 'bookmark_border'}</span>
                    ${plant.isSaved ? 'Guardado' : 'Guardar'}
                </button>
                <a href="biblioteca.html?plant=${plant.id}" class="btn btn-primary btn-sm">Ver Detalles</a>
            </div>
        </div>
    `;

    return card;
}

// Función para cargar una página de plantas
async function loadPlantPage(page) {
    const plantsContainer = document.getElementById('plant-recommendations');
    plantsContainer.innerHTML = '<div class="loading-indicator">Cargando recomendaciones...</div>';

    try {
        // Obtener filtros actuales
        const filters = getActiveFilters();

        // Realizar petición autenticada
        const response = await fetchAuth(`/api/plants/recommendations?page=${page}&sort=${currentSort}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(filters)
        });

        const data = await response.json();

        if (data.success) {
            displayPlants(data.plants);
            updatePaginationControls(data.pagination);
        } else {
            throw new Error(data.message || 'Error al cargar las recomendaciones');
        }
    } catch (error) {
        plantsContainer.innerHTML = `
            <div class="error-state">
                <span class="material-icons">error_outline</span>
                <h3>No se pudieron cargar las recomendaciones</h3>
                <p>${error.message}</p>
                <button onclick="loadPlantPage(${page})" class="btn btn-primary">
                    <span class="material-icons">refresh</span>
                    Reintentar
                </button>
            </div>
        `;
    }
}

// Función para guardar una planta
async function savePlant(plantId, buttonElement) {
    try {
        const response = await fetchAuth(`/api/plants/${plantId}/save`, {
            method: 'POST'
        });

        const data = await response.json();

        if (data.success) {
            // Actualizar UI
            const icon = buttonElement.querySelector('.material-icons');
            icon.textContent = 'bookmark';
            buttonElement.textContent = '';
            buttonElement.appendChild(icon);
            buttonElement.appendChild(document.createTextNode(' Guardado'));
            buttonElement.classList.add('saved');
        } else {
            throw new Error(data.message || 'Error al guardar la planta');
        }
    } catch (error) {
        showErrorNotification(error.message);
    }
}

// Función para quitar una planta de guardados
async function unsavePlant(plantId, buttonElement) {
    try {
        const response = await fetchAuth(`/api/plants/${plantId}/unsave`, {
            method: 'POST'
        });

        const data = await response.json();

        if (data.success) {
            // Actualizar UI
            const icon = buttonElement.querySelector('.material-icons');
            icon.textContent = 'bookmark_border';
            buttonElement.textContent = '';
            buttonElement.appendChild(icon);
            buttonElement.appendChild(document.createTextNode(' Guardar'));
            buttonElement.classList.remove('saved');
        } else {
            throw new Error(data.message || 'Error al quitar la planta de guardados');
        }
    } catch (error) {
        showErrorNotification(error.message);
    }
}