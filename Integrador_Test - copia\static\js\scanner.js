/**
 * PlantCare - Scanner de Plantas IA
 * JavaScript modernizado con animaciones y efectos mejorados
 */

document.addEventListener('DOMContentLoaded', function() {
    // Theme Toggle
    initializeThemeToggle();

    // User Menu Dropdown
    initializeUserMenu();

    // Scanner States and Camera
    initializeScanner();

    // Add animations for elements as they come into view
    initializeScrollAnimations();
});

/**
 * Inicializa el cambio de tema (oscuro/claro)
 */
function initializeThemeToggle() {
    const themeToggle = document.getElementById('theme-toggle');
    if (!themeToggle) return;

    const themeIcon = themeToggle.querySelector('.material-icons');

    // Check if user has a theme preference stored
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
        document.body.classList.add('dark-theme');
        themeIcon.textContent = 'light_mode';
    }

    themeToggle.addEventListener('click', function() {
        document.body.classList.toggle('dark-theme');

        if (document.body.classList.contains('dark-theme')) {
            localStorage.setItem('theme', 'dark');
            themeIcon.textContent = 'light_mode';
        } else {
            localStorage.setItem('theme', 'light');
            themeIcon.textContent = 'dark_mode';
        }
    });
}

/**
 * Inicializa el menú desplegable de usuario
 */
function initializeUserMenu() {
    const userMenuButton = document.getElementById('user-menu-button');
    const userDropdown = document.getElementById('user-dropdown');

    if (!userMenuButton || !userDropdown) return;

    userMenuButton.addEventListener('click', function() {
        userDropdown.classList.toggle('active');
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        if (!userMenuButton.contains(event.target) && !userDropdown.contains(event.target)) {
            userDropdown.classList.remove('active');
        }
    });

    // Logout Button
    const logoutButton = document.getElementById('logout-button');

    if (logoutButton) {
        logoutButton.addEventListener('click', function(e) {
            e.preventDefault();

            if (confirm('¿Estás seguro de que deseas cerrar sesión?')) {
                // In a real app, this would log the user out via a server request
                // For now just redirect to the URL in the href
                window.location.href = logoutButton.getAttribute('href');
            }
        });
    }
}

/**
 * Inicializa el escáner y los estados de la cámara
 */
function initializeScanner() {
    // Scanner States
    const states = {
        initial: document.getElementById('initial-state'),
        camera: document.getElementById('camera-state'),
        preview: document.getElementById('preview-state'),
        loading: document.getElementById('loading-state'),
        result: document.getElementById('result-state'),
        error: document.getElementById('error-state')
    };

    // Check if we're on the scanner page by verifying the existence of scanner states
    if (!states.initial) return;

    // Camera Elements
    const cameraFeed = document.getElementById('camera-feed');
    const photoCanvas = document.getElementById('photo-canvas');
    const previewImage = document.getElementById('preview-image');
    const photoUpload = document.getElementById('photo-upload');

    // Buttons
    const takePhotoBtn = document.getElementById('take-photo-btn');
    const uploadPhotoBtn = document.getElementById('upload-photo-btn');
    const captureBtn = document.getElementById('capture-btn');
    const cancelCameraBtn = document.getElementById('cancel-camera-btn');
    const switchCameraBtn = document.getElementById('switch-camera-btn');
    const retakeBtn = document.getElementById('retake-btn');
    const analyzeBtn = document.getElementById('analyze-btn');
    const newScanBtn = document.getElementById('new-scan-btn');
    const tryAgainBtn = document.getElementById('try-again-btn');

    // Camera Stream
    let stream = null;
    let facingMode = 'environment'; // Start with back camera

    /**
     * Show a specific state with animation
     * @param {string} stateName - The name of the state to show
     */
    function showState(stateName) {
        // Add slide out animation to current active state
        Object.values(states).forEach(state => {
            if (state.classList.contains('active')) {
                state.style.animation = 'fadeOut 0.3s forwards';

                // Wait for animation to complete before hiding
                setTimeout(() => {
                    state.classList.remove('active');
                    state.style.animation = '';
                }, 300);
            }
        });

        // Add slide in animation to new state after a brief delay
        setTimeout(() => {
            states[stateName].classList.add('active');
            states[stateName].style.animation = 'fadeIn 0.5s forwards';

            // Animate loading indicators when showing loading state
            if (stateName === 'loading') {
                simulateLoadingProcess();
            }

            // Focus on specific elements based on state
            if (stateName === 'camera') {
                // Nothing to focus, but we could add camera-specific init here
            } else if (stateName === 'preview') {
                analyzeBtn.focus();
            }
        }, 300);
    }

    /**
     * Simulate the loading process for demonstration
     */
    function simulateLoadingProcess() {
        const steps = document.querySelectorAll('.processing-step');

        // Reset all steps
        steps.forEach(step => step.classList.remove('active'));

        // Activate first step immediately
        steps[0].classList.add('active');

        // Activate subsequent steps with delays
        setTimeout(() => {
            steps[0].classList.remove('active');
            steps[1].classList.add('active');
        }, 1500);

        setTimeout(() => {
            steps[1].classList.remove('active');
            steps[2].classList.add('active');
        }, 3000);
    }

    /**
     * Initialize camera
     */
    async function initCamera() {
        try {
            // Add loading indicator to button
            takePhotoBtn.disabled = true;
            takePhotoBtn.innerHTML = '<span class="material-icons loading-icon">sync</span> Accediendo...';

            // Stop any existing stream
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
            }

            // Get camera stream
            stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    facingMode: facingMode,
                    width: { ideal: 1280 },
                    height: { ideal: 720 }
                },
                audio: false
            });

            // Connect stream to video element
            cameraFeed.srcObject = stream;

            // Wait for video to be ready
            cameraFeed.onloadedmetadata = function() {
                // Reset button state
                takePhotoBtn.disabled = false;
                takePhotoBtn.innerHTML = '<span class="material-icons">add_a_photo</span> Tomar Foto';

                // Show camera state
                showState('camera');
            };
        } catch (error) {
            console.error('Error accessing camera:', error);

            // Reset button state
            takePhotoBtn.disabled = false;
            takePhotoBtn.innerHTML = '<span class="material-icons">add_a_photo</span> Tomar Foto';

            showErrorState('No se pudo acceder a la cámara. Por favor, verifica que has dado los permisos necesarios.');
        }
    }

    /**
     * Capture photo from camera
     */
    function capturePhoto() {
        const context = photoCanvas.getContext('2d');

        // Add camera shutter effect
        const shutterEffect = document.createElement('div');
        shutterEffect.style.position = 'absolute';
        shutterEffect.style.top = '0';
        shutterEffect.style.left = '0';
        shutterEffect.style.width = '100%';
        shutterEffect.style.height = '100%';
        shutterEffect.style.backgroundColor = 'white';
        shutterEffect.style.opacity = '0';
        shutterEffect.style.transition = 'opacity 0.1s ease';
        shutterEffect.style.pointerEvents = 'none';
        document.querySelector('.camera-container').appendChild(shutterEffect);

        // Trigger shutter effect
        setTimeout(() => {
            shutterEffect.style.opacity = '0.8';
            setTimeout(() => {
                shutterEffect.style.opacity = '0';
                setTimeout(() => {
                    shutterEffect.remove();
                }, 100);
            }, 100);
        }, 0);

        // Set canvas dimensions to match video
        photoCanvas.width = cameraFeed.videoWidth;
        photoCanvas.height = cameraFeed.videoHeight;

        // Draw video frame to canvas
        context.drawImage(cameraFeed, 0, 0, photoCanvas.width, photoCanvas.height);

        // Convert canvas to data URL
        const photoDataUrl = photoCanvas.toDataURL('image/jpeg');

        // Display in preview
        previewImage.src = photoDataUrl;

        // Stop camera stream
        if (stream) {
            stream.getTracks().forEach(track => track.stop());
            stream = null;
        }

        // Show preview state
        showState('preview');
    }

    /**
     * Handle file upload
     * @param {Event} event - The file input change event
     */
    function handleFileUpload(event) {
        const file = event.target.files[0];

        if (file && file.type.startsWith('image/')) {
            // Add loading effect to upload button
            uploadPhotoBtn.disabled = true;
            uploadPhotoBtn.innerHTML = '<span class="material-icons loading-icon">sync</span> Cargando...';

            const reader = new FileReader();

            reader.onload = function(e) {
                previewImage.src = e.target.result;

                // Reset button
                uploadPhotoBtn.disabled = false;
                uploadPhotoBtn.innerHTML = '<span class="material-icons">upload</span> Subir Foto';

                showState('preview');
            };

            reader.readAsDataURL(file);
        } else {
            // Reset input value to allow selecting the same file again
            photoUpload.value = '';

            showErrorState('El archivo seleccionado no es una imagen válida. Por favor, selecciona una imagen en formato JPG, PNG o GIF.');
        }
    }

    /**
     * Analyze the image
     */
    function analyzeImage() {
        // Disable analyze button during processing
        analyzeBtn.disabled = true;
        analyzeBtn.innerHTML = '<span class="material-icons loading-icon">sync</span> Analizando...';

        // Show loading state
        showState('loading');

        // Create a FormData object to send the image
        const formData = new FormData();

        // Convert base64 image to blob
        const base64Data = previewImage.src.split(',')[1];
        const blob = base64ToBlob(base64Data, 'image/jpeg');
        formData.append('file', blob, 'plant_image.jpg');

        // Get CSRF token from meta tag
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        // Send the image to the server for analysis
        fetch('/diagnosis/upload', {
            method: 'POST',
            headers: {
                'X-CSRFToken': csrfToken
            },
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Error en la respuesta del servidor');
            }
            return response.json();
        })
        .then(data => {
            // Reset button state
            analyzeBtn.disabled = false;
            analyzeBtn.innerHTML = '<span class="material-icons">search</span> Identificar planta';

            if (data.success) {
                // Redirect to the result page
                window.location.href = `/diagnosis/result/${data.filename}`;
            } else {
                showErrorState(data.error || 'No pudimos identificar una planta en esta imagen. Intenta con una foto más clara o desde otro ángulo.');
            }
        })
        .catch(error => {
            console.error('Error:', error);

            // Reset button state
            analyzeBtn.disabled = false;
            analyzeBtn.innerHTML = '<span class="material-icons">search</span> Identificar planta';

            showErrorState('Ocurrió un error al analizar la imagen. Por favor, intenta de nuevo.');
        });
    }

    /**
     * Convert base64 to Blob
     * @param {string} base64 - Base64 string
     * @param {string} contentType - MIME type
     * @returns {Blob} - Blob object
     */
    function base64ToBlob(base64, contentType) {
        const byteCharacters = atob(base64);
        const byteArrays = [];

        for (let offset = 0; offset < byteCharacters.length; offset += 512) {
            const slice = byteCharacters.slice(offset, offset + 512);

            const byteNumbers = new Array(slice.length);
            for (let i = 0; i < slice.length; i++) {
                byteNumbers[i] = slice.charCodeAt(i);
            }

            const byteArray = new Uint8Array(byteNumbers);
            byteArrays.push(byteArray);
        }

        return new Blob(byteArrays, { type: contentType });
    }

    /**
     * Show result state with plant data
     */
    function showResultState() {
        // Set plant image (using the same image from preview for demo)
        document.getElementById('result-plant-image').src = previewImage.src;

        // Set confidence level (random between 70% and 95% for demo)
        const confidence = Math.floor(Math.random() * 26) + 70;
        document.getElementById('confidence-level').style.width = `${confidence}%`;
        document.getElementById('confidence-percentage').textContent = `${confidence}% de coincidencia`;

        // Plant data (using Agave for demo)
        document.getElementById('result-plant-name').textContent = 'Agave';
        document.getElementById('result-plant-scientific').textContent = 'Agave americana';
        document.getElementById('result-water-needs').textContent = 'Riego escaso, una vez cada 2-3 semanas';
        document.getElementById('result-sun-needs').textContent = 'Pleno sol, mínimo 6 horas diarias';
        document.getElementById('result-temp-needs').textContent = '10°C - 35°C';
        document.getElementById('result-soil-needs').textContent = 'Bien drenado, arenoso';
        document.getElementById('result-native').textContent = 'Nativa de Chihuahua';
        document.getElementById('result-description').textContent = 'El Agave americana es una planta suculenta grande que forma una roseta de hojas gruesas y carnosas, de color verde azulado o verde grisáceo. Es resistente a la sequía y adaptada al clima árido de Chihuahua.';

        // Set link to details page - assuming plant_id=1 for demo
        const viewDetailsBtn = document.getElementById('view-details-btn');
        // The href is already set correctly in the HTML template

        // Add similar plants
        addSimilarPlants();

        // Show result state
        showState('result');
    }

    /**
     * Add similar plants to the result
     */
    function addSimilarPlants() {
        const similarPlantsContainer = document.getElementById('similar-plants-container');
        const template = document.getElementById('similar-plant-template');

        if (!similarPlantsContainer || !template) return;

        // Clear previous similar plants
        similarPlantsContainer.innerHTML = '';

        // Similar plants data (for demo)
        const similarPlants = [
            {
                id: 4,
                name: 'Yucca',
                scientific: 'Yucca filamentosa',
                image: '/assets/plants/yucca.jpg',
                match: '78%'
            },
            {
                id: 9,
                name: 'Maguey',
                scientific: 'Agave salmiana',
                image: '/assets/plants/maguey.jpg',
                match: '72%'
            },
            {
                id: 11,
                name: 'Lechuguilla',
                scientific: 'Agave lechuguilla',
                image: '/assets/plants/lechuguilla.jpg',
                match: '65%'
            }
        ];

        // Add similar plants to the container with staggered animation
        similarPlants.forEach((plant, index) => {
            const plantItem = document.importNode(template.content, true);

            const img = plantItem.querySelector('.similar-plant-image');
            img.src = plant.image;
            img.alt = plant.name;

            plantItem.querySelector('.similar-plant-name').textContent = plant.name;
            plantItem.querySelector('.similar-plant-scientific').textContent = plant.scientific;
            plantItem.querySelector('.similar-plant-match').textContent = plant.match;

            // Set link to plant details
            const viewBtn = plantItem.querySelector('.similar-view-btn');
            viewBtn.href = `/plants/detail/${plant.id}`;

            // Add click event to make entire card clickable
            const itemContainer = plantItem.querySelector('.similar-plant-item');
            itemContainer.style.animation = `slideUp 0.6s forwards ${0.1 * index}s`;
            itemContainer.style.opacity = '0';
            itemContainer.style.transform = 'translateY(20px)';

            itemContainer.addEventListener('click', (e) => {
                // Don't trigger if clicking on the view button
                if (e.target.closest('.similar-view-btn')) return;

                window.location.href = viewBtn.href;
            });

            similarPlantsContainer.appendChild(plantItem);
        });
    }

    /**
     * Show error state with custom message
     * @param {string} message - The error message to display
     */
    function showErrorState(message) {
        document.getElementById('error-message').textContent = message;
        showState('error');
    }

    // Event Listeners
    if (takePhotoBtn) {
        takePhotoBtn.addEventListener('click', initCamera);
    }

    if (uploadPhotoBtn && photoUpload) {
        uploadPhotoBtn.addEventListener('click', () => {
            photoUpload.click();
        });

        photoUpload.addEventListener('change', handleFileUpload);
    }

    if (captureBtn) {
        captureBtn.addEventListener('click', capturePhoto);
    }

    if (cancelCameraBtn) {
        cancelCameraBtn.addEventListener('click', () => {
            // Stop camera stream
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                stream = null;
            }

            showState('initial');
        });
    }

    if (switchCameraBtn) {
        switchCameraBtn.addEventListener('click', () => {
            // Toggle between front and back camera
            facingMode = facingMode === 'environment' ? 'user' : 'environment';

            // Add rotation animation to button
            switchCameraBtn.querySelector('.material-icons').style.animation = 'rotate360 0.5s forwards';
            setTimeout(() => {
                switchCameraBtn.querySelector('.material-icons').style.animation = '';
            }, 500);

            initCamera();
        });
    }

    if (retakeBtn) {
        retakeBtn.addEventListener('click', () => {
            // Go back to initial state
            showState('initial');
        });
    }

    if (analyzeBtn) {
        analyzeBtn.addEventListener('click', analyzeImage);
    }

    if (newScanBtn) {
        newScanBtn.addEventListener('click', () => {
            // Go back to initial state
            showState('initial');
        });
    }

    if (tryAgainBtn) {
        tryAgainBtn.addEventListener('click', () => {
            // Go back to initial state
            showState('initial');
        });
    }

    // Add plant button
    const addToMyPlantsBtn = document.getElementById('add-to-my-plants');
    if (addToMyPlantsBtn) {
        addToMyPlantsBtn.addEventListener('click', function() {
            // Add success animation
            this.innerHTML = '<span class="material-icons">check</span> Añadida';
            this.classList.add('success');
            this.disabled = true;

            // In a real app, this would send a request to add the plant to the user's collection
            setTimeout(() => {
                // Reset button after 2s
                this.innerHTML = '<span class="material-icons">bookmark</span> Añadir a mis plantas';
                this.classList.remove('success');
                this.disabled = false;
            }, 2000);
        });
    }

    // Check if browser supports camera
    if (takePhotoBtn) {
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            takePhotoBtn.disabled = true;
            takePhotoBtn.title = 'Tu navegador no soporta acceso a la cámara';
            takePhotoBtn.classList.add('disabled');
        }
    }

    // Add some animations for the scanner tips
    const tipItems = document.querySelectorAll('.scanner-tips li');
    tipItems.forEach((tip, index) => {
        tip.style.opacity = '0';
        tip.style.transform = 'translateY(20px)';
        tip.style.animation = `slideUp 0.6s forwards ${0.1 * index}s`;
    });
}

/**
 * Initialize scroll-based animations
 */
function initializeScrollAnimations() {
    // Elements to animate on scroll
    const elementsToAnimate = document.querySelectorAll('.scanner-card, .scanner-tips, .error-container');

    // Set initial state
    elementsToAnimate.forEach(element => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
    });

    // Function to check if element is in viewport
    function isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top <= (window.innerHeight * 0.8) &&
            rect.bottom >= 0
        );
    }

    // Function to handle scrolling animation
    function handleScrollAnimation() {
        elementsToAnimate.forEach(element => {
            if (isInViewport(element) && element.style.opacity === '0') {
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }
        });
    }

    // Initial check on page load
    setTimeout(handleScrollAnimation, 100);

    // Listen for scroll events
    window.addEventListener('scroll', handleScrollAnimation);
}

/**
 * CSS Animation for Loading Icon
 */
const styleSheet = document.createElement('style');
styleSheet.textContent = `
@keyframes rotate360 {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.loading-icon {
    animation: rotate360 1s linear infinite;
}

.btn.success {
    background-color: #4CAF50 !important;
    color: white !important;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}
`;
document.head.appendChild(styleSheet);