document.addEventListener('DOMContentLoaded', function() {
    // Obtener el ID del tema de la URL
    const urlParams = new URLSearchParams(window.location.search);
    const topicId = urlParams.get('id');
    
    if (!topicId) {
        // Redirigir al foro si no hay ID de tema
        window.location.href = 'foro.html';
        return;
    }
    
    // Cargar los datos del tema
    loadTopicDetails(topicId);
    
    // Configurar los botones de acción
    setupActionButtons();
    
    // Configurar el formulario de respuesta
    setupReplyForm();
    
    // Configurar el selector de ordenamiento
    setupSortSelector();
});

// Función para cargar los detalles del tema
async function loadTopicDetails(topicId) {
    try {
        // En un entorno real, aquí se haría una petición a la API
        // Por ahora, usamos datos de ejemplo
        const topicData = await fetchTopicData(topicId);
        
        // Actualizar la información del tema
        updateTopicInfo(topicData);
        
        // Cargar las respuestas
        loadReplies(topicData.replies);
        
        // Cargar temas relacionados
        loadRelatedTopics(topicData.relatedTopics);
        
    } catch (error) {
        console.error('Error al cargar los detalles del tema:', error);
        showErrorMessage('No se pudo cargar la información del tema. Por favor, inténtalo de nuevo más tarde.');
    }
}

// Simulación de obtención de datos del tema
async function fetchTopicData(topicId) {
    // En un entorno real, esto sería una llamada a la API
    // Por ahora, devolvemos datos de ejemplo
    
    // Simulamos un pequeño retraso para imitar una llamada a la API
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return {
        id: topicId,
        title: '¿Cómo identificar si mi Agave necesita más sol?',
        category: 'Cuidados',
        tags: ['Agave', 'Cuidados', 'Sol'],
        content: `
            <p>Hola a todos los amantes de las plantas,</p>
            
            <p>Tengo un Agave americana que compré hace aproximadamente 6 meses. Inicialmente lo coloqué en mi balcón donde recibía sol directo durante unas 4 horas al día, y parecía estar bastante bien. Sin embargo, hace un mes lo moví a una ubicación interior cerca de una ventana orientada al este porque comenzó la temporada de lluvias y no quería que se inundara.</p>
            
            <p>Desde entonces, he notado que las hojas parecen estar un poco más pálidas de lo normal, y no tan firmes como antes. No estoy segura si esto es una señal de que necesita más luz solar o si podría ser otro problema como exceso de riego (aunque solo lo riego una vez cada 2-3 semanas).</p>
            
            <p>¿Cuáles son las señales claras de que un Agave necesita más sol? ¿Y cómo puedo proporcionarle mejores condiciones de luz si no puedo devolverlo a su ubicación original por ahora?</p>
            
            <p>Adjunto algunas fotos para que puedan ver cómo se ve actualmente.</p>
            
            <img src="assets/images/forum/agave_sample.jpg" alt="Mi planta de Agave">
            
            <p>Gracias de antemano por su ayuda!</p>
        `,
        author: {
            id: 1,
            name: 'María García',
            avatar: 'assets/avatars/user1.jpg'
        },
        date: '2025-03-17T14:30:00',
        views: 42,
        likes: 5,
        helpful: 3,
        thanks: 2,
        replies: [
            {
                id: 1,
                author: {
                    id: 2,
                    name: 'Carlos Rodríguez',
                    avatar: 'assets/avatars/user2.jpg'
                },
                date: '2025-03-17T15:45:00',
                content: `
                    <p>¡Hola María!</p>
                    
                    <p>Por las fotos que compartiste, definitivamente parece que tu Agave está experimentando estrés por falta de luz. Los Agaves son plantas que necesitan mucha luz solar directa, idealmente 6 o más horas al día.</p>
                    
                    <p>Las señales típicas de que un Agave necesita más sol incluyen:</p>
                    
                    <ul>
                        <li>Hojas que se vuelven más pálidas o amarillentas</li>
                        <li>Crecimiento más lento o estancado</li>
                        <li>Hojas que pierden su firmeza y rigidez característica</li>
                        <li>La planta comienza a "estirarse" buscando luz (etiolación)</li>
                    </ul>
                    
                    <p>Para mejorar su situación, te recomendaría:</p>
                    
                    <ol>
                        <li>Moverla a la ventana más luminosa que tengas, preferiblemente una orientada al sur si estás en el hemisferio norte</li>
                        <li>Considera usar una lámpara de crecimiento para plantas si no tienes suficiente luz natural</li>
                        <li>Rota la planta regularmente para asegurar que todas las partes reciban luz de manera uniforme</li>
                        <li>Si puedes, sácala al exterior en días soleados sin lluvia, aunque sea por unas horas</li>
                    </ol>
                    
                    <p>¡Espero que esto ayude! Tu Agave debería recuperar su color y firmeza en unas semanas si recibe más luz.</p>
                `,
                likes: 3
            },
            {
                id: 2,
                author: {
                    id: 3,
                    name: 'Laura Martínez',
                    avatar: 'assets/avatars/user3.jpg'
                },
                date: '2025-03-17T16:20:00',
                content: `
                    <p>María, además de lo que menciona Carlos sobre la luz, también revisa que no estés regando en exceso. Los Agaves son plantas suculentas adaptadas a condiciones desérticas y el exceso de agua puede causar problemas similares.</p>
                    
                    <p>En interiores, especialmente con menos luz, las plantas utilizan menos agua. Así que tal vez necesites espaciar aún más los riegos, quizás cada 3-4 semanas en lugar de 2-3.</p>
                    
                    <p>Asegúrate de que el sustrato esté completamente seco antes de volver a regar, y que la maceta tenga buen drenaje. El amarillamiento también puede ser señal de pudrición de raíces por exceso de humedad.</p>
                    
                    <p>¡Suerte con tu Agave!</p>
                `,
                likes: 2
            },
            {
                id: 3,
                author: {
                    id: 4,
                    name: 'Alejandro Sánchez',
                    avatar: 'assets/avatars/user4.jpg'
                },
                date: '2025-03-17T18:05:00',
                content: `
                    <p>Hola María,</p>
                    
                    <p>Tengo varios Agaves en mi colección y he notado que son bastante sensibles a los cambios bruscos de ubicación. El hecho de que lo hayas movido del exterior al interior podría haber causado un shock, independientemente de la cantidad de luz.</p>
                    
                    <p>Si decides moverlo de nuevo a un lugar más luminoso, hazlo gradualmente durante varios días para que se acostumbre. Por ejemplo, ponlo en la nueva ubicación durante unas horas al día y ve aumentando el tiempo progresivamente.</p>
                    
                    <p>También ten en cuenta que los Agaves crecen muy lentamente, así que cualquier mejora que veas después de hacer cambios podría tardar varias semanas en manifestarse. ¡Paciencia!</p>
                `,
                likes: 1
            }
        ],
        relatedTopics: [
            {
                id: 2,
                title: 'Guía completa para el cultivo de nopales en casa',
                author: 'Juan Pérez',
                replies: 12
            },
            {
                id: 3,
                title: 'Problemas comunes con cactus y suculentas',
                author: 'Ana López',
                replies: 8
            },
            {
                id: 4,
                title: 'Las mejores plantas de interior para espacios con poca luz',
                author: 'Roberto Gómez',
                replies: 15
            }
        ]
    };
}

// Actualizar la información del tema
function updateTopicInfo(topicData) {
    // Actualizar título y breadcrumbs
    document.getElementById('topic-title').textContent = topicData.title;
    document.getElementById('topic-title-breadcrumb').textContent = topicData.title;
    document.getElementById('topic-category').textContent = topicData.category;
    
    // Actualizar etiquetas
    const tagsContainer = document.getElementById('topic-tags');
    tagsContainer.innerHTML = '';
    topicData.tags.forEach(tag => {
        const tagElement = document.createElement('span');
        tagElement.className = 'tag';
        tagElement.textContent = tag;
        tagsContainer.appendChild(tagElement);
    });
    
    // Actualizar información del autor
    document.getElementById('author-name').textContent = topicData.author.name;
    document.getElementById('author-avatar').src = topicData.author.avatar;
    document.getElementById('author-avatar').alt = topicData.author.name;
    
    // Formatear fecha
    const postDate = new Date(topicData.date);
    const formattedDate = postDate.toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
    document.getElementById('post-date').textContent = formattedDate;
    
    // Actualizar estadísticas
    document.getElementById('view-count').textContent = `${topicData.views} vistas`;
    document.getElementById('reply-count').textContent = `${topicData.replies.length} respuestas`;
    document.getElementById('replies-count').textContent = `(${topicData.replies.length})`;
    
    // Actualizar contenido
    document.getElementById('topic-content').innerHTML = topicData.content;
    
    // Actualizar reacciones
    document.getElementById('like-count').textContent = topicData.likes;
    document.getElementById('helpful-count').textContent = topicData.helpful;
    document.getElementById('thanks-count').textContent = topicData.thanks;
}

// Cargar las respuestas
function loadReplies(replies) {
    const repliesList = document.getElementById('replies-list');
    repliesList.innerHTML = '';
    
    if (replies.length === 0) {
        const noRepliesMessage = document.createElement('div');
        noRepliesMessage.className = 'no-replies-message';
        noRepliesMessage.textContent = 'Aún no hay respuestas. ¡Sé el primero en responder!';
        repliesList.appendChild(noRepliesMessage);
        return;
    }
    
    const replyTemplate = document.getElementById('reply-template');
    
    replies.forEach(reply => {
        const replyElement = document.importNode(replyTemplate.content, true);
        
        // Actualizar avatar y nombre del autor
        replyElement.querySelector('.author-avatar img').src = reply.author.avatar;
        replyElement.querySelector('.author-avatar img').alt = reply.author.name;
        replyElement.querySelector('.author-name').textContent = reply.author.name;
        
        // Formatear fecha
        const replyDate = new Date(reply.date);
        const formattedDate = replyDate.toLocaleDateString('es-ES', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
        replyElement.querySelector('.reply-date').textContent = formattedDate;
        
        // Actualizar contenido
        replyElement.querySelector('.reply-content').innerHTML = reply.content;
        
        // Actualizar likes
        replyElement.querySelector('.like-count').textContent = reply.likes;
        
        // Añadir evento para el botón de like
        const likeButton = replyElement.querySelector('.like-btn');
        likeButton.addEventListener('click', function() {
            this.classList.toggle('active');
            const likeCount = this.querySelector('.like-count');
            let count = parseInt(likeCount.textContent);
            
            if (this.classList.contains('active')) {
                likeCount.textContent = count + 1;
            } else {
                likeCount.textContent = count - 1;
            }
        });
        
        // Añadir evento para el botón de responder
        const replyButton = replyElement.querySelector('.reply-to-btn');
        replyButton.addEventListener('click', function() {
            const replyForm = document.getElementById('reply-content');
            replyForm.focus();
            replyForm.value = `@${reply.author.name} `;
            
            // Scroll al formulario
            replyForm.scrollIntoView({ behavior: 'smooth' });
        });
        
        repliesList.appendChild(replyElement);
    });
}

// Cargar temas relacionados
function loadRelatedTopics(relatedTopics) {
    const relatedTopicsContainer = document.getElementById('related-topics');
    relatedTopicsContainer.innerHTML = '';
    
    const relatedTopicTemplate = document.getElementById('related-topic-template');
    
    relatedTopics.forEach(topic => {
        const topicElement = document.importNode(relatedTopicTemplate.content, true);
        
        const linkElement = topicElement.querySelector('a');
        linkElement.href = `tema-detalle.html?id=${topic.id}`;
        linkElement.textContent = topic.title;
        
        topicElement.querySelector('.topic-author').textContent = `Por ${topic.author}`;
        topicElement.querySelector('.reply-count').textContent = `${topic.replies} respuestas`;
        
        relatedTopicsContainer.appendChild(topicElement);
    });
}

// Configurar los botones de acción
function setupActionButtons() {
    // Botón de seguir tema
    const followButton = document.getElementById('follow-topic');
    followButton.addEventListener('click', function() {
        const isFollowing = this.classList.toggle('active');
        
        if (isFollowing) {
            this.innerHTML = '<span class="material-icons">notifications_active</span> Siguiendo';
            showMessage('Ahora estás siguiendo este tema');
        } else {
            this.innerHTML = '<span class="material-icons">notifications</span> Seguir tema';
            showMessage('Has dejado de seguir este tema');
        }
    });
    
    // Botón de compartir
    const shareButton = document.getElementById('share-topic');
    shareButton.addEventListener('click', function() {
        if (navigator.share) {
            navigator.share({
                title: document.getElementById('topic-title').textContent,
                text: 'Mira este tema en el foro de PlantCare',
                url: window.location.href
            })
            .catch(error => console.log('Error al compartir:', error));
        } else {
            // Fallback para navegadores que no soportan Web Share API
            prompt('Copia este enlace para compartir:', window.location.href);
        }
    });
    
    // Botones de reacción
    const reactionButtons = document.querySelectorAll('.topic-reactions .reaction-btn');
    reactionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const isActive = this.classList.toggle('active');
            const countElement = this.querySelector('span:last-child');
            let count = parseInt(countElement.textContent);
            
            if (isActive) {
                countElement.textContent = count + 1;
            } else {
                countElement.textContent = count - 1;
            }
        });
    });
}

// Configurar el formulario de respuesta
function setupReplyForm() {
    const replyForm = document.querySelector('.reply-form');
    const submitButton = document.getElementById('submit-reply');
    const cancelButton = document.getElementById('cancel-reply');
    const replyContent = document.getElementById('reply-content');
    
    submitButton.addEventListener('click', function() {
        if (replyContent.value.trim() === '') {
            showErrorMessage('Por favor, escribe una respuesta antes de enviar');
            return;
        }
        
        // En un entorno real, aquí se enviaría la respuesta a la API
        // Por ahora, simulamos una respuesta exitosa
        showMessage('Tu respuesta ha sido enviada correctamente');
        
        // Limpiar el formulario
        replyContent.value = '';
        
        // Simular la adición de la nueva respuesta
        const newReply = {
            id: Math.floor(Math.random() * 1000),
            author: {
                id: 999,
                name: 'Tú',
                avatar: 'assets/avatars/default.jpg'
            },
            date: new Date().toISOString(),
            content: '<p>Tu respuesta ha sido añadida. En un entorno real, aquí aparecería el contenido de tu respuesta.</p>',
            likes: 0
        };
        
        // Añadir la nueva respuesta a la lista
        const repliesList = document.getElementById('replies-list');
        const replyTemplate = document.getElementById('reply-template');
        const replyElement = document.importNode(replyTemplate.content, true);
        
        replyElement.querySelector('.author-avatar img').src = newReply.author.avatar;
        replyElement.querySelector('.author-avatar img').alt = newReply.author.name;
        replyElement.querySelector('.author-name').textContent = newReply.author.name;
        
        const replyDate = new Date(newReply.date);
        const formattedDate = replyDate.toLocaleDateString('es-ES', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
        replyElement.querySelector('.reply-date').textContent = formattedDate;
        
        replyElement.querySelector('.reply-content').innerHTML = newReply.content;
        replyElement.querySelector('.like-count').textContent = newReply.likes;
        
        // Eliminar mensaje de "no hay respuestas" si existe
        const noRepliesMessage = repliesList.querySelector('.no-replies-message');
        if (noRepliesMessage) {
            repliesList.removeChild(noRepliesMessage);
        }
        
        // Añadir la nueva respuesta al principio de la lista
        repliesList.insertBefore(replyElement, repliesList.firstChild);
        
        // Actualizar contador de respuestas
        const replyCount = document.getElementById('reply-count');
        const repliesCount = document.getElementById('replies-count');
        const currentCount = parseInt(replyCount.textContent);
        replyCount.textContent = `${currentCount + 1} respuestas`;
        repliesCount.textContent = `(${currentCount + 1})`;
    });
    
    cancelButton.addEventListener('click', function() {
        replyContent.value = '';
    });
}

// Configurar el selector de ordenamiento
function setupSortSelector() {
    const sortSelector = document.getElementById('sort-replies');
    
    sortSelector.addEventListener('change', function() {
        const sortValue = this.value;
        const repliesList = document.getElementById('replies-list');
        const replies = Array.from(repliesList.children);
        
        // Si no hay respuestas o solo hay una, no hay nada que ordenar
        if (replies.length <= 1) return;
        
        // Eliminar el mensaje de "no hay respuestas" si existe
        const noRepliesMessage = repliesList.querySelector('.no-replies-message');
        if (noRepliesMessage) {
            replies.splice(replies.indexOf(noRepliesMessage), 1);
        }
        
        // Ordenar las respuestas según el criterio seleccionado
        replies.sort((a, b) => {
            if (sortValue === 'newest') {
                const dateA = new Date(a.querySelector('.reply-date').textContent);
                const dateB = new Date(b.querySelector('.reply-date').textContent);
                return dateB - dateA;
            } else if (sortValue === 'oldest') {
                const dateA = new Date(a.querySelector('.reply-date').textContent);
                const dateB = new Date(b.querySelector('.reply-date').textContent);
                return dateA - dateB;
            } else if (sortValue === 'likes') {
                const likesA = parseInt(a.querySelector('.like-count').textContent);
                const likesB = parseInt(b.querySelector('.like-count').textContent);
                return likesB - likesA;
            }
        });
        
        // Vaciar la lista y añadir las respuestas ordenadas
        repliesList.innerHTML = '';
        replies.forEach(reply => {
            repliesList.appendChild(reply);
        });
    });
}

// Función para mostrar mensajes
function showMessage(message) {
    // Crear elemento de mensaje
    const messageElement = document.createElement('div');
    messageElement.className = 'message-toast';
    messageElement.textContent = message;
    
    // Añadir al DOM
    document.body.appendChild(messageElement);
    
    // Mostrar mensaje
    setTimeout(() => {
        messageElement.classList.add('show');
    }, 10);
    
    // Ocultar y eliminar después de 3 segundos
    setTimeout(() => {
        messageElement.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(messageElement);
        }, 300);
    }, 3000);
}

// Función para mostrar mensajes de error
function showErrorMessage(message) {
    // Crear elemento de mensaje
    const messageElement = document.createElement('div');
    messageElement.className = 'message-toast error';
    messageElement.textContent = message;
    
    // Añadir al DOM
    document.body.appendChild(messageElement);
    
    // Mostrar mensaje
    setTimeout(() => {
        messageElement.classList.add('show');
    }, 10);
    
    // Ocultar y eliminar después de 5 segundos
    setTimeout(() => {
        messageElement.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(messageElement);
        }, 300);
    }, 5000);
}
