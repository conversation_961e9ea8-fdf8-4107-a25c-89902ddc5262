<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Panel de Administración - PlantCare</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 50%, #1B5E20 100%);
            min-height: 100vh;
            color: #333;
        }

        .admin-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .admin-header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .admin-header h1 {
            color: #2E7D32;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .admin-header p {
            color: #666;
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            font-size: 3rem;
            color: #4CAF50;
            margin-bottom: 15px;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2E7D32;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .admin-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .action-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .action-card:hover {
            transform: translateY(-5px);
        }

        .action-card h3 {
            color: #2E7D32;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .action-card p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            background: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn:hover {
            background: #2E7D32;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #FF9800;
        }

        .btn-secondary:hover {
            background: #F57C00;
        }

        .recent-plants {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .recent-plants h3 {
            color: #2E7D32;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }

        .plant-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 10px;
            background: #f8f9fa;
            transition: background 0.3s ease;
        }

        .plant-item:hover {
            background: #e8f5e8;
        }

        .plant-icon {
            font-size: 2rem;
            color: #4CAF50;
        }

        .plant-info h4 {
            color: #2E7D32;
            margin-bottom: 5px;
        }

        .plant-info p {
            color: #666;
            font-size: 0.9rem;
        }

        .nav-links {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            justify-content: center;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .nav-links a:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .admin-container {
                padding: 15px;
            }

            .admin-header h1 {
                font-size: 2rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .admin-actions {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Navegación -->
        <div class="nav-links">
            <a href="{{ url_for('index') }}">🏠 Inicio</a>
            <a href="{{ url_for('admin.dashboard') }}">📊 Dashboard</a>
            <a href="{{ url_for('admin.plants_list') }}">🌱 Plantas</a>
            <a href="{{ url_for('admin.users_list') }}">👥 Usuarios</a>
            <a href="{{ url_for('logout') }}">🚪 Cerrar Sesión</a>
        </div>

        <!-- Header -->
        <div class="admin-header">
            <h1>🛠️ Panel de Administración</h1>
            <p>Gestiona plantas y usuarios de PlantCare</p>
        </div>

        <!-- Estadísticas -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">🌱</div>
                <div class="stat-number">{{ stats.total_plants }}</div>
                <div class="stat-label">Plantas Totales</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-number">{{ stats.total_users }}</div>
                <div class="stat-label">Usuarios Registrados</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🏜️</div>
                <div class="stat-number">{{ stats.native_plants }}</div>
                <div class="stat-label">Plantas Nativas</div>
            </div>
        </div>

        <!-- Acciones Principales -->
        <div class="admin-actions">
            <div class="action-card">
                <h3>🌱 Gestionar Plantas</h3>
                <p>Agregar, editar o eliminar plantas de la base de datos. Incluye información detallada sobre cuidados, características y usos.</p>
                <a href="{{ url_for('admin.plants_list') }}" class="btn">
                    <span class="material-icons">eco</span>
                    Ver Plantas
                </a>
                <a href="{{ url_for('admin.add_plant_form') }}" class="btn btn-secondary">
                    <span class="material-icons">add</span>
                    Agregar Planta
                </a>
            </div>
            
            <div class="action-card">
                <h3>👥 Gestionar Usuarios</h3>
                <p>Administrar cuentas de usuario, revisar actividad y gestionar permisos de acceso a la plataforma.</p>
                <a href="{{ url_for('admin.users_list') }}" class="btn">
                    <span class="material-icons">people</span>
                    Ver Usuarios
                </a>
            </div>
        </div>

        <!-- Plantas Recientes -->
        <!-- Plantas Recientes -->
        {% if stats.recent_plants %}
        <div class="recent-plants">
            <h3>🌿 Plantas Agregadas Recientemente</h3>
            {% for plant in stats.recent_plants %}
            <div class="plant-item">
                <div class="plant-icon">🌱</div>
                <div class="plant-info">
                    <h4>{{ plant.Nombre }}</h4>
                    <p><em>{{ plant.NombreCientifico }}</em> - {{ plant.NombreComun or 'Sin nombre común' }}</p>
                </div>
            </div>
            {% endfor %}
        </div>
        {% endif %}
    </div>
</body>
</html>
