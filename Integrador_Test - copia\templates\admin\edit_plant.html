<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editar Planta - Administración PlantCare</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 50%, #1B5E20 100%);
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }

        .form-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .form-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .form-header h1 {
            color: #2E7D32;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .form-header p {
            color: #666;
            font-size: 1.1rem;
        }

        .nav-links {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            justify-content: center;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .nav-links a:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2E7D32;
            font-weight: 500;
            font-size: 1rem;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            font-family: 'Roboto', sans-serif;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.2);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin: 0;
        }

        .form-section {
            margin-bottom: 40px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            border-left: 4px solid #4CAF50;
        }

        .form-section h3 {
            color: #2E7D32;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 40px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #4CAF50;
            color: white;
        }

        .btn-primary:hover {
            background: #2E7D32;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #f5f5f5;
            color: #666;
        }

        .btn-secondary:hover {
            background: #e0e0e0;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .required {
            color: #dc3545;
        }

        @media (max-width: 768px) {
            .form-container {
                padding: 20px;
                margin: 10px;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .form-header h1 {
                font-size: 2rem;
            }

            .form-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Navegación -->
    <div class="nav-links">
        <a href="{{ url_for('admin.dashboard') }}">📊 Dashboard</a>
        <a href="{{ url_for('admin.plants_list') }}">🌱 Lista de Plantas</a>
        <a href="{{ url_for('index') }}">🏠 Inicio</a>
    </div>

    <div class="form-container">
        <!-- Header -->
        <div class="form-header">
            <h1>✏️ Editar Planta</h1>
            <p>Modifica la información de: <strong>{{ plant.Nombre }}</strong></p>
        </div>

        <!-- Mensajes Flash -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'danger' else 'success' }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Formulario -->
        <form method="POST" enctype="multipart/form-data">
            <!-- Información Básica -->
            <div class="form-section">
                <h3>📝 Información Básica</h3>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="nombre">Nombre <span class="required">*</span></label>
                        <input type="text" id="nombre" name="nombre" required 
                               value="{{ form_data.Nombre if form_data else plant.Nombre }}"
                               placeholder="Ej: Agave Americana">
                    </div>
                    <div class="form-group">
                        <label for="nombre_cientifico">Nombre Científico <span class="required">*</span></label>
                        <input type="text" id="nombre_cientifico" name="nombre_cientifico" required 
                               value="{{ form_data.NombreCientifico if form_data else plant.NombreCientifico }}"
                               placeholder="Ej: Agave americana">
                    </div>
                    <div class="form-group">
                        <label for="nombre_comun">Nombre Común</label>
                        <input type="text" id="nombre_comun" name="nombre_comun" 
                               value="{{ form_data.NombreComun if form_data else plant.NombreComun }}"
                               placeholder="Ej: Maguey, Pita">
                    </div>
                    <div class="form-group">
                        <label for="imagen">URL de Imagen</label>
                        <input type="url" id="imagen" name="imagen" 
                               value="{{ form_data.Imagen if form_data else plant.Imagen }}"
                               placeholder="/static/images/plants/planta.jpg">
                    </div>
                </div>
                <div class="form-group full-width">
                    <label for="descripcion">Descripción</label>
                    <textarea id="descripcion" name="descripcion" rows="4" 
                              placeholder="Descripción detallada de la planta...">{{ form_data.Descripcion if form_data else plant.Descripcion }}</textarea>
                </div>
            </div>

            <!-- Clasificación -->
            <div class="form-section">
                <h3>🏷️ Clasificación</h3>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="tipo_planta_id">Tipo de Planta</label>
                        <select id="tipo_planta_id" name="tipo_planta_id">
                            <option value="1" {{ 'selected' if (form_data and form_data.TipoPlantaID == 1) or (not form_data and plant.TipoPlantaID == 1) else '' }}>Cactus</option>
                            <option value="2" {{ 'selected' if (form_data and form_data.TipoPlantaID == 2) or (not form_data and plant.TipoPlantaID == 2) else '' }}>Suculenta</option>
                            <option value="3" {{ 'selected' if (form_data and form_data.TipoPlantaID == 3) or (not form_data and plant.TipoPlantaID == 3) else '' }}>Arbusto</option>
                            <option value="4" {{ 'selected' if (form_data and form_data.TipoPlantaID == 4) or (not form_data and plant.TipoPlantaID == 4) else '' }}>Árbol</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="familia_botanica_id">Familia Botánica</label>
                        <select id="familia_botanica_id" name="familia_botanica_id">
                            <option value="1" {{ 'selected' if (form_data and form_data.FamiliaBotanicaID == 1) or (not form_data and plant.FamiliaBotanicaID == 1) else '' }}>Cactaceae</option>
                            <option value="2" {{ 'selected' if (form_data and form_data.FamiliaBotanicaID == 2) or (not form_data and plant.FamiliaBotanicaID == 2) else '' }}>Agavaceae</option>
                            <option value="3" {{ 'selected' if (form_data and form_data.FamiliaBotanicaID == 3) or (not form_data and plant.FamiliaBotanicaID == 3) else '' }}>Crassulaceae</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="es_nativa" name="es_nativa" 
                               {{ 'checked' if (form_data and form_data.EsNativa) or (not form_data and plant.EsNativa) else '' }}>
                        <label for="es_nativa">Es planta nativa de Chihuahua</label>
                    </div>
                </div>
                <div class="form-group">
                    <label for="region_nativa">Región Nativa</label>
                    <input type="text" id="region_nativa" name="region_nativa" 
                           value="{{ form_data.RegionNativa if form_data else plant.RegionNativa }}"
                           placeholder="Ej: Desierto de Chihuahua">
                </div>
            </div>

            <!-- Botones de Acción -->
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    <span class="material-icons">save</span>
                    Actualizar Planta
                </button>
                <a href="{{ url_for('admin.plants_list') }}" class="btn btn-secondary">
                    <span class="material-icons">cancel</span>
                    Cancelar
                </a>
            </div>
        </form>
    </div>
</body>
</html>
