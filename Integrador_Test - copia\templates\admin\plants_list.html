<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lista de Plantas - Administración PlantCare</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 50%, #1B5E20 100%);
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            color: #2E7D32;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .nav-links {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            justify-content: center;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .nav-links a:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .actions-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .search-box {
            display: flex;
            align-items: center;
            gap: 10px;
            flex: 1;
            max-width: 400px;
        }

        .search-box input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .search-box input:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.2);
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #4CAF50;
            color: white;
        }

        .btn-primary:hover {
            background: #2E7D32;
            transform: translateY(-2px);
        }

        .btn-danger {
            background: #f44336;
            color: white;
            padding: 8px 16px;
            font-size: 14px;
        }

        .btn-danger:hover {
            background: #d32f2f;
        }

        .btn-edit {
            background: #FF9800;
            color: white;
            padding: 8px 16px;
            font-size: 14px;
        }

        .btn-edit:hover {
            background: #F57C00;
        }

        .plants-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }

        .plant-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .plant-card:hover {
            transform: translateY(-5px);
        }

        .plant-header {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            margin-bottom: 20px;
        }

        .plant-icon {
            font-size: 3rem;
            color: #4CAF50;
        }

        .plant-info h3 {
            color: #2E7D32;
            font-size: 1.3rem;
            margin-bottom: 5px;
        }

        .plant-info .scientific-name {
            color: #666;
            font-style: italic;
            margin-bottom: 5px;
        }

        .plant-info .common-name {
            color: #888;
            font-size: 0.9rem;
        }

        .plant-details {
            margin-bottom: 20px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .detail-label {
            font-weight: 500;
            color: #555;
        }

        .detail-value {
            color: #777;
        }

        .plant-badges {
            display: flex;
            gap: 8px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .badge {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .badge-native {
            background: #e8f5e9;
            color: #2e7d32;
        }

        .badge-difficulty {
            background: #fff3e0;
            color: #f57c00;
        }

        .badge-toxicity {
            background: #ffebee;
            color: #c62828;
        }

        .plant-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .empty-state .icon {
            font-size: 4rem;
            color: #ccc;
            margin-bottom: 20px;
        }

        .empty-state h3 {
            color: #666;
            margin-bottom: 10px;
        }

        .empty-state p {
            color: #888;
            margin-bottom: 30px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .actions-bar {
                flex-direction: column;
                gap: 15px;
            }

            .search-box {
                max-width: 100%;
            }

            .plants-grid {
                grid-template-columns: 1fr;
            }

            .plant-actions {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Navegación -->
        <div class="nav-links">
            <a href="{{ url_for('admin.dashboard') }}">📊 Dashboard</a>
            <a href="{{ url_for('admin.add_plant_form') }}">➕ Agregar Planta</a>
            <a href="{{ url_for('admin.users_list') }}">👥 Usuarios</a>
            <a href="{{ url_for('index') }}">🏠 Inicio</a>
        </div>

        <!-- Header -->
        <div class="header">
            <h1>🌱 Gestión de Plantas</h1>
            <p>{{ plants|length }} plantas registradas en el sistema</p>
        </div>

        <!-- Mensajes Flash -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'danger' else 'success' }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Barra de Acciones -->
        <div class="actions-bar">
            <div class="search-box">
                <span class="material-icons">search</span>
                <input type="text" id="searchInput" placeholder="Buscar plantas por nombre..." onkeyup="filterPlants()">
            </div>
            <a href="{{ url_for('admin.add_plant_form') }}" class="btn btn-primary">
                <span class="material-icons">add</span>
                Agregar Nueva Planta
            </a>
        </div>

        <!-- Lista de Plantas -->
        {% if plants %}
        <div class="plants-grid" id="plantsGrid">
            {% for plant in plants %}
            <div class="plant-card" data-name="{{ plant.Nombre|lower }} {{ plant.NombreCientifico|lower }} {{ plant.NombreComun|lower if plant.NombreComun }}">
                <div class="plant-header">
                    <div class="plant-icon">🌱</div>
                    <div class="plant-info">
                        <h3>{{ plant.Nombre }}</h3>
                        <div class="scientific-name">{{ plant.NombreCientifico }}</div>
                        {% if plant.NombreComun %}
                        <div class="common-name">{{ plant.NombreComun }}</div>
                        {% endif %}
                    </div>
                </div>

                <div class="plant-details">
                    <div class="detail-row">
                        <span class="detail-label">Sol:</span>
                        <span class="detail-value">{{ plant.RequerimientosSol or 'No especificado' }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Agua:</span>
                        <span class="detail-value">{{ plant.RequerimientosAgua or 'No especificado' }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Dificultad:</span>
                        <span class="detail-value">{{ plant.DificultadCuidado or 'No especificado' }}</span>
                    </div>
                </div>

                <div class="plant-badges">
                    {% if plant.EsNativa %}
                    <span class="badge badge-native">🏜️ Nativa</span>
                    {% endif %}
                    {% if plant.DificultadCuidado %}
                    <span class="badge badge-difficulty">{{ plant.DificultadCuidado }}</span>
                    {% endif %}
                    {% if plant.Toxicidad and plant.Toxicidad != 'Ninguna' %}
                    <span class="badge badge-toxicity">⚠️ {{ plant.Toxicidad }}</span>
                    {% endif %}
                </div>

                <div class="plant-actions">
                    <a href="{{ url_for('admin.edit_plant_form', plant_id=plant.PlantaID) }}" class="btn btn-edit">
                        <span class="material-icons">edit</span>
                        Editar
                    </a>
                    <form method="POST" action="{{ url_for('admin.delete_plant_action', plant_id=plant.PlantaID) }}" 
                          style="display: inline;" onsubmit="return confirm('¿Estás seguro de que deseas eliminar esta planta?')">
                        <button type="submit" class="btn btn-danger">
                            <span class="material-icons">delete</span>
                            Eliminar
                        </button>
                    </form>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="empty-state">
            <div class="icon">🌱</div>
            <h3>No hay plantas registradas</h3>
            <p>Comienza agregando la primera planta al sistema</p>
            <a href="{{ url_for('admin.add_plant_form') }}" class="btn btn-primary">
                <span class="material-icons">add</span>
                Agregar Primera Planta
            </a>
        </div>
        {% endif %}
    </div>

    <script>
        function filterPlants() {
            const searchInput = document.getElementById('searchInput');
            const filter = searchInput.value.toLowerCase();
            const plantsGrid = document.getElementById('plantsGrid');
            const plantCards = plantsGrid.getElementsByClassName('plant-card');

            for (let i = 0; i < plantCards.length; i++) {
                const plantName = plantCards[i].getAttribute('data-name');
                if (plantName.includes(filter)) {
                    plantCards[i].style.display = '';
                } else {
                    plantCards[i].style.display = 'none';
                }
            }
        }
    </script>
</body>
</html>
