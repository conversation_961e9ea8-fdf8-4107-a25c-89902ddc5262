<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lista de Usuarios - Administración PlantCare</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 50%, #1B5E20 100%);
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            color: #2E7D32;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .nav-links {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            justify-content: center;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .nav-links a:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .users-table-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow-x: auto;
        }

        .users-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .users-table th,
        .users-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }

        .users-table th {
            background: #f8f9fa;
            color: #2E7D32;
            font-weight: 600;
            position: sticky;
            top: 0;
        }

        .users-table tr:hover {
            background: #f8f9fa;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #4CAF50;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 10px;
        }

        .user-info {
            display: flex;
            align-items: center;
        }

        .user-details h4 {
            margin: 0;
            color: #2E7D32;
        }

        .user-details p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }

        .badge {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .badge-admin {
            background: #e3f2fd;
            color: #1976d2;
        }

        .badge-user {
            background: #e8f5e9;
            color: #2e7d32;
        }

        .badge-active {
            background: #e8f5e9;
            color: #2e7d32;
        }

        .badge-inactive {
            background: #ffebee;
            color: #c62828;
        }

        .stats-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #2E7D32;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .search-box {
            margin-bottom: 20px;
        }

        .search-box input {
            width: 100%;
            max-width: 400px;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .search-box input:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.2);
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
        }

        .empty-state .icon {
            font-size: 4rem;
            color: #ccc;
            margin-bottom: 20px;
        }

        .empty-state h3 {
            color: #666;
            margin-bottom: 10px;
        }

        .empty-state p {
            color: #888;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .users-table-container {
                padding: 15px;
            }

            .users-table th,
            .users-table td {
                padding: 10px 8px;
                font-size: 0.9rem;
            }

            .user-avatar {
                width: 30px;
                height: 30px;
                font-size: 0.8rem;
            }

            .stats-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Navegación -->
        <div class="nav-links">
            <a href="{{ url_for('admin.dashboard') }}">📊 Dashboard</a>
            <a href="{{ url_for('admin.plants_list') }}">🌱 Plantas</a>
            <a href="{{ url_for('admin.add_plant_form') }}">➕ Agregar Planta</a>
            <a href="{{ url_for('index') }}">🏠 Inicio</a>
        </div>

        <!-- Header -->
        <div class="header">
            <h1>👥 Gestión de Usuarios</h1>
            <p>{{ users|length }} usuarios registrados en el sistema</p>
        </div>

        <!-- Estadísticas -->
        <div class="stats-row">
            <div class="stat-card">
                <div class="stat-number">{{ users|length }}</div>
                <div class="stat-label">Total Usuarios</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ users|selectattr('IsAdmin')|list|length }}</div>
                <div class="stat-label">Administradores</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ users|selectattr('Activo')|list|length }}</div>
                <div class="stat-label">Usuarios Activos</div>
            </div>
        </div>

        <!-- Tabla de Usuarios -->
        {% if users %}
        <div class="users-table-container">
            <div class="search-box">
                <input type="text" id="searchInput" placeholder="Buscar usuarios por nombre o email..." onkeyup="filterUsers()">
            </div>

            <table class="users-table" id="usersTable">
                <thead>
                    <tr>
                        <th>Usuario</th>
                        <th>Email</th>
                        <th>Rol</th>
                        <th>Estado</th>
                        <th>Fecha Registro</th>
                        <th>Último Acceso</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr data-search="{{ (user.Nombre + ' ' + user.Apellido + ' ' + user.Email + ' ' + user.Username)|lower }}">
                        <td>
                            <div class="user-info">
                                <div class="user-avatar">
                                    {{ user.Nombre[0]|upper }}{{ user.Apellido[0]|upper if user.Apellido else '' }}
                                </div>
                                <div class="user-details">
                                    <h4>{{ user.Nombre }} {{ user.Apellido }}</h4>
                                    <p>@{{ user.Username }}</p>
                                </div>
                            </div>
                        </td>
                        <td>{{ user.Email }}</td>
                        <td>
                            {% if user.IsAdmin or user.RolID == 1 %}
                                <span class="badge badge-admin">👑 Administrador</span>
                            {% else %}
                                <span class="badge badge-user">👤 Usuario</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if user.Activo %}
                                <span class="badge badge-active">✅ Activo</span>
                            {% else %}
                                <span class="badge badge-inactive">❌ Inactivo</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if user.FechaRegistro %}
                                {{ user.FechaRegistro[:10] }}
                            {% else %}
                                No disponible
                            {% endif %}
                        </td>
                        <td>
                            {% if user.UltimoAcceso %}
                                {{ user.UltimoAcceso[:10] }}
                            {% else %}
                                Nunca
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="users-table-container">
            <div class="empty-state">
                <div class="icon">👥</div>
                <h3>No hay usuarios registrados</h3>
                <p>Los usuarios aparecerán aquí cuando se registren en la plataforma</p>
            </div>
        </div>
        {% endif %}
    </div>

    <script>
        function filterUsers() {
            const searchInput = document.getElementById('searchInput');
            const filter = searchInput.value.toLowerCase();
            const table = document.getElementById('usersTable');
            const rows = table.getElementsByTagName('tr');

            for (let i = 1; i < rows.length; i++) { // Empezar desde 1 para saltar el header
                const searchData = rows[i].getAttribute('data-search');
                if (searchData && searchData.includes(filter)) {
                    rows[i].style.display = '';
                } else {
                    rows[i].style.display = 'none';
                }
            }
        }
    </script>
</body>
</html>
