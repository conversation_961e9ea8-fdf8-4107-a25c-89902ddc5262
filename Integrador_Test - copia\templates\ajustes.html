<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ajustes - PlantCare</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/home.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/user-menu.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/ajustes.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/navbar.css') }}">
</head>
<body>
    <header class="app-header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <span class="material-icons">local_florist</span>
                    <h1>PlantCare</h1>
                </div>
                <nav class="main-nav">
                    <ul>
                        <li><a href="/">Inicio</a></li>
                        <li><a href="/biblioteca">Biblioteca</a></li>
                        <li><a href="/diagnosis/scanner">Scanner</a></li>
                        <li><a href="/calendario">Calendario</a></li>
                        <li><a href="/foro">Foro</a></li>
                    </ul>
                </nav>
                <div class="user-actions">
                    <div class="user-menu">
                        <button id="user-menu-button" class="avatar">
                            <img src="{{ current_user.profile_image or url_for('static', filename='assets/pfp.jpg') }}" alt="Usuario">
                        </button>
                        <div id="user-dropdown" class="dropdown-menu">
                            <a href="/perfil">
                                <span class="material-icons">person</span>
                                Mi Perfil
                            </a>
                            <a href="/ajustes" class="active">
                                <span class="material-icons">settings</span>
                                Ajustes
                            </a>
                            <a href="#" id="logout-button">
                                <span class="material-icons">logout</span>
                                Cerrar Sesión
                            </a>
                            <div class="auth-buttons">
                                <a href="/login" class="btn btn-outline login-btn">
                                    <span class="material-icons">login</span>
                                    Iniciar Sesión
                                </a>
                                <a href="/register" class="btn btn-primary register-btn">
                                    <span class="material-icons">person_add</span>
                                    Registrarse
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <main>
        <div class="container">
            <section class="page-header">
                <h1>Ajustes</h1>
                <p class="subtitle">Personaliza tu experiencia en PlantCare</p>
            </section>

            <div class="settings-container">
                <div class="settings-sidebar">
                    <nav class="settings-nav">
                        <ul>
                            <li><a href="#account" class="active"><span class="material-icons">person</span> Cuenta</a></li>
                            <li><a href="#notifications"><span class="material-icons">notifications</span> Notificaciones</a></li>
                            <li><a href="#privacy"><span class="material-icons">security</span> Privacidad</a></li>
                            <li><a href="#language"><span class="material-icons">language</span> Idioma</a></li>
                            <li><a href="#about"><span class="material-icons">info</span> Acerca de</a></li>
                        </ul>
                    </nav>
                </div>

                <div class="settings-content">
                    <div id="account" class="settings-section active">
                        <h2>Configuración de la Cuenta</h2>
                        <form id="account-form">
                            <div class="form-group">
                                <label for="name">Nombre</label>
                                <input type="text" id="name" name="name" value="{{ current_user.nombre }}">
                            </div>
                            <div class="form-group">
                                <label for="email">Correo Electrónico</label>
                                <input type="email" id="email" name="email" value="{{ current_user.email }}">
                            </div>
                            <div class="form-group">
                                <label for="location">Ubicación</label>
                                <input type="text" id="location" name="location" value="{{ current_user.ubicacion or 'Chihuahua, México' }}">
                            </div>
                            <button type="submit" class="btn btn-primary">Guardar Cambios</button>
                        </form>
                    </div>

                    <div id="notifications" class="settings-section">
                        <h2>Configuración de Notificaciones</h2>
                        <form id="notifications-form">
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="email-notifications" name="email-notifications" {% if current_user.email_notifications %}checked{% endif %}>
                                    Recibir notificaciones por correo electrónico
                                </label>
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="push-notifications" name="push-notifications" {% if current_user.push_notifications %}checked{% endif %}>
                                    Recibir notificaciones push
                                </label>
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="reminders" name="reminders" {% if current_user.reminders %}checked{% endif %}>
                                    Recordatorios de cuidado de plantas
                                </label>
                            </div>
                            <button type="submit" class="btn btn-primary">Guardar Cambios</button>
                        </form>
                    </div>

                    <div id="privacy" class="settings-section">
                        <h2>Privacidad y Seguridad</h2>
                        <form id="privacy-form">
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="private-profile" name="private-profile" {% if current_user.private_profile %}checked{% endif %}>
                                    Hacer mi perfil privado
                                </label>
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="show-location" name="show-location" {% if current_user.show_location %}checked{% endif %}>
                                    Mostrar mi ubicación en publicaciones
                                </label>
                            </div>
                            <div class="form-group">
                                <label for="password">Cambiar Contraseña</label>
                                <input type="password" id="password" name="password" placeholder="Nueva contraseña">
                            </div>
                            <button type="submit" class="btn btn-primary">Guardar Cambios</button>
                        </form>
                    </div>

                    <div id="language" class="settings-section">
                        <h2>Idioma y Región</h2>
                        <form id="language-form">
                            <div class="form-group">
                                <label for="language-select">Idioma</label>
                                <select id="language-select" name="language-select">
                                    <option value="es" {% if current_user.language == 'es' or not current_user.language %}selected{% endif %}>Español</option>
                                    <option value="en" {% if current_user.language == 'en' %}selected{% endif %}>English</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="timezone-select">Zona Horaria</label>
                                <select id="timezone-select" name="timezone-select">
                                    <option value="America/Chihuahua" {% if current_user.timezone == 'America/Chihuahua' or not current_user.timezone %}selected{% endif %}>(GMT-06:00) Chihuahua</option>
                                    <option value="America/Mexico_City" {% if current_user.timezone == 'America/Mexico_City' %}selected{% endif %}>(GMT-06:00) Ciudad de México</option>
                                    <option value="America/New_York" {% if current_user.timezone == 'America/New_York' %}selected{% endif %}>(GMT-05:00) Nueva York</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary">Guardar Cambios</button>
                        </form>
                    </div>

                    <div id="about" class="settings-section">
                        <h2>Acerca de PlantCare</h2>
                        <div class="about-content">
                            <p>PlantCare es una aplicación diseñada para ayudarte a cuidar y aprender sobre las plantas nativas de Chihuahua.</p>
                            <p>Versión: 1.0.0</p>
                            <p>Desarrollado por: Equipo PlantCare</p>
                            <div class="legal-links">
                                <a href="/#terminos">Términos de Servicio</a>
                                <a href="/#privacidad">Política de Privacidad</a>
                                <a href="/#licencias">Licencias</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="app-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="logo">
                        <span class="material-icons">local_florist</span>
                        <h2>PlantCare</h2>
                    </div>
                    <p>Tu asistente para el cuidado de plantas nativas de Chihuahua.</p>
                </div>

                <div class="footer-section">
                    <h3>Enlaces</h3>
                    <ul>
                        <li><a href="/">Inicio</a></li>
                        <li><a href="/biblioteca">Biblioteca</a></li>
                        <li><a href="/diagnosis/scanner">Scanner</a></li>
                        <li><a href="/calendario">Calendario</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Comunidad</h3>
                    <ul>
                        <!-- <li><a href="#">Foro</a></li> -->
                        <li><a href="/#blog">Blog</a></li>
                        <li><a href="/#contacto">Contacto</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Legal</h3>
                    <ul>
                        <li><a href="/#terminos">Términos de Uso</a></li>
                        <li><a href="/#privacidad">Política de Privacidad</a></li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2025 PlantCare. Todos los derechos reservados.</p>
            </div>
        </div>
    </footer>

    <script src="{{ url_for('static', filename='js/ajustes.js') }}"></script>
</body>
</html>



