<!-- templates/base.html -->
<!DOCTYPE html>
<html>
<head>
    <title>{% block title %}PlantCare{% endblock %}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    {% block styles %}{% endblock %}
</head>
<body>
    <header>
        {% include 'navbar.html' %}
    </header>

    <main>
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="flash-messages">
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }}">{{ message }}</div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}
        
        {% block content %}{% endblock %}
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2025 PlantCare. Todos los derechos reservados.</p>
            <div class="footer-links">
                <a href="/">Inicio</a>
                <a href="/biblioteca">Biblioteca</a>
                <a href="#">Política de Privacidad</a>
                <a href="#">Términos de Uso</a>
            </div>
        </div>
    </footer>

    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
