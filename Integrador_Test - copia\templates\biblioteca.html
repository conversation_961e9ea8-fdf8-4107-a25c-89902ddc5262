<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Biblioteca de Plantas - PlantCare</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="../static/css/biblioteca.css">
    <link rel="stylesheet" href="../static/css/user-menu.css">
    <link rel="stylesheet" href="../static/css/navbar.css">
    <link rel="stylesheet" href="../static/css/home-navbar.css">
    <style>
        /* Footer responsive */
        @media (max-width: 1024px) {
            footer .container > div:first-child {
                grid-template-columns: 1fr 1fr !important;
                gap: 32px !important;
            }
        }

        @media (max-width: 768px) {
            footer .container > div:first-child {
                grid-template-columns: 1fr !important;
                gap: 24px !important;
                text-align: center !important;
            }

            footer .container > div:last-child {
                flex-direction: column !important;
                text-align: center !important;
            }
        }
    </style>
</head>
<body>
    <!-- Navegación atractiva -->
    <nav class="home-navbar">
        <div class="home-navbar-container">
            <!-- Logo atractivo -->
            <a href="/" class="home-logo">
                <span class="material-icons home-logo-icon">local_florist</span>
                <h1 class="home-logo-text">PlantCare</h1>
            </a>

            <!-- Navegación central con iconos -->
            <ul class="home-nav-links">
                <li class="home-nav-item">
                    <a href="/" class="home-nav-link">
                        <span class="material-icons home-nav-icon">home</span>
                        Inicio
                    </a>
                </li>
                <li class="home-nav-item">
                    <a href="/biblioteca" class="home-nav-link active">
                        <span class="material-icons home-nav-icon">library_books</span>
                        Biblioteca
                    </a>
                </li>
                <li class="home-nav-item">
                    <a href="/scanner" class="home-nav-link">
                        <span class="material-icons home-nav-icon">photo_camera</span>
                        Scanner
                    </a>
                </li>
                <li class="home-nav-item">
                    <a href="/calendario" class="home-nav-link">
                        <span class="material-icons home-nav-icon">event</span>
                        Calendario
                    </a>
                </li>
                <li class="home-nav-item">
                    <a href="/clubes" class="home-nav-link">
                        <span class="material-icons home-nav-icon">groups</span>
                        Clubes
                    </a>
                </li>
            </ul>

            <!-- Botones de acción atractivos -->
            <div class="home-auth-buttons">
                {% if current_user.is_authenticated %}
                    <div class="user-menu">
                        <button id="user-menu-button" class="avatar">
                            <img src="{{ url_for('static', filename='assets/pfp.jpg') }}" alt="Usuario">
                        </button>
                        <div id="user-dropdown" class="dropdown-menu">
                            <a href="/perfil">
                                <span class="material-icons">person</span>
                                Mi Perfil
                            </a>
                            <a href="/ajustes">
                                <span class="material-icons">settings</span>
                                Ajustes
                            </a>
                            <a href="/login" id="logout-button">
                                <span class="material-icons">logout</span>
                                Cerrar Sesión
                            </a>
                        </div>
                    </div>
                {% else %}
                    <a href="/login" class="home-btn home-btn-outline">
                        <span class="material-icons home-btn-icon">login</span>
                        Iniciar Sesión
                    </a>
                    <a href="/register" class="home-btn home-btn-primary">
                        <span class="material-icons home-btn-icon">person_add</span>
                        Registrarse
                    </a>
                {% endif %}
            </div>
        </div>
    </nav>

    <main>
        <div class="container">
            <!-- Encabezado de página -->
            <section class="page-header">
                <h1>Biblioteca de Plantas</h1>
                <p class="subtitle">Explora nuestra colección de plantas nativas de Chihuahua</p>
            </section>

            <!-- Búsqueda y filtros -->
            <div class="search-filters">
                <div class="search-bar">
                    <span class="material-icons">search</span>
                    <input type="text" id="search-input" placeholder="Buscar por nombre, tipo o características...">
                </div>

                <div class="filters">
                    <div class="filter-group">
                        <label for="category-filter">Categoría</label>
                        <select id="category-filter">
                            <option value="">Todas</option>
                            <option value="cactus">Cactáceas</option>
                            <option value="arbustos">Arbustos</option>
                            <option value="suculentas">Suculentas</option>
                            <option value="flores">Flores</option>
                            <option value="arboles">Árboles</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="water-filter">Riego</label>
                        <select id="water-filter">
                            <option value="">Todos</option>
                            <option value="bajo">Bajo</option>
                            <option value="moderado">Moderado</option>
                            <option value="alto">Alto</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="sun-filter">Luz Solar</label>
                        <select id="sun-filter">
                            <option value="">Todas</option>
                            <option value="sombra">Sombra</option>
                            <option value="parcial">Sol parcial</option>
                            <option value="pleno">Pleno sol</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="difficulty-filter">Dificultad</label>
                        <select id="difficulty-filter">
                            <option value="">Todas</option>
                            <option value="facil">Fácil</option>
                            <option value="moderada">Moderada</option>
                            <option value="dificil">Difícil</option>
                        </select>
                    </div>

                    <button id="reset-filters" class="btn btn-outline">
                        <span class="material-icons">refresh</span>
                        Reiniciar
                    </button>
                </div>
            </div>

            <!-- Contenedor de plantas -->
            <div class="plants-container">
                <div class="plants-grid" id="plants-grid">
                    <!-- Las tarjetas de plantas se generarán dinámicamente con JavaScript -->
                </div>

                <!-- Paginación -->
                <div class="pagination">
                    <button id="prev-page" class="btn btn-outline" disabled>
                        <span class="material-icons">chevron_left</span>
                        Anterior
                    </button>
                    <div class="page-info">
                        Página <span id="current-page">1</span> de <span id="total-pages">5</span>
                    </div>
                    <button id="next-page" class="btn btn-outline">
                        Siguiente
                        <span class="material-icons">chevron_right</span>
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer Premium -->
    <footer style="background: linear-gradient(135deg, #1f2937 0%, #111827 50%, #0f172a 100%); color: white; margin-top: 80px; position: relative; overflow: hidden;">
        <!-- Patrón de fondo -->
        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: url('data:image/svg+xml,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 100 100&quot;><defs><pattern id=&quot;plant-pattern&quot; width=&quot;50&quot; height=&quot;50&quot; patternUnits=&quot;userSpaceOnUse&quot;><circle cx=&quot;25&quot; cy=&quot;25&quot; r=&quot;1&quot; fill=&quot;rgba(5,150,105,0.1)&quot;/><circle cx=&quot;10&quot; cy=&quot;10&quot; r=&quot;0.5&quot; fill=&quot;rgba(16,185,129,0.05)&quot;/><circle cx=&quot;40&quot; cy=&quot;15&quot; r=&quot;0.5&quot; fill=&quot;rgba(52,211,153,0.05)&quot;/></pattern></defs><rect width=&quot;100&quot; height=&quot;100&quot; fill=&quot;url(%23plant-pattern)&quot;/></svg>'); opacity: 0.3;"></div>

        <div class="container" style="position: relative; z-index: 1;">
            <!-- Contenido principal del footer -->
            <div style="display: grid; grid-template-columns: 2fr 1fr 1fr 1fr; gap: 48px; padding: 64px 0 48px 0;">

                <!-- Sección del logo y descripción -->
                <div style="max-width: 400px;">
                    <div style="display: flex; align-items: center; gap: 16px; margin-bottom: 24px;">
                        <div style="background: linear-gradient(135deg, #059669, #10b981); padding: 16px; border-radius: 20px; box-shadow: 0 8px 32px rgba(5, 150, 105, 0.3);">
                            <span class="material-icons" style="font-size: 32px; color: white;">local_florist</span>
                        </div>
                        <h2 style="margin: 0; font-size: 32px; font-weight: 800; background: linear-gradient(135deg, #10b981, #34d399); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">PlantCare</h2>
                    </div>
                    <p style="margin: 0 0 24px 0; font-size: 16px; line-height: 1.6; color: #d1d5db;">Tu asistente inteligente para el cuidado de plantas nativas de Chihuahua. Descubre, identifica y aprende con tecnología de vanguardia.</p>

                    <!-- Estadísticas del footer -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 24px;">
                        <div style="background: rgba(5, 150, 105, 0.1); padding: 16px; border-radius: 12px; text-align: center; border: 1px solid rgba(5, 150, 105, 0.2);">
                            <div style="font-size: 24px; font-weight: 700; color: #10b981; margin-bottom: 4px;">5.2k+</div>
                            <div style="font-size: 12px; color: #9ca3af; text-transform: uppercase; letter-spacing: 0.5px;">Usuarios Activos</div>
                        </div>
                        <div style="background: rgba(5, 150, 105, 0.1); padding: 16px; border-radius: 12px; text-align: center; border: 1px solid rgba(5, 150, 105, 0.2);">
                            <div style="font-size: 24px; font-weight: 700; color: #10b981; margin-bottom: 4px;">150+</div>
                            <div style="font-size: 12px; color: #9ca3af; text-transform: uppercase; letter-spacing: 0.5px;">Especies Nativas</div>
                        </div>
                    </div>

                    <!-- Redes sociales -->
                    <div style="display: flex; gap: 12px;">
                        <a href="#" style="background: rgba(255, 255, 255, 0.1); padding: 12px; border-radius: 12px; color: #d1d5db; text-decoration: none; transition: all 0.3s ease; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.1);" onmouseover="this.style.background='rgba(5, 150, 105, 0.2)'; this.style.color='#10b981'; this.style.transform='translateY(-2px)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'; this.style.color='#d1d5db'; this.style.transform='translateY(0)'">
                            <span class="material-icons" style="font-size: 20px;">facebook</span>
                        </a>
                        <a href="#" style="background: rgba(255, 255, 255, 0.1); padding: 12px; border-radius: 12px; color: #d1d5db; text-decoration: none; transition: all 0.3s ease; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.1);" onmouseover="this.style.background='rgba(5, 150, 105, 0.2)'; this.style.color='#10b981'; this.style.transform='translateY(-2px)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'; this.style.color='#d1d5db'; this.style.transform='translateY(0)'">
                            <span class="material-icons" style="font-size: 20px;">alternate_email</span>
                        </a>
                        <a href="#" style="background: rgba(255, 255, 255, 0.1); padding: 12px; border-radius: 12px; color: #d1d5db; text-decoration: none; transition: all 0.3s ease; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.1);" onmouseover="this.style.background='rgba(5, 150, 105, 0.2)'; this.style.color='#10b981'; this.style.transform='translateY(-2px)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'; this.style.color='#d1d5db'; this.style.transform='translateY(0)'">
                            <span class="material-icons" style="font-size: 20px;">share</span>
                        </a>
                    </div>
                </div>

                <!-- Enlaces principales -->
                <div>
                    <h3 style="margin: 0 0 24px 0; font-size: 18px; font-weight: 700; color: white; display: flex; align-items: center; gap: 8px;">
                        <span class="material-icons" style="color: #10b981;">navigation</span>
                        Enlaces
                    </h3>
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li style="margin-bottom: 12px;">
                            <a href="/" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">home</span>
                                Inicio
                            </a>
                        </li>
                        <li style="margin-bottom: 12px;">
                            <a href="/biblioteca" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">library_books</span>
                                Biblioteca
                            </a>
                        </li>
                        <li style="margin-bottom: 12px;">
                            <a href="/scanner" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">photo_camera</span>
                                Scanner
                            </a>
                        </li>
                        <li style="margin-bottom: 12px;">
                            <a href="/calendario" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">event</span>
                                Calendario
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Comunidad -->
                <div>
                    <h3 style="margin: 0 0 24px 0; font-size: 18px; font-weight: 700; color: white; display: flex; align-items: center; gap: 8px;">
                        <span class="material-icons" style="color: #10b981;">groups</span>
                        Comunidad
                    </h3>
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li style="margin-bottom: 12px;">
                            <a href="/clubes" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">diversity_3</span>
                                Clubes
                            </a>
                        </li>
                        <li style="margin-bottom: 12px;">
                            <a href="#" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">article</span>
                                Blog
                            </a>
                        </li>
                        <li style="margin-bottom: 12px;">
                            <a href="#" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">contact_support</span>
                                Contacto
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Legal -->
                <div>
                    <h3 style="margin: 0 0 24px 0; font-size: 18px; font-weight: 700; color: white; display: flex; align-items: center; gap: 8px;">
                        <span class="material-icons" style="color: #10b981;">gavel</span>
                        Legal
                    </h3>
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li style="margin-bottom: 12px;">
                            <a href="/register" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">description</span>
                                Términos de Uso
                            </a>
                        </li>
                        <li style="margin-bottom: 12px;">
                            <a href="/ajustes" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">privacy_tip</span>
                                Política de Privacidad
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Línea divisoria -->
            <div style="height: 1px; background: linear-gradient(90deg, transparent 0%, rgba(5, 150, 105, 0.3) 50%, transparent 100%); margin: 32px 0;"></div>

            <!-- Footer bottom -->
            <div style="display: flex; justify-content: space-between; align-items: center; padding-bottom: 32px; flex-wrap: wrap; gap: 16px;">
                <p style="margin: 0; color: #9ca3af; font-size: 14px;">
                    © 2025 <span style="color: #10b981; font-weight: 600;">PlantCare</span>. Todos los derechos reservados.
                </p>
                <div style="display: flex; align-items: center; gap: 16px; color: #9ca3af; font-size: 14px;">
                    <span style="display: flex; align-items: center; gap: 4px;">
                        <span class="material-icons" style="font-size: 16px; color: #10b981;">eco</span>
                        Hecho con amor para las plantas
                    </span>
                    <span style="display: flex; align-items: center; gap: 4px;">
                        <span class="material-icons" style="font-size: 16px; color: #10b981;">location_on</span>
                        Chihuahua, México
                    </span>
                </div>
            </div>
        </div>
    </footer>

    <!-- Template para las tarjetas de plantas -->
    <template id="plant-card-template">
        <div class="plant-card">
            <div class="plant-image">
                <img src="" alt="">
                <div class="plant-badges">
                    <span class="badge native-badge">Nativa</span>
                </div>
            </div>
            <div class="plant-info">
                <h3 class="plant-name"></h3>
                <p class="plant-scientific-name"></p>
                <div class="plant-attributes">
                    <div class="attribute">
                        <span class="material-icons">opacity</span>
                        <span class="water-needs"></span>
                    </div>
                    <div class="attribute">
                        <span class="material-icons">wb_sunny</span>
                        <span class="sun-needs"></span>
                    </div>
                </div>
                <a href="/biblioteca" class="btn btn-primary view-details">Ver Detalles</a>
            </div>
        </div>
    </template>

    <!-- Script para la funcionalidad de la biblioteca -->
    <script src="../static/js/biblioteca.js"></script>
    <script src="../static/js/home-navbar.js"></script>
</body>
</html>













