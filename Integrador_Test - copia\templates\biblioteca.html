<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Biblioteca de Plantas - PlantCare</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="../static/css/biblioteca.css">
    <link rel="stylesheet" href="../static/css/user-menu.css">
    <link rel="stylesheet" href="../static/css/navbar.css">
</head>
<body>
    <!-- Navegación minimalista -->
    <nav class="navbar">
        <div class="navbar-container">
            <!-- Logo -->
            <a href="/" class="navbar-brand">
                <span class="material-icons brand-icon">local_florist</span>
                <span class="brand-text">PlantCare</span>
            </a>

            <!-- Navegación central -->
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a href="/" class="nav-link">Inicio</a>
                </li>
                <li class="nav-item">
                    <a href="/biblioteca" class="nav-link active">Biblioteca</a>
                </li>
                <li class="nav-item">
                    <a href="/scanner" class="nav-link">Scanner</a>
                </li>
                <li class="nav-item">
                    <a href="/calendario" class="nav-link">Calendario</a>
                </li>
                <li class="nav-item">
                    <a href="/foro" class="nav-link">Foro</a>
                </li>
            </ul>

            <!-- Botones de acción -->
            <div class="navbar-actions">
                {% if current_user.is_authenticated %}
                    <div class="user-menu">
                        <button id="user-menu-button" class="avatar">
                            <img src="{{ url_for('static', filename='assets/pfp.jpg') }}" alt="Usuario">
                        </button>
                        <div id="user-dropdown" class="dropdown-menu">
                            <a href="/perfil">
                                <span class="material-icons">person</span>
                                Mi Perfil
                            </a>
                            <a href="/ajustes">
                                <span class="material-icons">settings</span>
                                Ajustes
                            </a>
                            <a href="/login" id="logout-button">
                                <span class="material-icons">logout</span>
                                Cerrar Sesión
                            </a>
                        </div>
                    </div>
                {% else %}
                    <a href="/login" class="btn btn-outline">
                        <span class="material-icons">login</span>
                        Iniciar Sesión
                    </a>
                    <a href="/register" class="btn btn-primary">
                        <span class="material-icons">person_add</span>
                        Registrarse
                    </a>
                {% endif %}

                <!-- Botón menú móvil -->
                <button class="mobile-menu-toggle">
                    <span class="material-icons">menu</span>
                </button>
            </div>
        </div>
    </nav>

    <main>
        <div class="container">
            <!-- Encabezado de página -->
            <section class="page-header">
                <h1>Biblioteca de Plantas</h1>
                <p class="subtitle">Explora nuestra colección de plantas nativas de Chihuahua</p>
            </section>

            <!-- Búsqueda y filtros -->
            <div class="search-filters">
                <div class="search-bar">
                    <span class="material-icons">search</span>
                    <input type="text" id="search-input" placeholder="Buscar por nombre, tipo o características...">
                </div>

                <div class="filters">
                    <div class="filter-group">
                        <label for="category-filter">Categoría</label>
                        <select id="category-filter">
                            <option value="">Todas</option>
                            <option value="cactus">Cactáceas</option>
                            <option value="arbustos">Arbustos</option>
                            <option value="suculentas">Suculentas</option>
                            <option value="flores">Flores</option>
                            <option value="arboles">Árboles</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="water-filter">Riego</label>
                        <select id="water-filter">
                            <option value="">Todos</option>
                            <option value="bajo">Bajo</option>
                            <option value="moderado">Moderado</option>
                            <option value="alto">Alto</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="sun-filter">Luz Solar</label>
                        <select id="sun-filter">
                            <option value="">Todas</option>
                            <option value="sombra">Sombra</option>
                            <option value="parcial">Sol parcial</option>
                            <option value="pleno">Pleno sol</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="difficulty-filter">Dificultad</label>
                        <select id="difficulty-filter">
                            <option value="">Todas</option>
                            <option value="facil">Fácil</option>
                            <option value="moderada">Moderada</option>
                            <option value="dificil">Difícil</option>
                        </select>
                    </div>

                    <button id="reset-filters" class="btn btn-outline">
                        <span class="material-icons">refresh</span>
                        Reiniciar
                    </button>
                </div>
            </div>

            <!-- Contenedor de plantas -->
            <div class="plants-container">
                <div class="plants-grid" id="plants-grid">
                    <!-- Las tarjetas de plantas se generarán dinámicamente con JavaScript -->
                </div>

                <!-- Paginación -->
                <div class="pagination">
                    <button id="prev-page" class="btn btn-outline" disabled>
                        <span class="material-icons">chevron_left</span>
                        Anterior
                    </button>
                    <div class="page-info">
                        Página <span id="current-page">1</span> de <span id="total-pages">5</span>
                    </div>
                    <button id="next-page" class="btn btn-outline">
                        Siguiente
                        <span class="material-icons">chevron_right</span>
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="app-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="logo">
                        <span class="material-icons">local_florist</span>
                        <h2>PlantCare</h2>
                    </div>
                    <p>Tu asistente para el cuidado de plantas nativas de Chihuahua.</p>
                </div>

                <div class="footer-section">
                    <h3>Enlaces</h3>
                    <ul>
                        <li><a href="/">Inicio</a></li>
                        <li><a href="/biblioteca">Biblioteca</a></li>
                        <li><a href="/scanner">Scanner</a></li>
                        <li><a href="/calendario">Calendario</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Comunidad</h3>
                    <ul>
                        <li><a href="/foro">Foro</a></li>
                        <li><a href="/">Blog</a></li>
                        <li><a href="/">Contacto</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Legal</h3>
                    <ul>
                        <li><a href="/register">Términos de Uso</a></li>
                        <li><a href="/ajustes">Política de Privacidad</a></li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2025 PlantCare. Todos los derechos reservados.</p>
            </div>
        </div>
    </footer>

    <!-- Template para las tarjetas de plantas -->
    <template id="plant-card-template">
        <div class="plant-card">
            <div class="plant-image">
                <img src="" alt="">
                <div class="plant-badges">
                    <span class="badge native-badge">Nativa</span>
                </div>
            </div>
            <div class="plant-info">
                <h3 class="plant-name"></h3>
                <p class="plant-scientific-name"></p>
                <div class="plant-attributes">
                    <div class="attribute">
                        <span class="material-icons">opacity</span>
                        <span class="water-needs"></span>
                    </div>
                    <div class="attribute">
                        <span class="material-icons">wb_sunny</span>
                        <span class="sun-needs"></span>
                    </div>
                </div>
                <a href="/biblioteca" class="btn btn-primary view-details">Ver Detalles</a>
            </div>
        </div>
    </template>

    <!-- Script para la funcionalidad de la biblioteca -->
    <script src="../static/js/biblioteca.js"></script>
    <script src="../static/js/navbar.js"></script>
</body>
</html>













