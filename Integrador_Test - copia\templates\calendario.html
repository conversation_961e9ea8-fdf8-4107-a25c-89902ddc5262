<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calendario de Cuidados - PlantCare</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/calendario.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/navbar.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/home-navbar.css') }}">
    <style>
        /* Footer responsive */
        @media (max-width: 1024px) {
            footer .container > div:first-child {
                grid-template-columns: 1fr 1fr !important;
                gap: 32px !important;
            }
        }

        @media (max-width: 768px) {
            footer .container > div:first-child {
                grid-template-columns: 1fr !important;
                gap: 24px !important;
                text-align: center !important;
            }

            footer .container > div:last-child {
                flex-direction: column !important;
                text-align: center !important;
            }
        }
    </style>
</head>
<body>
    <!-- Navegación atractiva -->
    <nav class="home-navbar">
        <div class="home-navbar-container">
            <!-- Logo atractivo -->
            <a href="/" class="home-logo">
                <span class="material-icons home-logo-icon">local_florist</span>
                <h1 class="home-logo-text">PlantCare</h1>
            </a>

            <!-- Navegación central con iconos -->
            <ul class="home-nav-links">
                <li class="home-nav-item">
                    <a href="/" class="home-nav-link">
                        <span class="material-icons home-nav-icon">home</span>
                        Inicio
                    </a>
                </li>
                <li class="home-nav-item">
                    <a href="/biblioteca" class="home-nav-link">
                        <span class="material-icons home-nav-icon">library_books</span>
                        Biblioteca
                    </a>
                </li>
                <li class="home-nav-item">
                    <a href="/scanner" class="home-nav-link">
                        <span class="material-icons home-nav-icon">photo_camera</span>
                        Scanner
                    </a>
                </li>
                <li class="home-nav-item">
                    <a href="/calendario" class="home-nav-link active">
                        <span class="material-icons home-nav-icon">event</span>
                        Calendario
                    </a>
                </li>
                <li class="home-nav-item">
                    <a href="/clubes" class="home-nav-link">
                        <span class="material-icons home-nav-icon">groups</span>
                        Clubes
                    </a>
                </li>
            </ul>

            <!-- Botones de acción atractivos -->
            <div class="home-auth-buttons">
                {% if current_user.is_authenticated %}
                    <div class="user-menu">
                        <button id="user-menu-button" class="avatar">
                            <img src="{{ url_for('static', filename='assets/pfp.jpg') }}" alt="Usuario">
                        </button>
                        <div id="user-dropdown" class="dropdown-menu">
                            <a href="/perfil">
                                <span class="material-icons">person</span>
                                Mi Perfil
                            </a>
                            <a href="/ajustes">
                                <span class="material-icons">settings</span>
                                Ajustes
                            </a>
                            <a href="/login" id="logout-button">
                                <span class="material-icons">logout</span>
                                Cerrar Sesión
                            </a>
                        </div>
                    </div>
                {% else %}
                    <a href="/login" class="home-btn home-btn-outline">
                        <span class="material-icons home-btn-icon">login</span>
                        Iniciar Sesión
                    </a>
                    <a href="/register" class="home-btn home-btn-primary">
                        <span class="material-icons home-btn-icon">person_add</span>
                        Registrarse
                    </a>
                {% endif %}
            </div>
        </div>
    </nav>

    <div class="container">

        <!-- Header del calendario -->
        <div class="calendar-header">
            <h1>🌱 Calendario de Cuidados</h1>
            <p>Organiza y programa el cuidado de tus plantas nativas de Chihuahua</p>
        </div>

        <!-- Calendario principal -->
        <div class="calendar-main">
            <div class="calendar-card">
                <div class="calendar-controls">
                    <button id="prev-month" class="control-btn">
                        <span class="material-icons">chevron_left</span>
                    </button>
                    <h2 id="current-month-year">Enero 2025</h2>
                    <button id="next-month" class="control-btn">
                        <span class="material-icons">chevron_right</span>
                    </button>
                </div>

                <div class="calendar-actions">
                    <button id="add-event-btn" class="add-event-btn">
                        <span class="material-icons">add</span>
                        Agregar Evento
                    </button>
                    <button id="toggle-agricultural" class="toggle-btn">
                        <span class="material-icons">agriculture</span>
                        Calendario Agrícola
                    </button>
                </div>

                <div class="calendar-grid">
                    <div class="weekdays">
                        <div class="weekday">Dom</div>
                        <div class="weekday">Lun</div>
                        <div class="weekday">Mar</div>
                        <div class="weekday">Mié</div>
                        <div class="weekday">Jue</div>
                        <div class="weekday">Vie</div>
                        <div class="weekday">Sáb</div>
                    </div>
                    <div class="days-grid" id="days-grid">
                        <!-- Los días se generarán con JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Sección de tareas y eventos -->
        <div class="tasks-section">
            <div class="tasks-grid">
                <!-- Tareas de hoy -->
                <div class="task-card">
                    <div class="task-header">
                        <h3>📅 Tareas de Hoy</h3>
                        <span class="task-count" id="today-count">3</span>
                    </div>
                    <div class="task-list" id="today-tasks">
                        <div class="task-item riego">
                            <div class="task-icon">💧</div>
                            <div class="task-info">
                                <h4>Regar Agave</h4>
                                <p>9:00 AM - Agave americana</p>
                            </div>
                            <button class="task-complete">✓</button>
                        </div>
                        <div class="task-item fertilizar">
                            <div class="task-icon">🌱</div>
                            <div class="task-info">
                                <h4>Fertilizar Nopal</h4>
                                <p>2:00 PM - Opuntia ficus-indica</p>
                            </div>
                            <button class="task-complete">✓</button>
                        </div>
                        <div class="task-item podar">
                            <div class="task-icon">✂️</div>
                            <div class="task-info">
                                <h4>Revisar Yucca</h4>
                                <p>5:00 PM - Yucca filifera</p>
                            </div>
                            <button class="task-complete">✓</button>
                        </div>
                    </div>
                </div>

                <!-- Próximas tareas -->
                <div class="task-card">
                    <div class="task-header">
                        <h3>⏰ Próximas Tareas</h3>
                        <span class="task-count" id="upcoming-count">5</span>
                    </div>
                    <div class="task-list" id="upcoming-tasks">
                        <div class="task-item riego">
                            <div class="task-icon">💧</div>
                            <div class="task-info">
                                <h4>Regar Biznaga</h4>
                                <p>Mañana - 8:00 AM</p>
                            </div>
                        </div>
                        <div class="task-item trasplantar">
                            <div class="task-icon">🪴</div>
                            <div class="task-info">
                                <h4>Trasplantar Lechuguilla</h4>
                                <p>En 3 días - 10:00 AM</p>
                            </div>
                        </div>
                        <div class="task-item fertilizar">
                            <div class="task-icon">🌱</div>
                            <div class="task-info">
                                <h4>Fertilizar Mezquite</h4>
                                <p>En 5 días - 3:00 PM</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Estadísticas -->
                <div class="stats-card">
                    <div class="task-header">
                        <h3>📊 Estadísticas del Mes</h3>
                    </div>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-icon riego">💧</div>
                            <div class="stat-info">
                                <span class="stat-number">12</span>
                                <span class="stat-label">Riegos</span>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon fertilizar">🌱</div>
                            <div class="stat-info">
                                <span class="stat-number">4</span>
                                <span class="stat-label">Fertilizaciones</span>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon podar">✂️</div>
                            <div class="stat-info">
                                <span class="stat-number">2</span>
                                <span class="stat-label">Podas</span>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon trasplantar">🪴</div>
                            <div class="stat-info">
                                <span class="stat-number">1</span>
                                <span class="stat-label">Trasplantes</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Modal para agregar eventos -->
    <div id="event-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Agregar Nuevo Evento</h3>
                <button class="close-btn" onclick="closeModal()">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <form id="event-form">
                <div class="form-group">
                    <label for="event-title">Título del Evento</label>
                    <input type="text" id="event-title" placeholder="Ej. Regar Agave" required>
                </div>
                <div class="form-group">
                    <label for="event-type">Tipo de Actividad</label>
                    <select id="event-type" required>
                        <option value="">Seleccionar tipo...</option>
                        <option value="riego">💧 Riego</option>
                        <option value="fertilizar">🌱 Fertilización</option>
                        <option value="podar">✂️ Poda</option>
                        <option value="trasplantar">🪴 Trasplante</option>
                        <option value="siembra">🌱 Siembra</option>
                        <option value="cosecha">🌾 Cosecha</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="event-plant">Planta</label>
                    <input type="text" id="event-plant" placeholder="Ej. Agave americana">
                </div>
                <div class="form-group">
                    <label for="event-date">Fecha</label>
                    <input type="date" id="event-date" required>
                </div>
                <div class="form-group">
                    <label for="event-time">Hora</label>
                    <input type="time" id="event-time">
                </div>
                <div class="form-group">
                    <label for="event-notes">Notas</label>
                    <textarea id="event-notes" rows="3" placeholder="Notas adicionales..."></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-cancel" onclick="closeModal()">Cancelar</button>
                    <button type="submit" class="btn-save">Guardar Evento</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Footer Premium -->
    <footer style="background: linear-gradient(135deg, #1f2937 0%, #111827 50%, #0f172a 100%); color: white; margin-top: 80px; position: relative; overflow: hidden;">
        <!-- Patrón de fondo -->
        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: url('data:image/svg+xml,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 100 100&quot;><defs><pattern id=&quot;plant-pattern&quot; width=&quot;50&quot; height=&quot;50&quot; patternUnits=&quot;userSpaceOnUse&quot;><circle cx=&quot;25&quot; cy=&quot;25&quot; r=&quot;1&quot; fill=&quot;rgba(5,150,105,0.1)&quot;/><circle cx=&quot;10&quot; cy=&quot;10&quot; r=&quot;0.5&quot; fill=&quot;rgba(16,185,129,0.05)&quot;/><circle cx=&quot;40&quot; cy=&quot;15&quot; r=&quot;0.5&quot; fill=&quot;rgba(52,211,153,0.05)&quot;/></pattern></defs><rect width=&quot;100&quot; height=&quot;100&quot; fill=&quot;url(%23plant-pattern)&quot;/></svg>'); opacity: 0.3;"></div>

        <div class="container" style="position: relative; z-index: 1;">
            <!-- Contenido principal del footer -->
            <div style="display: grid; grid-template-columns: 2fr 1fr 1fr 1fr; gap: 48px; padding: 64px 0 48px 0;">

                <!-- Sección del logo y descripción -->
                <div style="max-width: 400px;">
                    <div style="display: flex; align-items: center; gap: 16px; margin-bottom: 24px;">
                        <div style="background: linear-gradient(135deg, #059669, #10b981); padding: 16px; border-radius: 20px; box-shadow: 0 8px 32px rgba(5, 150, 105, 0.3);">
                            <span class="material-icons" style="font-size: 32px; color: white;">local_florist</span>
                        </div>
                        <h2 style="margin: 0; font-size: 32px; font-weight: 800; background: linear-gradient(135deg, #10b981, #34d399); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">PlantCare</h2>
                    </div>
                    <p style="margin: 0 0 24px 0; font-size: 16px; line-height: 1.6; color: #d1d5db;">Tu asistente inteligente para el cuidado de plantas nativas de Chihuahua. Descubre, identifica y aprende con tecnología de vanguardia.</p>

                    <!-- Estadísticas del footer -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 24px;">
                        <div style="background: rgba(5, 150, 105, 0.1); padding: 16px; border-radius: 12px; text-align: center; border: 1px solid rgba(5, 150, 105, 0.2);">
                            <div style="font-size: 24px; font-weight: 700; color: #10b981; margin-bottom: 4px;">5.2k+</div>
                            <div style="font-size: 12px; color: #9ca3af; text-transform: uppercase; letter-spacing: 0.5px;">Usuarios Activos</div>
                        </div>
                        <div style="background: rgba(5, 150, 105, 0.1); padding: 16px; border-radius: 12px; text-align: center; border: 1px solid rgba(5, 150, 105, 0.2);">
                            <div style="font-size: 24px; font-weight: 700; color: #10b981; margin-bottom: 4px;">150+</div>
                            <div style="font-size: 12px; color: #9ca3af; text-transform: uppercase; letter-spacing: 0.5px;">Especies Nativas</div>
                        </div>
                    </div>

                    <!-- Redes sociales -->
                    <div style="display: flex; gap: 12px;">
                        <a href="#" style="background: rgba(255, 255, 255, 0.1); padding: 12px; border-radius: 12px; color: #d1d5db; text-decoration: none; transition: all 0.3s ease; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.1);" onmouseover="this.style.background='rgba(5, 150, 105, 0.2)'; this.style.color='#10b981'; this.style.transform='translateY(-2px)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'; this.style.color='#d1d5db'; this.style.transform='translateY(0)'">
                            <span class="material-icons" style="font-size: 20px;">facebook</span>
                        </a>
                        <a href="#" style="background: rgba(255, 255, 255, 0.1); padding: 12px; border-radius: 12px; color: #d1d5db; text-decoration: none; transition: all 0.3s ease; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.1);" onmouseover="this.style.background='rgba(5, 150, 105, 0.2)'; this.style.color='#10b981'; this.style.transform='translateY(-2px)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'; this.style.color='#d1d5db'; this.style.transform='translateY(0)'">
                            <span class="material-icons" style="font-size: 20px;">alternate_email</span>
                        </a>
                        <a href="#" style="background: rgba(255, 255, 255, 0.1); padding: 12px; border-radius: 12px; color: #d1d5db; text-decoration: none; transition: all 0.3s ease; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.1);" onmouseover="this.style.background='rgba(5, 150, 105, 0.2)'; this.style.color='#10b981'; this.style.transform='translateY(-2px)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'; this.style.color='#d1d5db'; this.style.transform='translateY(0)'">
                            <span class="material-icons" style="font-size: 20px;">share</span>
                        </a>
                    </div>
                </div>

                <!-- Enlaces principales -->
                <div>
                    <h3 style="margin: 0 0 24px 0; font-size: 18px; font-weight: 700; color: white; display: flex; align-items: center; gap: 8px;">
                        <span class="material-icons" style="color: #10b981;">navigation</span>
                        Enlaces
                    </h3>
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li style="margin-bottom: 12px;">
                            <a href="/" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">home</span>
                                Inicio
                            </a>
                        </li>
                        <li style="margin-bottom: 12px;">
                            <a href="/biblioteca" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">library_books</span>
                                Biblioteca
                            </a>
                        </li>
                        <li style="margin-bottom: 12px;">
                            <a href="/scanner" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">photo_camera</span>
                                Scanner
                            </a>
                        </li>
                        <li style="margin-bottom: 12px;">
                            <a href="/calendario" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">event</span>
                                Calendario
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Comunidad -->
                <div>
                    <h3 style="margin: 0 0 24px 0; font-size: 18px; font-weight: 700; color: white; display: flex; align-items: center; gap: 8px;">
                        <span class="material-icons" style="color: #10b981;">groups</span>
                        Comunidad
                    </h3>
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li style="margin-bottom: 12px;">
                            <a href="/clubes" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">diversity_3</span>
                                Clubes
                            </a>
                        </li>
                        <li style="margin-bottom: 12px;">
                            <a href="#" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">article</span>
                                Blog
                            </a>
                        </li>
                        <li style="margin-bottom: 12px;">
                            <a href="#" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">contact_support</span>
                                Contacto
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Legal -->
                <div>
                    <h3 style="margin: 0 0 24px 0; font-size: 18px; font-weight: 700; color: white; display: flex; align-items: center; gap: 8px;">
                        <span class="material-icons" style="color: #10b981;">gavel</span>
                        Legal
                    </h3>
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li style="margin-bottom: 12px;">
                            <a href="/register" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">description</span>
                                Términos de Uso
                            </a>
                        </li>
                        <li style="margin-bottom: 12px;">
                            <a href="/ajustes" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">privacy_tip</span>
                                Política de Privacidad
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Línea divisoria -->
            <div style="height: 1px; background: linear-gradient(90deg, transparent 0%, rgba(5, 150, 105, 0.3) 50%, transparent 100%); margin: 32px 0;"></div>

            <!-- Footer bottom -->
            <div style="display: flex; justify-content: space-between; align-items: center; padding-bottom: 32px; flex-wrap: wrap; gap: 16px;">
                <p style="margin: 0; color: #9ca3af; font-size: 14px;">
                    © 2025 <span style="color: #10b981; font-weight: 600;">PlantCare</span>. Todos los derechos reservados.
                </p>
                <div style="display: flex; align-items: center; gap: 16px; color: #9ca3af; font-size: 14px;">
                    <span style="display: flex; align-items: center; gap: 4px;">
                        <span class="material-icons" style="font-size: 16px; color: #10b981;">eco</span>
                        Hecho con amor para las plantas
                    </span>
                    <span style="display: flex; align-items: center; gap: 4px;">
                        <span class="material-icons" style="font-size: 16px; color: #10b981;">location_on</span>
                        Chihuahua, México
                    </span>
                </div>
            </div>
        </div>
    </footer>

    <script src="{{ url_for('static', filename='js/calendario.js') }}"></script>
    <script src="{{ url_for('static', filename='js/home-navbar.js') }}"></script>

</body>
</html>

