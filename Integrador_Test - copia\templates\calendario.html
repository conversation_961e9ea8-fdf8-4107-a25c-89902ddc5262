<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calendario de Cuidados - PlantCare</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/calendario.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/navbar.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/home-navbar.css') }}">
</head>
<body>
    <!-- Navegación atractiva -->
    <nav class="home-navbar">
        <div class="home-navbar-container">
            <!-- Logo atractivo -->
            <a href="/" class="home-logo">
                <span class="material-icons home-logo-icon">local_florist</span>
                <h1 class="home-logo-text">PlantCare</h1>
            </a>

            <!-- Navegación central con iconos -->
            <ul class="home-nav-links">
                <li class="home-nav-item">
                    <a href="/" class="home-nav-link">
                        <span class="material-icons home-nav-icon">home</span>
                        Inicio
                    </a>
                </li>
                <li class="home-nav-item">
                    <a href="/biblioteca" class="home-nav-link">
                        <span class="material-icons home-nav-icon">library_books</span>
                        Biblioteca
                    </a>
                </li>
                <li class="home-nav-item">
                    <a href="/scanner" class="home-nav-link">
                        <span class="material-icons home-nav-icon">photo_camera</span>
                        Scanner
                    </a>
                </li>
                <li class="home-nav-item">
                    <a href="/calendario" class="home-nav-link active">
                        <span class="material-icons home-nav-icon">event</span>
                        Calendario
                    </a>
                </li>
                <li class="home-nav-item">
                    <a href="/foro" class="home-nav-link">
                        <span class="material-icons home-nav-icon">forum</span>
                        Foro
                    </a>
                </li>
            </ul>

            <!-- Botones de acción atractivos -->
            <div class="home-auth-buttons">
                {% if current_user.is_authenticated %}
                    <div class="user-menu">
                        <button id="user-menu-button" class="avatar">
                            <img src="{{ url_for('static', filename='assets/pfp.jpg') }}" alt="Usuario">
                        </button>
                        <div id="user-dropdown" class="dropdown-menu">
                            <a href="/perfil">
                                <span class="material-icons">person</span>
                                Mi Perfil
                            </a>
                            <a href="/ajustes">
                                <span class="material-icons">settings</span>
                                Ajustes
                            </a>
                            <a href="/login" id="logout-button">
                                <span class="material-icons">logout</span>
                                Cerrar Sesión
                            </a>
                        </div>
                    </div>
                {% else %}
                    <a href="/login" class="home-btn home-btn-outline">
                        <span class="material-icons home-btn-icon">login</span>
                        Iniciar Sesión
                    </a>
                    <a href="/register" class="home-btn home-btn-primary">
                        <span class="material-icons home-btn-icon">person_add</span>
                        Registrarse
                    </a>
                {% endif %}
            </div>
        </div>
    </nav>

    <div class="container">

        <!-- Header del calendario -->
        <div class="calendar-header">
            <h1>🌱 Calendario de Cuidados</h1>
            <p>Organiza y programa el cuidado de tus plantas nativas de Chihuahua</p>
        </div>

        <!-- Calendario principal -->
        <div class="calendar-main">
            <div class="calendar-card">
                <div class="calendar-controls">
                    <button id="prev-month" class="control-btn">
                        <span class="material-icons">chevron_left</span>
                    </button>
                    <h2 id="current-month-year">Enero 2025</h2>
                    <button id="next-month" class="control-btn">
                        <span class="material-icons">chevron_right</span>
                    </button>
                </div>

                <div class="calendar-actions">
                    <button id="add-event-btn" class="add-event-btn">
                        <span class="material-icons">add</span>
                        Agregar Evento
                    </button>
                    <button id="toggle-agricultural" class="toggle-btn">
                        <span class="material-icons">agriculture</span>
                        Calendario Agrícola
                    </button>
                </div>

                <div class="calendar-grid">
                    <div class="weekdays">
                        <div class="weekday">Dom</div>
                        <div class="weekday">Lun</div>
                        <div class="weekday">Mar</div>
                        <div class="weekday">Mié</div>
                        <div class="weekday">Jue</div>
                        <div class="weekday">Vie</div>
                        <div class="weekday">Sáb</div>
                    </div>
                    <div class="days-grid" id="days-grid">
                        <!-- Los días se generarán con JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Sección de tareas y eventos -->
        <div class="tasks-section">
            <div class="tasks-grid">
                <!-- Tareas de hoy -->
                <div class="task-card">
                    <div class="task-header">
                        <h3>📅 Tareas de Hoy</h3>
                        <span class="task-count" id="today-count">3</span>
                    </div>
                    <div class="task-list" id="today-tasks">
                        <div class="task-item riego">
                            <div class="task-icon">💧</div>
                            <div class="task-info">
                                <h4>Regar Agave</h4>
                                <p>9:00 AM - Agave americana</p>
                            </div>
                            <button class="task-complete">✓</button>
                        </div>
                        <div class="task-item fertilizar">
                            <div class="task-icon">🌱</div>
                            <div class="task-info">
                                <h4>Fertilizar Nopal</h4>
                                <p>2:00 PM - Opuntia ficus-indica</p>
                            </div>
                            <button class="task-complete">✓</button>
                        </div>
                        <div class="task-item podar">
                            <div class="task-icon">✂️</div>
                            <div class="task-info">
                                <h4>Revisar Yucca</h4>
                                <p>5:00 PM - Yucca filifera</p>
                            </div>
                            <button class="task-complete">✓</button>
                        </div>
                    </div>
                </div>

                <!-- Próximas tareas -->
                <div class="task-card">
                    <div class="task-header">
                        <h3>⏰ Próximas Tareas</h3>
                        <span class="task-count" id="upcoming-count">5</span>
                    </div>
                    <div class="task-list" id="upcoming-tasks">
                        <div class="task-item riego">
                            <div class="task-icon">💧</div>
                            <div class="task-info">
                                <h4>Regar Biznaga</h4>
                                <p>Mañana - 8:00 AM</p>
                            </div>
                        </div>
                        <div class="task-item trasplantar">
                            <div class="task-icon">🪴</div>
                            <div class="task-info">
                                <h4>Trasplantar Lechuguilla</h4>
                                <p>En 3 días - 10:00 AM</p>
                            </div>
                        </div>
                        <div class="task-item fertilizar">
                            <div class="task-icon">🌱</div>
                            <div class="task-info">
                                <h4>Fertilizar Mezquite</h4>
                                <p>En 5 días - 3:00 PM</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Estadísticas -->
                <div class="stats-card">
                    <div class="task-header">
                        <h3>📊 Estadísticas del Mes</h3>
                    </div>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-icon riego">💧</div>
                            <div class="stat-info">
                                <span class="stat-number">12</span>
                                <span class="stat-label">Riegos</span>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon fertilizar">🌱</div>
                            <div class="stat-info">
                                <span class="stat-number">4</span>
                                <span class="stat-label">Fertilizaciones</span>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon podar">✂️</div>
                            <div class="stat-info">
                                <span class="stat-number">2</span>
                                <span class="stat-label">Podas</span>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon trasplantar">🪴</div>
                            <div class="stat-info">
                                <span class="stat-number">1</span>
                                <span class="stat-label">Trasplantes</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Modal para agregar eventos -->
    <div id="event-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Agregar Nuevo Evento</h3>
                <button class="close-btn" onclick="closeModal()">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <form id="event-form">
                <div class="form-group">
                    <label for="event-title">Título del Evento</label>
                    <input type="text" id="event-title" placeholder="Ej. Regar Agave" required>
                </div>
                <div class="form-group">
                    <label for="event-type">Tipo de Actividad</label>
                    <select id="event-type" required>
                        <option value="">Seleccionar tipo...</option>
                        <option value="riego">💧 Riego</option>
                        <option value="fertilizar">🌱 Fertilización</option>
                        <option value="podar">✂️ Poda</option>
                        <option value="trasplantar">🪴 Trasplante</option>
                        <option value="siembra">🌱 Siembra</option>
                        <option value="cosecha">🌾 Cosecha</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="event-plant">Planta</label>
                    <input type="text" id="event-plant" placeholder="Ej. Agave americana">
                </div>
                <div class="form-group">
                    <label for="event-date">Fecha</label>
                    <input type="date" id="event-date" required>
                </div>
                <div class="form-group">
                    <label for="event-time">Hora</label>
                    <input type="time" id="event-time">
                </div>
                <div class="form-group">
                    <label for="event-notes">Notas</label>
                    <textarea id="event-notes" rows="3" placeholder="Notas adicionales..."></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-cancel" onclick="closeModal()">Cancelar</button>
                    <button type="submit" class="btn-save">Guardar Evento</button>
                </div>
            </form>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/calendario.js') }}"></script>
    <script src="{{ url_for('static', filename='js/home-navbar.js') }}"></script></script>

</body>
</html>

