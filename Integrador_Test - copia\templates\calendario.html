<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calendario de Cuidados - PlantCare</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 50%, #1B5E20 100%);
            min-height: 100vh;
            color: white;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Navegación */
        .main-nav {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 15px 0;
            margin-bottom: 30px;
            border-radius: 15px;
        }

        .nav-links {
            display: flex;
            justify-content: center;
            list-style: none;
            gap: 30px;
            flex-wrap: wrap;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        /* Header del calendario */
        .calendar-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .calendar-header h1 {
            font-size: 3rem;
            font-weight: 300;
            margin-bottom: 10px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .calendar-header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        /* Calendario principal */
        .calendar-main {
            margin-bottom: 40px;
        }

        .calendar-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .calendar-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .control-btn {
            background: #4CAF50;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
        }

        .control-btn:hover {
            background: #2E7D32;
            transform: scale(1.1);
        }

        #current-month-year {
            font-size: 2rem;
            font-weight: 400;
            text-align: center;
            flex: 1;
            color: #2E7D32;
        }

        .calendar-grid {
            width: 100%;
        }

        .weekdays {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }

        .weekday {
            text-align: center;
            font-weight: 500;
            padding: 15px 0;
            background: #f5f5f5;
            border-radius: 10px;
            font-size: 0.9rem;
            color: #2E7D32;
        }

        .days-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 10px;
        }

        .day {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f9f9f9;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            font-weight: 500;
            min-height: 60px;
            color: #333;
            border: 2px solid transparent;
        }

        .day:hover {
            background: #e8f5e8;
            transform: scale(1.05);
            border-color: #4CAF50;
        }

        .day.today {
            background: #4CAF50;
            color: white;
            box-shadow: 0 0 20px rgba(76, 175, 80, 0.3);
            font-weight: 700;
        }

        .day.other-month {
            opacity: 0.3;
            color: #999;
        }

        .day.has-tasks::after {
            content: '';
            position: absolute;
            bottom: 5px;
            right: 5px;
            width: 8px;
            height: 8px;
            background: #FFD700;
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }

        .day.agricultural::before {
            content: '🌾';
            position: absolute;
            top: 2px;
            left: 2px;
            font-size: 12px;
        }

        /* Acciones del calendario */
        .calendar-actions {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            justify-content: center;
        }

        .add-event-btn, .toggle-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }

        .add-event-btn:hover, .toggle-btn:hover {
            background: #2E7D32;
            transform: translateY(-2px);
        }

        .toggle-btn.active {
            background: #FF9800;
        }

        /* Modal para agregar eventos */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            backdrop-filter: blur(5px);
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 20px;
            padding: 30px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .modal-header h3 {
            color: #2E7D32;
            margin: 0;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #999;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: #f5f5f5;
            color: #333;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2E7D32;
            font-weight: 500;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.2);
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 25px;
        }

        .btn-cancel {
            background: #f5f5f5;
            color: #666;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-cancel:hover {
            background: #e0e0e0;
        }

        .btn-save {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-save:hover {
            background: #2E7D32;
        }

        /* Información agrícola */
        .agricultural-info {
            margin-bottom: 30px;
        }

        .agricultural-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .agricultural-card h3 {
            color: white;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }

        .agricultural-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .agricultural-section h4 {
            color: white;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .agricultural-section ul {
            list-style: none;
            padding: 0;
        }

        .agricultural-section li {
            background: rgba(255, 255, 255, 0.1);
            padding: 8px 12px;
            margin-bottom: 5px;
            border-radius: 8px;
            color: white;
        }

        .day.selected {
            background: #FF9800 !important;
            color: white;
            transform: scale(1.1);
        }

        /* Sección de tareas */
        .tasks-section {
            margin-top: 40px;
        }

        .tasks-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
        }

        .task-card, .stats-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .task-header h3 {
            font-size: 1.3rem;
            font-weight: 500;
        }

        .task-count {
            background: rgba(255, 255, 255, 0.2);
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .task-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .task-item {
            display: flex;
            align-items: center;
            gap: 15px;
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .task-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(5px);
        }

        .task-icon {
            font-size: 1.5rem;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
        }

        .task-info {
            flex: 1;
        }

        .task-info h4 {
            font-size: 1rem;
            font-weight: 500;
            margin-bottom: 5px;
        }

        .task-info p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .task-complete {
            background: rgba(76, 175, 80, 0.3);
            border: 2px solid rgba(76, 175, 80, 0.5);
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
            font-weight: bold;
        }

        .task-complete:hover {
            background: rgba(76, 175, 80, 0.5);
            transform: scale(1.1);
        }

        /* Estadísticas */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 15px;
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 12px;
        }

        .stat-icon {
            font-size: 1.5rem;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
        }

        .stat-info {
            display: flex;
            flex-direction: column;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        /* Colores por tipo de tarea */
        .riego .task-icon, .riego .stat-icon {
            background: rgba(33, 150, 243, 0.3);
        }

        .fertilizar .task-icon, .fertilizar .stat-icon {
            background: rgba(76, 175, 80, 0.3);
        }

        .podar .task-icon, .podar .stat-icon {
            background: rgba(255, 152, 0, 0.3);
        }

        .trasplantar .task-icon, .trasplantar .stat-icon {
            background: rgba(156, 39, 176, 0.3);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .calendar-header h1 {
                font-size: 2rem;
            }

            .calendar-card {
                padding: 20px;
            }

            #current-month-year {
                font-size: 1.5rem;
            }

            .tasks-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .nav-links {
                gap: 15px;
            }

            .nav-links a {
                padding: 8px 15px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Navegación principal -->
        <nav class="main-nav">
            <ul class="nav-links">
                <li><a href="/">Inicio</a></li>
                <li><a href="/biblioteca">Biblioteca</a></li>
                <li><a href="/scanner">Scanner</a></li>
                <li><a href="/calendario" class="active">Calendario</a></li>
                <li><a href="/recomendaciones">Recomendaciones</a></li>
                <li><a href="/foro">Foro</a></li>
            </ul>
        </nav>

        <!-- Header del calendario -->
        <div class="calendar-header">
            <h1>🌱 Calendario de Cuidados</h1>
            <p>Organiza y programa el cuidado de tus plantas nativas de Chihuahua</p>
        </div>

        <!-- Calendario principal -->
        <div class="calendar-main">
            <div class="calendar-card">
                <div class="calendar-controls">
                    <button id="prev-month" class="control-btn">
                        <span class="material-icons">chevron_left</span>
                    </button>
                    <h2 id="current-month-year">Enero 2025</h2>
                    <button id="next-month" class="control-btn">
                        <span class="material-icons">chevron_right</span>
                    </button>
                </div>

                <div class="calendar-actions">
                    <button id="add-event-btn" class="add-event-btn">
                        <span class="material-icons">add</span>
                        Agregar Evento
                    </button>
                    <button id="toggle-agricultural" class="toggle-btn">
                        <span class="material-icons">agriculture</span>
                        Calendario Agrícola
                    </button>
                </div>

                <div class="calendar-grid">
                    <div class="weekdays">
                        <div class="weekday">Dom</div>
                        <div class="weekday">Lun</div>
                        <div class="weekday">Mar</div>
                        <div class="weekday">Mié</div>
                        <div class="weekday">Jue</div>
                        <div class="weekday">Vie</div>
                        <div class="weekday">Sáb</div>
                    </div>
                    <div class="days-grid" id="days-grid">
                        <!-- Los días se generarán con JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Sección de tareas y eventos -->
        <div class="tasks-section">
            <div class="tasks-grid">
                <!-- Tareas de hoy -->
                <div class="task-card">
                    <div class="task-header">
                        <h3>📅 Tareas de Hoy</h3>
                        <span class="task-count" id="today-count">3</span>
                    </div>
                    <div class="task-list" id="today-tasks">
                        <div class="task-item riego">
                            <div class="task-icon">💧</div>
                            <div class="task-info">
                                <h4>Regar Agave</h4>
                                <p>9:00 AM - Agave americana</p>
                            </div>
                            <button class="task-complete">✓</button>
                        </div>
                        <div class="task-item fertilizar">
                            <div class="task-icon">🌱</div>
                            <div class="task-info">
                                <h4>Fertilizar Nopal</h4>
                                <p>2:00 PM - Opuntia ficus-indica</p>
                            </div>
                            <button class="task-complete">✓</button>
                        </div>
                        <div class="task-item podar">
                            <div class="task-icon">✂️</div>
                            <div class="task-info">
                                <h4>Revisar Yucca</h4>
                                <p>5:00 PM - Yucca filifera</p>
                            </div>
                            <button class="task-complete">✓</button>
                        </div>
                    </div>
                </div>

                <!-- Próximas tareas -->
                <div class="task-card">
                    <div class="task-header">
                        <h3>⏰ Próximas Tareas</h3>
                        <span class="task-count" id="upcoming-count">5</span>
                    </div>
                    <div class="task-list" id="upcoming-tasks">
                        <div class="task-item riego">
                            <div class="task-icon">💧</div>
                            <div class="task-info">
                                <h4>Regar Biznaga</h4>
                                <p>Mañana - 8:00 AM</p>
                            </div>
                        </div>
                        <div class="task-item trasplantar">
                            <div class="task-icon">🪴</div>
                            <div class="task-info">
                                <h4>Trasplantar Lechuguilla</h4>
                                <p>En 3 días - 10:00 AM</p>
                            </div>
                        </div>
                        <div class="task-item fertilizar">
                            <div class="task-icon">🌱</div>
                            <div class="task-info">
                                <h4>Fertilizar Mezquite</h4>
                                <p>En 5 días - 3:00 PM</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Estadísticas -->
                <div class="stats-card">
                    <div class="task-header">
                        <h3>📊 Estadísticas del Mes</h3>
                    </div>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-icon riego">💧</div>
                            <div class="stat-info">
                                <span class="stat-number">12</span>
                                <span class="stat-label">Riegos</span>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon fertilizar">🌱</div>
                            <div class="stat-info">
                                <span class="stat-number">4</span>
                                <span class="stat-label">Fertilizaciones</span>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon podar">✂️</div>
                            <div class="stat-info">
                                <span class="stat-number">2</span>
                                <span class="stat-label">Podas</span>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon trasplantar">🪴</div>
                            <div class="stat-info">
                                <span class="stat-number">1</span>
                                <span class="stat-label">Trasplantes</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Modal para agregar eventos -->
    <div id="event-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Agregar Nuevo Evento</h3>
                <button class="close-btn" onclick="closeModal()">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <form id="event-form">
                <div class="form-group">
                    <label for="event-title">Título del Evento</label>
                    <input type="text" id="event-title" placeholder="Ej. Regar Agave" required>
                </div>
                <div class="form-group">
                    <label for="event-type">Tipo de Actividad</label>
                    <select id="event-type" required>
                        <option value="">Seleccionar tipo...</option>
                        <option value="riego">💧 Riego</option>
                        <option value="fertilizar">🌱 Fertilización</option>
                        <option value="podar">✂️ Poda</option>
                        <option value="trasplantar">🪴 Trasplante</option>
                        <option value="siembra">🌱 Siembra</option>
                        <option value="cosecha">🌾 Cosecha</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="event-plant">Planta</label>
                    <input type="text" id="event-plant" placeholder="Ej. Agave americana">
                </div>
                <div class="form-group">
                    <label for="event-date">Fecha</label>
                    <input type="date" id="event-date" required>
                </div>
                <div class="form-group">
                    <label for="event-time">Hora</label>
                    <input type="time" id="event-time">
                </div>
                <div class="form-group">
                    <label for="event-notes">Notas</label>
                    <textarea id="event-notes" rows="3" placeholder="Notas adicionales..."></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-cancel" onclick="closeModal()">Cancelar</button>
                    <button type="submit" class="btn-save">Guardar Evento</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Variables globales
        let currentDate = new Date();
        let showAgriculturalCalendar = false;
        let userEvents = {};

        const monthNames = [
            'Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
            'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
        ];

        // Tareas de ejemplo
        const sampleTasks = {
            '2025-01-15': ['Regar Agave', 'Fertilizar Nopal'],
            '2025-01-16': ['Podar Yucca'],
            '2025-01-20': ['Trasplantar Biznaga'],
            '2025-01-25': ['Regar Lechuguilla', 'Revisar Mezquite']
        };

        // Calendario agrícola de Chihuahua
        const agriculturalCalendar = {
            1: { // Enero
                activities: ['Preparación de terrenos', 'Poda de frutales', 'Siembra de ajo'],
                plants: ['Ajo', 'Cebolla', 'Espinaca', 'Lechuga']
            },
            2: { // Febrero
                activities: ['Siembra de hortalizas de invierno', 'Injertos', 'Preparación de almácigos'],
                plants: ['Brócoli', 'Coliflor', 'Zanahoria', 'Rábano']
            },
            3: { // Marzo
                activities: ['Siembra de primavera', 'Trasplantes', 'Control de plagas'],
                plants: ['Tomate', 'Chile', 'Calabaza', 'Frijol']
            },
            4: { // Abril
                activities: ['Siembra de cultivos de temporal', 'Riego intensivo', 'Fertilización'],
                plants: ['Maíz', 'Sorgo', 'Girasol', 'Sandía']
            },
            5: { // Mayo
                activities: ['Siembra tardía', 'Control de malezas', 'Cosecha temprana'],
                plants: ['Melón', 'Pepino', 'Okra', 'Quelite']
            },
            6: { // Junio
                activities: ['Inicio de lluvias', 'Siembra de temporal', 'Cosecha de primavera'],
                plants: ['Frijol de temporal', 'Maíz criollo', 'Calabaza de castilla']
            },
            7: { // Julio
                activities: ['Temporada de lluvias', 'Siembra intensiva', 'Control fitosanitario'],
                plants: ['Quelites', 'Verdolagas', 'Chía', 'Amaranto']
            },
            8: { // Agosto
                activities: ['Mantenimiento de cultivos', 'Cosecha de verano', 'Preparación de conservas'],
                plants: ['Elote', 'Calabaza tierna', 'Ejotes', 'Nopales']
            },
            9: { // Septiembre
                activities: ['Cosecha principal', 'Secado de granos', 'Preparación de semillas'],
                plants: ['Maíz', 'Frijol', 'Chile seco', 'Quelites']
            },
            10: { // Octubre
                activities: ['Cosecha de otoño', 'Almacenamiento', 'Siembra de invierno'],
                plants: ['Calabaza de castilla', 'Frijol', 'Avena', 'Cebada']
            },
            11: { // Noviembre
                activities: ['Preparación para invierno', 'Poda', 'Conservación de semillas'],
                plants: ['Trigo', 'Avena', 'Cebolla de cambray', 'Cilantro']
            },
            12: { // Diciembre
                activities: ['Descanso de tierras', 'Planificación', 'Mantenimiento de herramientas'],
                plants: ['Ajo', 'Cebolla', 'Espinaca de invierno', 'Acelga']
            }
        };

        // Inicializar calendario
        function initCalendar() {
            updateCalendarHeader();
            generateCalendarDays();
        }

        // Actualizar header del calendario
        function updateCalendarHeader() {
            const monthYear = document.getElementById('current-month-year');
            monthYear.textContent = `${monthNames[currentDate.getMonth()]} ${currentDate.getFullYear()}`;
        }

        // Generar días del calendario
        function generateCalendarDays() {
            const daysGrid = document.getElementById('days-grid');
            daysGrid.innerHTML = '';

            const year = currentDate.getFullYear();
            const month = currentDate.getMonth();

            // Primer día del mes
            const firstDay = new Date(year, month, 1);
            const lastDay = new Date(year, month + 1, 0);
            const startDate = new Date(firstDay);
            startDate.setDate(startDate.getDate() - firstDay.getDay());

            // Generar 42 días (6 semanas)
            for (let i = 0; i < 42; i++) {
                const date = new Date(startDate);
                date.setDate(startDate.getDate() + i);

                const dayElement = createDayElement(date, month);
                daysGrid.appendChild(dayElement);
            }
        }

        // Crear elemento de día
        function createDayElement(date, currentMonth) {
            const dayDiv = document.createElement('div');
            dayDiv.className = 'day';
            dayDiv.textContent = date.getDate();

            // Verificar si es del mes actual
            if (date.getMonth() !== currentMonth) {
                dayDiv.classList.add('other-month');
            }

            // Verificar si es hoy
            const today = new Date();
            if (date.toDateString() === today.toDateString()) {
                dayDiv.classList.add('today');
            }

            // Verificar si tiene tareas
            const dateString = date.toISOString().split('T')[0];
            if (sampleTasks[dateString] || userEvents[dateString]) {
                dayDiv.classList.add('has-tasks');
                const tasks = [...(sampleTasks[dateString] || []), ...(userEvents[dateString] || [])];
                dayDiv.title = `Tareas: ${tasks.join(', ')}`;
            }

            // Mostrar información agrícola si está activada
            if (showAgriculturalCalendar && date.getMonth() === currentMonth) {
                const monthData = agriculturalCalendar[date.getMonth() + 1];
                if (monthData && date.getDate() <= 7) { // Mostrar solo en la primera semana
                    dayDiv.classList.add('agricultural');
                    const activities = monthData.activities.join(', ');
                    const plants = monthData.plants.join(', ');
                    dayDiv.title = `Actividades agrícolas: ${activities}\nPlantas recomendadas: ${plants}`;
                }
            }

            // Agregar evento de clic para seleccionar fecha
            dayDiv.addEventListener('click', () => {
                document.querySelectorAll('.day.selected').forEach(d => d.classList.remove('selected'));
                dayDiv.classList.add('selected');

                // Establecer fecha en el modal
                const eventDate = document.getElementById('event-date');
                eventDate.value = date.toISOString().split('T')[0];
            });

            return dayDiv;
        }

        // Navegación de meses
        document.getElementById('prev-month').addEventListener('click', () => {
            currentDate.setMonth(currentDate.getMonth() - 1);
            initCalendar();
        });

        document.getElementById('next-month').addEventListener('click', () => {
            currentDate.setMonth(currentDate.getMonth() + 1);
            initCalendar();
        });

        // Completar tareas
        document.querySelectorAll('.task-complete').forEach(button => {
            button.addEventListener('click', function() {
                const taskItem = this.closest('.task-item');
                taskItem.style.opacity = '0.5';
                taskItem.style.textDecoration = 'line-through';
                this.style.background = 'rgba(76, 175, 80, 0.8)';
                this.textContent = '✓';

                // Actualizar contador
                const taskCard = taskItem.closest('.task-card');
                const countElement = taskCard.querySelector('.task-count');
                let count = parseInt(countElement.textContent);
                countElement.textContent = Math.max(0, count - 1);
            });
        });

        // Funciones del modal
        function openModal() {
            document.getElementById('event-modal').classList.add('show');
        }

        function closeModal() {
            document.getElementById('event-modal').classList.remove('show');
            document.getElementById('event-form').reset();
        }

        // Agregar evento
        document.getElementById('event-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const title = document.getElementById('event-title').value;
            const type = document.getElementById('event-type').value;
            const plant = document.getElementById('event-plant').value;
            const date = document.getElementById('event-date').value;
            const time = document.getElementById('event-time').value;
            const notes = document.getElementById('event-notes').value;

            // Crear evento
            const eventText = `${title}${plant ? ' - ' + plant : ''}${time ? ' (' + time + ')' : ''}`;

            if (!userEvents[date]) {
                userEvents[date] = [];
            }
            userEvents[date].push(eventText);

            // Actualizar calendario
            generateCalendarDays();
            closeModal();

            // Mostrar notificación
            alert('Evento agregado exitosamente!');
        });

        // Toggle calendario agrícola
        document.getElementById('toggle-agricultural').addEventListener('click', function() {
            showAgriculturalCalendar = !showAgriculturalCalendar;
            this.classList.toggle('active');

            if (showAgriculturalCalendar) {
                this.innerHTML = '<span class="material-icons">agriculture</span>Ocultar Calendario Agrícola';
                showAgriculturalInfo();
            } else {
                this.innerHTML = '<span class="material-icons">agriculture</span>Calendario Agrícola';
                hideAgriculturalInfo();
            }

            generateCalendarDays();
        });

        // Mostrar información agrícola
        function showAgriculturalInfo() {
            const month = currentDate.getMonth() + 1;
            const monthData = agriculturalCalendar[month];

            if (monthData) {
                const infoDiv = document.createElement('div');
                infoDiv.id = 'agricultural-info';
                infoDiv.className = 'agricultural-info';
                infoDiv.innerHTML = `
                    <div class="agricultural-card">
                        <h3>🌾 Calendario Agrícola - ${monthNames[month - 1]}</h3>
                        <div class="agricultural-content">
                            <div class="agricultural-section">
                                <h4>📋 Actividades Recomendadas:</h4>
                                <ul>
                                    ${monthData.activities.map(activity => `<li>${activity}</li>`).join('')}
                                </ul>
                            </div>
                            <div class="agricultural-section">
                                <h4>🌱 Plantas de Temporada:</h4>
                                <ul>
                                    ${monthData.plants.map(plant => `<li>${plant}</li>`).join('')}
                                </ul>
                            </div>
                        </div>
                    </div>
                `;

                document.querySelector('.tasks-section').insertBefore(infoDiv, document.querySelector('.tasks-grid'));
            }
        }

        // Ocultar información agrícola
        function hideAgriculturalInfo() {
            const infoDiv = document.getElementById('agricultural-info');
            if (infoDiv) {
                infoDiv.remove();
            }
        }

        // Event listeners
        document.getElementById('add-event-btn').addEventListener('click', openModal);

        // Cerrar modal al hacer clic fuera
        document.getElementById('event-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // Inicializar cuando se carga la página
        document.addEventListener('DOMContentLoaded', initCalendar);
    </script>

</body>
</html>

