<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clubes - PlantCare</title>
    <style>
        /* CSS básico para asegurar que funcione */
        body {
            padding-top: 80px;
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }

        /* Header mejorado */
        .page-header {
            background: linear-gradient(135deg, #059669 0%, #10b981 50%, #34d399 100%);
            border-radius: 24px;
            padding: 48px 40px;
            margin-bottom: 40px;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 40px;
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .main-title {
            font-size: 42px;
            font-weight: 800;
            margin: 0 0 16px 0;
            display: flex;
            align-items: center;
            gap: 16px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .title-icon {
            font-size: 48px;
            background: rgba(255,255,255,0.2);
            padding: 12px;
            border-radius: 16px;
            backdrop-filter: blur(10px);
        }

        .subtitle {
            font-size: 18px;
            margin: 0 0 24px 0;
            opacity: 0.9;
            line-height: 1.6;
            max-width: 500px;
        }

        .header-stats {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
        }

        .stat-badge {
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            padding: 12px 16px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 500;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .stat-badge .material-icons {
            font-size: 18px;
        }

        /* Tarjetas flotantes */
        .header-visual {
            position: relative;
        }

        .floating-cards {
            position: relative;
            width: 200px;
            height: 200px;
        }

        .mini-card {
            position: absolute;
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.15);
            animation: float 6s ease-in-out infinite;
        }

        .mini-card img {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            object-fit: cover;
        }

        .mini-card span {
            position: absolute;
            bottom: -8px;
            right: -8px;
            background: #fbbf24;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .card-1 {
            top: 0;
            left: 0;
            animation-delay: 0s;
        }

        .card-2 {
            top: 60px;
            right: 0;
            animation-delay: 2s;
        }

        .card-3 {
            bottom: 0;
            left: 40px;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(2deg); }
        }

        .clubs-container {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 32px;
            margin-top: 32px;
        }

        .club-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .club-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #059669, #10b981);
            color: white;
            box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #047857, #059669);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(5, 150, 105, 0.4);
        }

        .btn-outline {
            background: white;
            color: #059669;
            border: 1px solid #059669;
        }

        .btn-outline:hover {
            background: #f0fdf4;
            transform: translateY(-2px);
        }

        /* Sidebar mejorado */
        .clubs-sidebar {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .sidebar-section {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e5e7eb;
        }

        .sidebar-section h3 {
            margin: 0 0 16px 0;
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        @media (max-width: 768px) {
            .clubs-container { grid-template-columns: 1fr; }
            .header-content { grid-template-columns: 1fr; text-align: center; }
            .floating-cards { margin: 0 auto; }
            .main-title { font-size: 32px; justify-content: center; }
            .header-stats { justify-content: center; }
        }

        /* Footer responsive */
        @media (max-width: 1024px) {
            footer .container > div:first-child {
                grid-template-columns: 1fr 1fr !important;
                gap: 32px !important;
            }
        }

        @media (max-width: 768px) {
            footer .container > div:first-child {
                grid-template-columns: 1fr !important;
                gap: 24px !important;
                text-align: center !important;
            }

            footer .container > div:last-child {
                flex-direction: column !important;
                text-align: center !important;
            }
        }
    </style>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/clubes.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/navbar.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/home-navbar.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/forum-modern.css') }}">
</head>
<body>
    <!-- Navegación atractiva -->
    <nav class="home-navbar">
        <div class="home-navbar-container">
            <!-- Logo atractivo -->
            <a href="/" class="home-logo">
                <span class="material-icons home-logo-icon">local_florist</span>
                <h1 class="home-logo-text">PlantCare</h1>
            </a>
            
            <!-- Navegación central con iconos -->
            <ul class="home-nav-links">
                <li class="home-nav-item">
                    <a href="/" class="home-nav-link">
                        <span class="material-icons home-nav-icon">home</span>
                        Inicio
                    </a>
                </li>
                <li class="home-nav-item">
                    <a href="/biblioteca" class="home-nav-link">
                        <span class="material-icons home-nav-icon">library_books</span>
                        Biblioteca
                    </a>
                </li>
                <li class="home-nav-item">
                    <a href="/scanner" class="home-nav-link">
                        <span class="material-icons home-nav-icon">photo_camera</span>
                        Scanner
                    </a>
                </li>
                <li class="home-nav-item">
                    <a href="/calendario" class="home-nav-link">
                        <span class="material-icons home-nav-icon">event</span>
                        Calendario
                    </a>
                </li>
                <li class="home-nav-item">
                    <a href="/clubes" class="home-nav-link active">
                        <span class="material-icons home-nav-icon">groups</span>
                        Clubes
                    </a>
                </li>
            </ul>
            
            <!-- Botones de acción atractivos -->
            <div class="home-auth-buttons">
                {% if current_user.is_authenticated %}
                    <div class="user-menu">
                        <button id="user-menu-button" class="avatar">
                            <img src="{{ url_for('static', filename='assets/pfp.jpg') }}" alt="Usuario">
                        </button>
                        <div id="user-dropdown" class="dropdown-menu">
                            <a href="/perfil">
                                <span class="material-icons">person</span>
                                Mi Perfil
                            </a>
                            <a href="/ajustes">
                                <span class="material-icons">settings</span>
                                Ajustes
                            </a>
                            <a href="/login" id="logout-button">
                                <span class="material-icons">logout</span>
                                Cerrar Sesión
                            </a>
                        </div>
                    </div>
                {% else %}
                    <a href="/login" class="home-btn home-btn-outline">
                        <span class="material-icons home-btn-icon">login</span>
                        Iniciar Sesión
                    </a>
                    <a href="/register" class="home-btn home-btn-primary">
                        <span class="material-icons home-btn-icon">person_add</span>
                        Registrarse
                    </a>
                {% endif %}
            </div>
        </div>
    </nav>

    <main>
        <div class="container">
            <section class="page-header">
                <div class="header-content">
                    <div class="header-text">
                        <h1 class="main-title">
                            <span class="material-icons title-icon">groups</span>
                            Clubes por Intereses
                        </h1>
                        <p class="subtitle">Únete a comunidades especializadas y conecta con personas que comparten tu pasión</p>
                        <div class="header-stats">
                            <div class="stat-badge">
                                <span class="material-icons">people</span>
                                <span>5.2k+ miembros activos</span>
                            </div>
                            <div class="stat-badge">
                                <span class="material-icons">forum</span>
                                <span>24 clubes disponibles</span>
                            </div>
                            <div class="stat-badge">
                                <span class="material-icons">event</span>
                                <span>127 eventos este mes</span>
                            </div>
                        </div>
                    </div>
                    <div class="header-visual">
                        <div class="floating-cards">
                            <div class="mini-card card-1">
                                <img src="{{ url_for('static', filename='assets/agave.webp') }}" alt="Cactus">
                                <span>🌵</span>
                            </div>
                            <div class="mini-card card-2">
                                <img src="{{ url_for('static', filename='assets/yuca.jpg') }}" alt="Orquídeas">
                                <span>🌺</span>
                            </div>
                            <div class="mini-card card-3">
                                <img src="{{ url_for('static', filename='assets/nopal.jpg') }}" alt="Huertos">
                                <span>🌱</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <div class="clubs-container">
                <!-- Sidebar con filtros y estadísticas -->
                <div class="clubs-sidebar">
                    <div class="sidebar-section">
                        <h3>Mis Clubes</h3>
                        <div class="my-clubs">
                            <div class="my-club-item">
                                <img src="../static/assets/agave.webp" alt="Club de Cactus" class="club-mini-avatar">
                                <div class="club-mini-info">
                                    <span class="club-mini-name">Cactus Lovers</span>
                                    <span class="club-mini-members">1.2k miembros</span>
                                </div>
                            </div>
                            <div class="my-club-item">
                                <img src="../static/assets/nopal.jpg" alt="Huertos Caseros" class="club-mini-avatar">
                                <div class="club-mini-info">
                                    <span class="club-mini-name">Huertos Caseros</span>
                                    <span class="club-mini-members">856 miembros</span>
                                </div>
                            </div>
                        </div>
                        <button class="btn btn-outline btn-sm">Ver todos mis clubes</button>
                    </div>

                    <div class="sidebar-section">
                        <h3>Categorías</h3>
                        <div class="category-filters">
                            <button class="category-filter active" data-category="all">Todos</button>
                            <button class="category-filter" data-category="cactus">Cactus y Suculentas</button>
                            <button class="category-filter" data-category="orquideas">Orquídeas</button>
                            <button class="category-filter" data-category="carnivoras">Plantas Carnívoras</button>
                            <button class="category-filter" data-category="huertos">Huertos Caseros</button>
                            <button class="category-filter" data-category="nativas">Plantas Nativas</button>
                            <button class="category-filter" data-category="interior">Plantas de Interior</button>
                        </div>
                    </div>

                    <div class="sidebar-section">
                        <h3>Estadísticas</h3>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <span class="stat-number">24</span>
                                <span class="stat-label">Clubes Activos</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">5.2k</span>
                                <span class="stat-label">Miembros Total</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">127</span>
                                <span class="stat-label">Eventos Este Mes</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contenido principal -->
                <div class="clubs-content">
                    <!-- Barra de búsqueda y acciones -->
                    <div class="clubs-actions">
                        <div class="search-box">
                            <input type="text" placeholder="Buscar clubes...">
                            <button class="icon-button">
                                <span class="material-icons">search</span>
                            </button>
                        </div>
                        <button id="create-club-btn" class="btn btn-primary">
                            <span class="material-icons">add</span>
                            Crear Club
                        </button>
                    </div>

                    <!-- Clubes destacados -->
                    <div class="featured-clubs">
                        <h2 style="margin: 0 0 24px 0; font-size: 28px; font-weight: 700; color: #1f2937;">Clubes Destacados</h2>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 24px;">

                            <!-- Club de Cactus -->
                            <div class="club-card" style="border: 2px solid #059669; position: relative;" data-category="cactus">
                                <div style="position: absolute; top: 16px; right: 16px; background: linear-gradient(135deg, #f59e0b, #f97316); color: white; padding: 8px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; display: flex; align-items: center; gap: 4px;">
                                    <span class="material-icons" style="font-size: 16px;">star</span>
                                    Destacado
                                </div>
                                <div style="height: 200px; overflow: hidden; border-radius: 12px; margin-bottom: 16px;">
                                    <img src="{{ url_for('static', filename='assets/agave.webp') }}" alt="Club de Cactus" style="width: 100%; height: 100%; object-fit: cover;">
                                </div>
                                <h3 style="margin: 0 0 12px 0; font-size: 20px; font-weight: 700; color: #1f2937;">🌵 Cactus Lovers México</h3>
                                <p style="margin: 0 0 16px 0; color: #6b7280; line-height: 1.6; font-size: 14px;">Comunidad dedicada al cultivo y cuidado de cactus y suculentas. Comparte fotos, consejos y experiencias.</p>

                                <div style="margin-bottom: 16px;">
                                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px; font-size: 13px; color: #6b7280;">
                                        <span class="material-icons" style="font-size: 16px; color: #9ca3af;">people</span>
                                        <span>1,234 miembros</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px; font-size: 13px; color: #6b7280;">
                                        <span class="material-icons" style="font-size: 16px; color: #9ca3af;">forum</span>
                                        <span>89 publicaciones hoy</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 8px; font-size: 13px; color: #6b7280;">
                                        <span class="material-icons" style="font-size: 16px; color: #9ca3af;">event</span>
                                        <span>3 eventos próximos</span>
                                    </div>
                                </div>

                                <div style="display: flex; flex-wrap: wrap; gap: 8px; margin-bottom: 20px;">
                                    <span style="background: #f3f4f6; color: #6b7280; padding: 4px 12px; border-radius: 16px; font-size: 11px; font-weight: 500;">Cactus</span>
                                    <span style="background: #f3f4f6; color: #6b7280; padding: 4px 12px; border-radius: 16px; font-size: 11px; font-weight: 500;">Suculentas</span>
                                    <span style="background: #f3f4f6; color: #6b7280; padding: 4px 12px; border-radius: 16px; font-size: 11px; font-weight: 500;">Desierto</span>
                                </div>

                                <div style="display: flex; gap: 12px;">
                                    <button class="btn btn-primary join-club" data-club-id="1" style="flex: 1;">
                                        <span class="material-icons">group_add</span>
                                        Unirse
                                    </button>
                                    <button class="btn btn-outline view-club" data-club-id="1" style="flex: 1;">
                                        Ver Club
                                    </button>
                                </div>
                            </div>

                            <!-- Club de Orquídeas -->
                            <div class="club-card" data-category="orquideas">
                                <div style="height: 200px; overflow: hidden; border-radius: 12px; margin-bottom: 16px;">
                                    <img src="{{ url_for('static', filename='assets/yuca.jpg') }}" alt="Club de Orquídeas" style="width: 100%; height: 100%; object-fit: cover;">
                                </div>
                                <h3 style="margin: 0 0 12px 0; font-size: 20px; font-weight: 700; color: #1f2937;">🌺 Orquídeas Exóticas</h3>
                                <p style="margin: 0 0 16px 0; color: #6b7280; line-height: 1.6; font-size: 14px;">Para amantes de las orquídeas. Técnicas avanzadas de cultivo, hibridación y cuidados especializados.</p>

                                <div style="margin-bottom: 16px;">
                                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px; font-size: 13px; color: #6b7280;">
                                        <span class="material-icons" style="font-size: 16px; color: #9ca3af;">people</span>
                                        <span>892 miembros</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px; font-size: 13px; color: #6b7280;">
                                        <span class="material-icons" style="font-size: 16px; color: #9ca3af;">forum</span>
                                        <span>45 publicaciones hoy</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 8px; font-size: 13px; color: #6b7280;">
                                        <span class="material-icons" style="font-size: 16px; color: #9ca3af;">event</span>
                                        <span>1 evento próximo</span>
                                    </div>
                                </div>

                                <div style="display: flex; flex-wrap: wrap; gap: 8px; margin-bottom: 20px;">
                                    <span style="background: #f3f4f6; color: #6b7280; padding: 4px 12px; border-radius: 16px; font-size: 11px; font-weight: 500;">Orquídeas</span>
                                    <span style="background: #f3f4f6; color: #6b7280; padding: 4px 12px; border-radius: 16px; font-size: 11px; font-weight: 500;">Hibridación</span>
                                    <span style="background: #f3f4f6; color: #6b7280; padding: 4px 12px; border-radius: 16px; font-size: 11px; font-weight: 500;">Avanzado</span>
                                </div>

                                <div style="display: flex; gap: 12px;">
                                    <button class="btn btn-primary join-club" data-club-id="2" style="flex: 1;">
                                        <span class="material-icons">group_add</span>
                                        Unirse
                                    </button>
                                    <button class="btn btn-outline view-club" data-club-id="2" style="flex: 1;">
                                        Ver Club
                                    </button>
                                </div>
                            </div>

                            <!-- Club de Plantas Carnívoras -->
                            <div class="club-card" data-category="carnivoras">
                                <div style="height: 200px; overflow: hidden; border-radius: 12px; margin-bottom: 16px;">
                                    <img src="{{ url_for('static', filename='assets/nopal.jpg') }}" alt="Plantas Carnívoras" style="width: 100%; height: 100%; object-fit: cover;">
                                </div>
                                <h3 style="margin: 0 0 12px 0; font-size: 20px; font-weight: 700; color: #1f2937;">🪴 Plantas Carnívoras</h3>
                                <p style="margin: 0 0 16px 0; color: #6b7280; line-height: 1.6; font-size: 14px;">Fascinante mundo de las plantas carnívoras. Venus atrapamoscas, nepenthes, droseras y más especies exóticas.</p>

                                <div style="margin-bottom: 16px;">
                                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px; font-size: 13px; color: #6b7280;">
                                        <span class="material-icons" style="font-size: 16px; color: #9ca3af;">people</span>
                                        <span>567 miembros</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px; font-size: 13px; color: #6b7280;">
                                        <span class="material-icons" style="font-size: 16px; color: #9ca3af;">forum</span>
                                        <span>23 publicaciones hoy</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 8px; font-size: 13px; color: #6b7280;">
                                        <span class="material-icons" style="font-size: 16px; color: #9ca3af;">event</span>
                                        <span>2 eventos próximos</span>
                                    </div>
                                </div>

                                <div style="display: flex; flex-wrap: wrap; gap: 8px; margin-bottom: 20px;">
                                    <span style="background: #f3f4f6; color: #6b7280; padding: 4px 12px; border-radius: 16px; font-size: 11px; font-weight: 500;">Carnívoras</span>
                                    <span style="background: #f3f4f6; color: #6b7280; padding: 4px 12px; border-radius: 16px; font-size: 11px; font-weight: 500;">Venus</span>
                                    <span style="background: #f3f4f6; color: #6b7280; padding: 4px 12px; border-radius: 16px; font-size: 11px; font-weight: 500;">Exóticas</span>
                                </div>

                                <div style="display: flex; gap: 12px;">
                                    <button class="btn btn-primary join-club" data-club-id="3" style="flex: 1;">
                                        <span class="material-icons">group_add</span>
                                        Unirse
                                    </button>
                                    <button class="btn btn-outline view-club" data-club-id="3" style="flex: 1;">
                                        Ver Club
                                    </button>
                                </div>
                            </div>

                            <!-- Club de Huertos Caseros -->
                            <div class="club-card" data-category="huertos">
                                <div style="height: 200px; overflow: hidden; border-radius: 12px; margin-bottom: 16px;">
                                    <img src="{{ url_for('static', filename='assets/agave.webp') }}" alt="Huertos Caseros" style="width: 100%; height: 100%; object-fit: cover;">
                                </div>
                                <h3 style="margin: 0 0 12px 0; font-size: 20px; font-weight: 700; color: #1f2937;">🌱 Huertos Caseros</h3>
                                <p style="margin: 0 0 16px 0; color: #6b7280; line-height: 1.6; font-size: 14px;">Cultiva tus propios alimentos en casa. Desde hierbas aromáticas hasta vegetales frescos en espacios pequeños.</p>

                                <div style="margin-bottom: 16px;">
                                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px; font-size: 13px; color: #6b7280;">
                                        <span class="material-icons" style="font-size: 16px; color: #9ca3af;">people</span>
                                        <span>1,456 miembros</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px; font-size: 13px; color: #6b7280;">
                                        <span class="material-icons" style="font-size: 16px; color: #9ca3af;">forum</span>
                                        <span>67 publicaciones hoy</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 8px; font-size: 13px; color: #6b7280;">
                                        <span class="material-icons" style="font-size: 16px; color: #9ca3af;">event</span>
                                        <span>5 eventos próximos</span>
                                    </div>
                                </div>

                                <div style="display: flex; flex-wrap: wrap; gap: 8px; margin-bottom: 20px;">
                                    <span style="background: #f3f4f6; color: #6b7280; padding: 4px 12px; border-radius: 16px; font-size: 11px; font-weight: 500;">Huertos</span>
                                    <span style="background: #f3f4f6; color: #6b7280; padding: 4px 12px; border-radius: 16px; font-size: 11px; font-weight: 500;">Urbano</span>
                                    <span style="background: #f3f4f6; color: #6b7280; padding: 4px 12px; border-radius: 16px; font-size: 11px; font-weight: 500;">Orgánico</span>
                                </div>

                                <div style="display: flex; gap: 12px;">
                                    <button class="btn btn-primary join-club" data-club-id="4" style="flex: 1;">
                                        <span class="material-icons">group_add</span>
                                        Unirse
                                    </button>
                                    <button class="btn btn-outline view-club" data-club-id="4" style="flex: 1;">
                                        Ver Club
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer style="background: linear-gradient(135deg, #1f2937 0%, #111827 50%, #0f172a 100%); color: white; margin-top: 80px; position: relative; overflow: hidden;">
        <!-- Patrón de fondo -->
        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: url('data:image/svg+xml,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 100 100&quot;><defs><pattern id=&quot;plant-pattern&quot; width=&quot;50&quot; height=&quot;50&quot; patternUnits=&quot;userSpaceOnUse&quot;><circle cx=&quot;25&quot; cy=&quot;25&quot; r=&quot;1&quot; fill=&quot;rgba(5,150,105,0.1)&quot;/><circle cx=&quot;10&quot; cy=&quot;10&quot; r=&quot;0.5&quot; fill=&quot;rgba(16,185,129,0.05)&quot;/><circle cx=&quot;40&quot; cy=&quot;15&quot; r=&quot;0.5&quot; fill=&quot;rgba(52,211,153,0.05)&quot;/></pattern></defs><rect width=&quot;100&quot; height=&quot;100&quot; fill=&quot;url(%23plant-pattern)&quot;/></svg>'); opacity: 0.3;"></div>

        <div class="container" style="position: relative; z-index: 1;">
            <!-- Contenido principal del footer -->
            <div style="display: grid; grid-template-columns: 2fr 1fr 1fr 1fr; gap: 48px; padding: 64px 0 48px 0;">

                <!-- Sección del logo y descripción -->
                <div style="max-width: 400px;">
                    <div style="display: flex; align-items: center; gap: 16px; margin-bottom: 24px;">
                        <div style="background: linear-gradient(135deg, #059669, #10b981); padding: 16px; border-radius: 20px; box-shadow: 0 8px 32px rgba(5, 150, 105, 0.3);">
                            <span class="material-icons" style="font-size: 32px; color: white;">local_florist</span>
                        </div>
                        <h2 style="margin: 0; font-size: 32px; font-weight: 800; background: linear-gradient(135deg, #10b981, #34d399); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">PlantCare</h2>
                    </div>
                    <p style="margin: 0 0 24px 0; font-size: 16px; line-height: 1.6; color: #d1d5db;">Tu asistente inteligente para el cuidado de plantas nativas de Chihuahua. Descubre, identifica y aprende con tecnología de vanguardia.</p>

                    <!-- Estadísticas del footer -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 24px;">
                        <div style="background: rgba(5, 150, 105, 0.1); padding: 16px; border-radius: 12px; text-align: center; border: 1px solid rgba(5, 150, 105, 0.2);">
                            <div style="font-size: 24px; font-weight: 700; color: #10b981; margin-bottom: 4px;">5.2k+</div>
                            <div style="font-size: 12px; color: #9ca3af; text-transform: uppercase; letter-spacing: 0.5px;">Usuarios Activos</div>
                        </div>
                        <div style="background: rgba(5, 150, 105, 0.1); padding: 16px; border-radius: 12px; text-align: center; border: 1px solid rgba(5, 150, 105, 0.2);">
                            <div style="font-size: 24px; font-weight: 700; color: #10b981; margin-bottom: 4px;">150+</div>
                            <div style="font-size: 12px; color: #9ca3af; text-transform: uppercase; letter-spacing: 0.5px;">Especies Nativas</div>
                        </div>
                    </div>

                    <!-- Redes sociales -->
                    <div style="display: flex; gap: 12px;">
                        <a href="#" style="background: rgba(255, 255, 255, 0.1); padding: 12px; border-radius: 12px; color: #d1d5db; text-decoration: none; transition: all 0.3s ease; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.1);" onmouseover="this.style.background='rgba(5, 150, 105, 0.2)'; this.style.color='#10b981'; this.style.transform='translateY(-2px)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'; this.style.color='#d1d5db'; this.style.transform='translateY(0)'">
                            <span class="material-icons" style="font-size: 20px;">facebook</span>
                        </a>
                        <a href="#" style="background: rgba(255, 255, 255, 0.1); padding: 12px; border-radius: 12px; color: #d1d5db; text-decoration: none; transition: all 0.3s ease; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.1);" onmouseover="this.style.background='rgba(5, 150, 105, 0.2)'; this.style.color='#10b981'; this.style.transform='translateY(-2px)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'; this.style.color='#d1d5db'; this.style.transform='translateY(0)'">
                            <span class="material-icons" style="font-size: 20px;">alternate_email</span>
                        </a>
                        <a href="#" style="background: rgba(255, 255, 255, 0.1); padding: 12px; border-radius: 12px; color: #d1d5db; text-decoration: none; transition: all 0.3s ease; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.1);" onmouseover="this.style.background='rgba(5, 150, 105, 0.2)'; this.style.color='#10b981'; this.style.transform='translateY(-2px)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'; this.style.color='#d1d5db'; this.style.transform='translateY(0)'">
                            <span class="material-icons" style="font-size: 20px;">share</span>
                        </a>
                    </div>
                </div>

                <!-- Enlaces principales -->
                <div>
                    <h3 style="margin: 0 0 24px 0; font-size: 18px; font-weight: 700; color: white; display: flex; align-items: center; gap: 8px;">
                        <span class="material-icons" style="color: #10b981;">navigation</span>
                        Enlaces
                    </h3>
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li style="margin-bottom: 12px;">
                            <a href="/" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">home</span>
                                Inicio
                            </a>
                        </li>
                        <li style="margin-bottom: 12px;">
                            <a href="/biblioteca" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">library_books</span>
                                Biblioteca
                            </a>
                        </li>
                        <li style="margin-bottom: 12px;">
                            <a href="/scanner" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">photo_camera</span>
                                Scanner
                            </a>
                        </li>
                        <li style="margin-bottom: 12px;">
                            <a href="/calendario" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">event</span>
                                Calendario
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Comunidad -->
                <div>
                    <h3 style="margin: 0 0 24px 0; font-size: 18px; font-weight: 700; color: white; display: flex; align-items: center; gap: 8px;">
                        <span class="material-icons" style="color: #10b981;">groups</span>
                        Comunidad
                    </h3>
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li style="margin-bottom: 12px;">
                            <a href="/clubes" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">diversity_3</span>
                                Clubes
                            </a>
                        </li>
                        <li style="margin-bottom: 12px;">
                            <a href="#" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">article</span>
                                Blog
                            </a>
                        </li>
                        <li style="margin-bottom: 12px;">
                            <a href="#" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">contact_support</span>
                                Contacto
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Legal -->
                <div>
                    <h3 style="margin: 0 0 24px 0; font-size: 18px; font-weight: 700; color: white; display: flex; align-items: center; gap: 8px;">
                        <span class="material-icons" style="color: #10b981;">gavel</span>
                        Legal
                    </h3>
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li style="margin-bottom: 12px;">
                            <a href="/register" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">description</span>
                                Términos de Uso
                            </a>
                        </li>
                        <li style="margin-bottom: 12px;">
                            <a href="/ajustes" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">privacy_tip</span>
                                Política de Privacidad
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Línea divisoria -->
            <div style="height: 1px; background: linear-gradient(90deg, transparent 0%, rgba(5, 150, 105, 0.3) 50%, transparent 100%); margin: 32px 0;"></div>

            <!-- Footer bottom -->
            <div style="display: flex; justify-content: space-between; align-items: center; padding-bottom: 32px; flex-wrap: wrap; gap: 16px;">
                <p style="margin: 0; color: #9ca3af; font-size: 14px;">
                    © 2025 <span style="color: #10b981; font-weight: 600;">PlantCare</span>. Todos los derechos reservados.
                </p>
                <div style="display: flex; align-items: center; gap: 16px; color: #9ca3af; font-size: 14px;">
                    <span style="display: flex; align-items: center; gap: 4px;">
                        <span class="material-icons" style="font-size: 16px; color: #10b981;">eco</span>
                        Hecho con amor para las plantas
                    </span>
                    <span style="display: flex; align-items: center; gap: 4px;">
                        <span class="material-icons" style="font-size: 16px; color: #10b981;">location_on</span>
                        Chihuahua, México
                    </span>
                </div>
            </div>
        </div>
    </footer>

    <script src="{{ url_for('static', filename='js/home-navbar.js') }}"></script>
    <script>
        // JavaScript básico para clubes
        document.addEventListener('DOMContentLoaded', function() {
            // Funcionalidad de unirse a clubes
            document.querySelectorAll('.join-club').forEach(button => {
                button.addEventListener('click', function() {
                    const clubId = this.dataset.clubId;
                    const isJoined = this.classList.contains('joined');

                    if (isJoined) {
                        // Salir del club
                        this.classList.remove('joined');
                        this.innerHTML = '<span class="material-icons">group_add</span> Unirse';
                        this.style.background = 'linear-gradient(135deg, #059669, #10b981)';
                        alert('Has salido del club');
                    } else {
                        // Unirse al club
                        this.classList.add('joined');
                        this.innerHTML = '<span class="material-icons">check</span> Miembro';
                        this.style.background = '#10b981';
                        alert('¡Te has unido al club exitosamente!');
                    }
                });
            });

            // Funcionalidad de ver club
            document.querySelectorAll('.view-club').forEach(button => {
                button.addEventListener('click', function() {
                    const clubId = this.dataset.clubId;
                    alert(`Abriendo club ${clubId}. Esta funcionalidad estará disponible pronto.`);
                });
            });

            // Filtros de categoría
            document.querySelectorAll('.category-filter').forEach(filter => {
                filter.addEventListener('click', function() {
                    // Remover active de todos
                    document.querySelectorAll('.category-filter').forEach(f => f.classList.remove('active'));
                    // Agregar active al clickeado
                    this.classList.add('active');

                    const category = this.dataset.category;
                    const cards = document.querySelectorAll('.club-card');

                    cards.forEach(card => {
                        if (category === 'all' || card.dataset.category === category) {
                            card.style.display = 'block';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            });

            // Búsqueda básica
            const searchInput = document.querySelector('.search-box input');
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    const cards = document.querySelectorAll('.club-card');

                    cards.forEach(card => {
                        const title = card.querySelector('h3').textContent.toLowerCase();
                        const description = card.querySelector('p').textContent.toLowerCase();

                        if (title.includes(searchTerm) || description.includes(searchTerm)) {
                            card.style.display = 'block';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            }

            // Crear club
            const createBtn = document.getElementById('create-club-btn');
            if (createBtn) {
                createBtn.addEventListener('click', function() {
                    alert('Funcionalidad de crear club estará disponible pronto. ¡Gracias por tu interés!');
                });
            }
        });
    </script>
</body>
</html>
