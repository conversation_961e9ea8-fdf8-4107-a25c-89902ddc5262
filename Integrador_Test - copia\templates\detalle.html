<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detalle de Planta - PlantCare</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="../static/css/detalle.css">
</head>
<body>
    <header class="app-header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <span class="material-icons">local_florist</span>
                    <h1>PlantCare</h1>
                </div>
                <nav class="main-nav">
                    <ul>
                        <li><a href="/">Inicio</a></li>
                        <li><a href="/biblioteca" class="active">Biblioteca</a></li>
                        <li><a href="/diagnosis/scanner">Scanner</a></li>
                        <li><a href="/calendario">Calendario</a></li>
                        <!-- <li><a href="foro.html">Foro</a></li> -->
                        <li><a href="/">Recomendaciones</a></li>
                    </ul>
                </nav>
                <div class="user-actions">
                    <button id="theme-toggle" class="icon-button">
                        <span class="material-icons">dark_mode</span>
                    </button>
                    <div class="user-menu">
                        <button id="user-menu-button" class="avatar">
                            <img src="assets/avatars/default.jpg" alt="Usuario">
                        </button>
                        <div id="user-dropdown" class="dropdown-menu">
                            <a href="perfil.html">
                                <span class="material-icons">person</span>
                                Mi Perfil
                            </a>
                            <a href="/ajustes">
                                <span class="material-icons">settings</span>
                                Ajustes
                            </a>
                            <a href="#" id="logout-button">
                                <span class="material-icons">logout</span>
                                Cerrar Sesión
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <main>
        <div class="container">
            <div class="back-button">
                <a href="/biblioteca" class="btn btn-outline">
                    <span class="material-icons">arrow_back</span>
                    Volver a la Biblioteca
                </a>
            </div>

            <div class="plant-detail-container">
                <div class="plant-gallery">
                    <div class="main-image">
                        <img id="main-plant-image" src="" alt="">
                        <div class="plant-badges">
                            <span class="badge native-badge">Nativa</span>
                            <span class="badge category-badge"></span>
                        </div>
                    </div>
                    <div class="thumbnail-gallery">
                        <!-- Las miniaturas se generarán dinámicamente con JavaScript -->
                    </div>
                </div>

                <div class="plant-info-container">
                    <div class="plant-header">
                        <h1 id="plant-name" class="plant-title"></h1>
                        <p id="plant-scientific-name" class="scientific-name"></p>
                    </div>

                    <div class="plant-attributes">
                        <div class="attribute-card">
                            <span class="material-icons">opacity</span>
                            <div class="attribute-info">
                                <h3>Riego</h3>
                                <p id="water-needs"></p>
                            </div>
                        </div>

                        <div class="attribute-card">
                            <span class="material-icons">wb_sunny</span>
                            <div class="attribute-info">
                                <h3>Luz Solar</h3>
                                <p id="sun-needs"></p>
                            </div>
                        </div>

                        <div class="attribute-card">
                            <span class="material-icons">thermostat</span>
                            <div class="attribute-info">
                                <h3>Temperatura</h3>
                                <p id="temperature-needs"></p>
                            </div>
                        </div>

                        <div class="attribute-card">
                            <span class="material-icons">psychology</span>
                            <div class="attribute-info">
                                <h3>Dificultad</h3>
                                <p id="difficulty-level"></p>
                            </div>
                        </div>
                    </div>

                    <div class="plant-actions">
                        <button id="add-to-garden" class="btn btn-primary">
                            <span class="material-icons">add</span>
                            Añadir a Mi Jardín
                        </button>
                        <button id="add-to-calendar" class="btn btn-outline">
                            <span class="material-icons">event</span>
                            Programar Recordatorio
                        </button>
                        <button id="share-plant" class="btn btn-outline">
                            <span class="material-icons">share</span>
                            Compartir
                        </button>
                    </div>
                </div>
            </div>

            <div class="plant-details-tabs">
                <div class="tabs-header">
                    <button class="tab-button active" data-tab="description">Descripción</button>
                    <button class="tab-button" data-tab="care">Cuidados</button>
                    <button class="tab-button" data-tab="seasons">Temporadas</button>
                    <button class="tab-button" data-tab="tips">Consejos</button>
                </div>

                <div class="tabs-content">
                    <div id="description-tab" class="tab-content active">
                        <h2>Descripción</h2>
                        <p id="plant-description"></p>

                        <div class="plant-characteristics">
                            <h3>Características</h3>
                            <ul id="plant-characteristics"></ul>
                        </div>

                        <div class="plant-habitat">
                            <h3>Hábitat Natural</h3>
                            <p id="plant-habitat"></p>
                        </div>
                    </div>

                    <div id="care-tab" class="tab-content">
                        <h2>Cuidados</h2>

                        <div class="care-section">
                            <h3>Riego</h3>
                            <p id="watering-instructions"></p>
                        </div>

                        <div class="care-section">
                            <h3>Luz</h3>
                            <p id="light-instructions"></p>
                        </div>

                        <div class="care-section">
                            <h3>Suelo</h3>
                            <p id="soil-instructions"></p>
                        </div>

                        <div class="care-section">
                            <h3>Fertilización</h3>
                            <p id="fertilizer-instructions"></p>
                        </div>

                        <div class="care-section">
                            <h3>Poda</h3>
                            <p id="pruning-instructions"></p>
                        </div>
                    </div>

                    <div id="seasons-tab" class="tab-content">
                        <h2>Temporadas</h2>

                        <div class="seasons-container">
                            <div class="season-card">
                                <h3>Primavera</h3>
                                <div class="season-icon">
                                    <span class="material-icons">eco</span>
                                </div>
                                <p id="spring-care"></p>
                            </div>

                            <div class="season-card">
                                <h3>Verano</h3>
                                <div class="season-icon">
                                    <span class="material-icons">wb_sunny</span>
                                </div>
                                <p id="summer-care"></p>
                            </div>

                            <div class="season-card">
                                <h3>Otoño</h3>
                                <div class="season-icon">
                                    <span class="material-icons">eco</span>
                                </div>
                                <p id="fall-care"></p>
                            </div>

                            <div class="season-card">
                                <h3>Invierno</h3>
                                <div class="season-icon">
                                    <span class="material-icons">ac_unit</span>
                                </div>
                                <p id="winter-care"></p>
                            </div>
                        </div>
                    </div>

                    <div id="tips-tab" class="tab-content">
                        <h2>Consejos y Problemas Comunes</h2>

                        <div class="tips-section">
                            <h3>Problemas Comunes</h3>
                            <div id="common-problems" class="problems-list"></div>
                        </div>

                        <div class="tips-section">
                            <h3>Consejos de Expertos</h3>
                            <ul id="expert-tips" class="tips-list"></ul>
                        </div>
                    </div>
                </div>
            </div>

            <section class="related-plants">
                <h2>Plantas Relacionadas</h2>
                <div class="related-plants-grid" id="related-plants-grid">
                    <!-- Las tarjetas de plantas relacionadas se generarán dinámicamente con JavaScript -->
                </div>
            </section>
        </div>
    </main>

    <footer class="app-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="logo">
                        <span class="material-icons">local_florist</span>
                        <h2>PlantCare</h2>
                    </div>
                    <p>Tu asistente para el cuidado de plantas nativas de Chihuahua.</p>
                </div>

                <div class="footer-section">
                    <h3>Enlaces</h3>
                    <ul>
                        <li><a href="/">Inicio</a></li>
                        <li><a href="/biblioteca">Biblioteca</a></li>
                        <li><a href="/diagnosis/scanner">Scanner</a></li>
                        <li><a href="calendario.html">Calendario</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Comunidad</h3>
                    <ul>
                        <li><a href="foro.html">Foro</a></li>
                        <li><a href="blog.html">Blog</a></li>
                        <li><a href="contacto.html">Contacto</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Legal</h3>
                    <ul>
                        <li><a href="terminos.html">Términos de Uso</a></li>
                        <li><a href="privacidad.html">Política de Privacidad</a></li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2025 PlantCare. Todos los derechos reservados.</p>
            </div>
        </div>
    </footer>

    <!-- Templates para problemas comunes -->
    <template id="problem-card-template">
        <div class="problem-card">
            <div class="problem-header">
                <span class="material-icons problem-icon">error_outline</span>
                <h4 class="problem-title"></h4>
            </div>
            <div class="problem-content">
                <p class="problem-description"></p>
                <div class="problem-solution">
                    <h5>Solución:</h5>
                    <p class="solution-text"></p>
                </div>
            </div>
        </div>
    </template>

    <!-- Template para miniaturas de galería -->
    <template id="thumbnail-template">
        <div class="thumbnail">
            <img src="" alt="">
        </div>
    </template>

    <!-- Template para plantas relacionadas -->
    <template id="related-plant-template">
        <div class="related-plant-card">
            <div class="related-plant-image">
                <img src="" alt="">
            </div>
            <div class="related-plant-info">
                <h3 class="related-plant-name"></h3>
                <p class="related-plant-scientific-name"></p>
            </div>
        </div>
    </template>

    <script src="js/theme-toggle.js"></script>
    <script src="js/user-menu.js"></script>
    <script src="js/detalle.js"></script>
</body>
</html>

