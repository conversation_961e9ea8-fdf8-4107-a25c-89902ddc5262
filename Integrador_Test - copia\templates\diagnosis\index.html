<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnóstico IA - PlantCare</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 50%, #1B5E20 100%);
            min-height: 100vh;
            color: white;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 300;
            margin-bottom: 20px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            transition: transform 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .feature-card:hover {
            transform: translateY(-10px);
        }

        .feature-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            display: block;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            font-weight: 500;
        }

        .feature-card p {
            opacity: 0.9;
            line-height: 1.6;
        }

        .cta-section {
            text-align: center;
            margin-bottom: 50px;
        }

        .cta-button {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 20px 40px;
            border-radius: 50px;
            text-decoration: none;
            font-size: 1.2rem;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .cta-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .how-it-works {
            margin-bottom: 50px;
        }

        .how-it-works h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 40px;
            font-weight: 300;
        }

        .steps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
        }

        .step-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            position: relative;
        }

        .step-number {
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            background: #FF9800;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .step-card h4 {
            margin: 20px 0 15px 0;
            font-size: 1.2rem;
        }

        .nav-links {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            justify-content: center;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .nav-links a:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .status-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 25px;
            font-size: 0.9rem;
            z-index: 1000;
        }

        .status-online {
            background: rgba(76, 175, 80, 0.9);
        }

        .status-offline {
            background: rgba(244, 67, 54, 0.9);
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .cta-button {
                padding: 15px 30px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Indicador de estado -->
    <div id="status-indicator" class="status-indicator">
        <span id="status-text">Verificando sistema...</span>
    </div>

    <!-- Navegación -->
    <div class="nav-links">
        <a href="{{ url_for('index') }}">🏠 Inicio</a>
        <a href="{{ url_for('diagnosis.upload_image') }}">📸 Diagnosticar</a>
        <a href="{{ url_for('diagnosis.diagnosis_help') }}">❓ Ayuda</a>
    </div>

    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🤖 Diagnóstico con Inteligencia Artificial</h1>
            <p>Utiliza tecnología de vanguardia para identificar enfermedades y problemas en tus plantas. 
               Sube una foto y obtén un diagnóstico instantáneo con recomendaciones de tratamiento.</p>
        </div>

        <!-- Características principales -->
        <div class="features-grid">
            <div class="feature-card">
                <span class="feature-icon">🔍</span>
                <h3>Detección Precisa</h3>
                <p>Nuestro modelo de IA puede identificar más de 38 tipos diferentes de enfermedades y condiciones en plantas.</p>
            </div>

            <div class="feature-card">
                <span class="feature-icon">⚡</span>
                <h3>Resultados Instantáneos</h3>
                <p>Obtén un diagnóstico completo en segundos, sin necesidad de esperar días por una consulta.</p>
            </div>

            <div class="feature-card">
                <span class="feature-icon">💊</span>
                <h3>Recomendaciones de Tratamiento</h3>
                <p>Recibe consejos específicos de tratamiento y prevención adaptados a la condición detectada.</p>
            </div>

            <div class="feature-card">
                <span class="feature-icon">🌱</span>
                <h3>Especializado en Plantas Nativas</h3>
                <p>Optimizado para plantas del desierto de Chihuahua y especies comunes en México.</p>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="cta-section">
            <a href="{{ url_for('diagnosis.upload_image') }}" class="cta-button">
                <span class="material-icons">camera_alt</span>
                Diagnosticar Mi Planta Ahora
            </a>
        </div>

        <!-- Cómo funciona -->
        <div class="how-it-works">
            <h2>¿Cómo Funciona?</h2>
            <div class="steps-grid">
                <div class="step-card">
                    <div class="step-number">1</div>
                    <h4>Toma una Foto</h4>
                    <p>Captura una imagen clara de tu planta, enfocándote en las áreas problemáticas.</p>
                </div>

                <div class="step-card">
                    <div class="step-number">2</div>
                    <h4>Sube la Imagen</h4>
                    <p>Carga la foto en nuestro sistema usando el botón de diagnóstico.</p>
                </div>

                <div class="step-card">
                    <div class="step-number">3</div>
                    <h4>Análisis IA</h4>
                    <p>Nuestro modelo analiza la imagen usando redes neuronales avanzadas.</p>
                </div>

                <div class="step-card">
                    <div class="step-number">4</div>
                    <h4>Recibe Resultados</h4>
                    <p>Obtén un diagnóstico detallado con recomendaciones de tratamiento.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Verificar estado del sistema
        async function checkSystemStatus() {
            try {
                const response = await fetch('/diagnosis/api/status');
                const data = await response.json();
                
                const indicator = document.getElementById('status-indicator');
                const statusText = document.getElementById('status-text');
                
                if (data.status === 'operational') {
                    indicator.className = 'status-indicator status-online';
                    statusText.textContent = '✅ Sistema IA Operativo';
                } else {
                    indicator.className = 'status-indicator status-offline';
                    statusText.textContent = '⚠️ Sistema IA No Disponible';
                }
            } catch (error) {
                const indicator = document.getElementById('status-indicator');
                const statusText = document.getElementById('status-text');
                indicator.className = 'status-indicator status-offline';
                statusText.textContent = '❌ Error de Conexión';
            }
        }

        // Verificar estado al cargar la página
        document.addEventListener('DOMContentLoaded', checkSystemStatus);

        // Verificar estado cada 30 segundos
        setInterval(checkSystemStatus, 30000);
    </script>
</body>
</html>
