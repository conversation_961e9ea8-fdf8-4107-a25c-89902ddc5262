<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Iniciar <PERSON><PERSON>ón - PlantCare</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow: hidden;
            margin: 0;
        }

        /* Burbujas animadas en el fondo */
        .bubbles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .bubble {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .bubble:nth-child(1) {
            width: 80px;
            height: 80px;
            left: 10%;
            animation-delay: 0s;
            animation-duration: 6s;
        }

        .bubble:nth-child(2) {
            width: 60px;
            height: 60px;
            left: 20%;
            animation-delay: 1s;
            animation-duration: 8s;
        }

        .bubble:nth-child(3) {
            width: 100px;
            height: 100px;
            left: 35%;
            animation-delay: 2s;
            animation-duration: 7s;
        }

        .bubble:nth-child(4) {
            width: 40px;
            height: 40px;
            left: 50%;
            animation-delay: 3s;
            animation-duration: 9s;
        }

        .bubble:nth-child(5) {
            width: 70px;
            height: 70px;
            left: 70%;
            animation-delay: 4s;
            animation-duration: 6s;
        }

        .bubble:nth-child(6) {
            width: 90px;
            height: 90px;
            left: 85%;
            animation-delay: 5s;
            animation-duration: 8s;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        .auth-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 450px;
            height: 85vh;
            max-height: 600px;
            animation: slideUp 0.6s ease-out;
            position: relative;
            z-index: 10;
            perspective: 1000px;
            display: flex;
            flex-direction: column;
        }

        .auth-toggle {
            position: absolute;
            top: -50px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 8px 16px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .auth-toggle:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        .form-container {
            transition: transform 0.8s ease-in-out;
            transform-style: preserve-3d;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .form-container.flipped {
            transform: rotateY(180deg);
        }

        .login-form, .register-form {
            backface-visibility: hidden;
            position: relative;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .register-form {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            transform: rotateY(180deg);
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .auth-header {
            background: linear-gradient(135deg, #4CAF50, #2E7D32);
            color: white;
            padding: 30px 30px;
            text-align: center;
            flex-shrink: 0;
        }

        .auth-header .logo {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
        }

        .auth-header .logo .material-icons {
            font-size: 32px;
            margin-right: 8px;
        }

        .auth-header h1 {
            font-size: 24px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .auth-header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .form-content {
            padding: 30px 30px;
            overflow-y: auto;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        /* Estilos personalizados para el scrollbar */
        .form-content::-webkit-scrollbar {
            width: 6px;
        }

        .form-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .form-content::-webkit-scrollbar-thumb {
            background: #4CAF50;
            border-radius: 3px;
        }

        .form-content::-webkit-scrollbar-thumb:hover {
            background: #2E7D32;
        }

        .form-row {
            display: flex;
            gap: 16px;
            margin-bottom: 24px;
        }

        .form-group {
            margin-bottom: 24px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
            font-size: 14px;
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
            background: #fafafa;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4CAF50;
            background: white;
        }

        .form-group .material-icons {
            position: absolute;
            right: 12px;
            top: 38px;
            color: #999;
            font-size: 20px;
        }

        .auth-btn {
            width: 100%;
            background: linear-gradient(135deg, #4CAF50, #2E7D32);
            color: white;
            border: none;
            padding: 14px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            margin-bottom: 20px;
        }

        .auth-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3);
        }

        .auth-btn:active {
            transform: translateY(0);
        }

        .divider {
            text-align: center;
            margin: 20px 0;
            position: relative;
            color: #999;
            font-size: 14px;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e0e0e0;
            z-index: 1;
        }

        .divider span {
            background: white;
            padding: 0 16px;
            position: relative;
            z-index: 2;
        }

        .auth-link {
            text-align: center;
            margin-top: 20px;
        }

        .auth-link a {
            color: #4CAF50;
            text-decoration: none;
            font-weight: 500;
        }

        .auth-link a:hover {
            text-decoration: underline;
        }

        .terms {
            font-size: 12px;
            color: #666;
            text-align: center;
            margin-top: 16px;
            line-height: 1.4;
        }

        .terms a {
            color: #4CAF50;
            text-decoration: none;
        }

        .terms a:hover {
            text-decoration: underline;
        }

        .back-home {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
            font-weight: 500;
            transition: opacity 0.3s;
        }

        .back-home:hover {
            opacity: 0.8;
        }

        .back-home .material-icons {
            margin-right: 8px;
        }

        /* Florecita decorativa */
        .decorative-flower {
            position: fixed;
            bottom: 20px;
            left: 20px;
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1000;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .decorative-flower:hover {
            transform: scale(1.1);
            background: rgba(255, 255, 255, 0.3);
        }

        .decorative-flower .material-icons {
            font-size: 32px;
            color: white;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        /* Animaciones especiales */
        .weather-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 5;
        }

        .sun-emoji {
            position: absolute;
            top: 15%;
            right: 15%;
            font-size: 80px;
            animation: sun-bounce 3s ease-in-out infinite;
            filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8));
            text-shadow: 0 0 30px rgba(255, 165, 0, 0.6);
        }

        @keyframes sun-bounce {
            0%, 100% {
                transform: scale(1) rotate(0deg);
                filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8));
            }
            25% {
                transform: scale(1.2) rotate(90deg);
                filter: drop-shadow(0 0 40px rgba(255, 215, 0, 1));
            }
            50% {
                transform: scale(1.1) rotate(180deg);
                filter: drop-shadow(0 0 30px rgba(255, 215, 0, 0.9));
            }
            75% {
                transform: scale(1.3) rotate(270deg);
                filter: drop-shadow(0 0 50px rgba(255, 215, 0, 1));
            }
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .rain-drop {
            position: absolute;
            background: linear-gradient(to bottom, #00BFFF, #1E90FF, #4169E1);
            width: 4px;
            height: 25px;
            border-radius: 0 0 50% 50%;
            animation: fall linear infinite;
            box-shadow: 0 0 3px rgba(30, 144, 255, 0.5);
            filter: brightness(1.3);
        }

        @keyframes fall {
            from {
                transform: translateY(-100vh) rotate(0deg);
                opacity: 1;
            }
            to {
                transform: translateY(100vh) rotate(5deg);
                opacity: 0.3;
            }
        }

        .bee {
            position: absolute;
            font-size: 32px;
            animation: fly 6s ease-in-out infinite;
            filter: brightness(1.2) contrast(1.1);
            text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
        }

        @keyframes fly {
            0%, 100% {
                transform: translate(0, 0) rotate(0deg) scale(1);
            }
            20% {
                transform: translate(150px, -80px) rotate(10deg) scale(1.1);
            }
            40% {
                transform: translate(300px, 30px) rotate(-15deg) scale(0.9);
            }
            60% {
                transform: translate(450px, -60px) rotate(20deg) scale(1.2);
            }
            80% {
                transform: translate(200px, -30px) rotate(-10deg) scale(1);
            }
        }

        .cat {
            position: absolute;
            font-size: 40px;
            animation: walk 8s ease-in-out infinite;
            filter: brightness(1.1) contrast(1.2);
            text-shadow: 0 0 8px rgba(255, 192, 203, 0.6);
        }

        @keyframes walk {
            0% {
                transform: translateX(-80px) scaleX(1);
                left: 0%;
            }
            25% {
                transform: translateX(-20px) scaleX(1) rotateZ(5deg);
                left: 25%;
            }
            50% {
                transform: translateX(0px) scaleX(-1);
                left: 70%;
            }
            75% {
                transform: translateX(20px) scaleX(-1) rotateZ(-5deg);
                left: 45%;
            }
            100% {
                transform: translateX(-80px) scaleX(1);
                left: 0%;
            }
        }

        /* Mensajes flash */
        .flash-messages {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            max-width: 400px;
        }

        .flash-message {
            padding: 15px 20px;
            margin-bottom: 10px;
            border-radius: 10px;
            color: white;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            animation: slideIn 0.3s ease-out;
            position: relative;
            cursor: pointer;
        }

        .flash-message.success {
            background: linear-gradient(135deg, #4CAF50, #2E7D32);
        }

        .flash-message.error {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }

        .flash-message.info {
            background: linear-gradient(135deg, #2196F3, #1976D2);
        }

        .flash-message::after {
            content: '×';
            position: absolute;
            top: 5px;
            right: 10px;
            font-size: 20px;
            cursor: pointer;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 10px;
            }

            .flash-messages {
                top: 10px;
                right: 10px;
                left: 10px;
                max-width: none;
            }

            .auth-container {
                margin: 0;
                height: 90vh;
                max-height: none;
            }

            .auth-header {
                padding: 20px 20px;
            }

            .form-content {
                padding: 20px 20px;
            }

            .form-row {
                flex-direction: column;
                gap: 0;
            }

            .auth-toggle {
                top: -35px;
                right: 10px;
                font-size: 12px;
                padding: 6px 12px;
            }

            .decorative-flower {
                width: 50px;
                height: 50px;
                bottom: 15px;
                left: 15px;
            }

            .decorative-flower .material-icons {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <!-- Burbujas animadas -->
    <div class="bubbles">
        <div class="bubble"></div>
        <div class="bubble"></div>
        <div class="bubble"></div>
        <div class="bubble"></div>
        <div class="bubble"></div>
        <div class="bubble"></div>
    </div>

    <a href="/" class="back-home">
        <span class="material-icons">arrow_back</span>
        Volver al inicio
    </a>

    <!-- Florecita decorativa -->
    <div class="decorative-flower" onclick="triggerRandomAnimation()">
        <span class="material-icons">local_florist</span>
    </div>

    <!-- Contenedor para animaciones especiales -->
    <div id="weather-container" class="weather-animation"></div>

    <!-- Mensajes flash -->
    <div class="flash-messages">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="flash-message {{ category }}" onclick="this.remove()">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <div class="auth-container">
        <button class="auth-toggle" onclick="toggleForm()">
            <span id="toggle-text">¿No tienes cuenta? Registrarse</span>
        </button>

        <div class="form-container" id="form-container">
            <!-- Formulario de Login (visible por defecto) -->
            <div class="login-form">
                <div class="auth-header">
                    <div class="logo">
                        <span class="material-icons">local_florist</span>
                        <h1>PlantCare</h1>
                    </div>
                    <p>Cuida tus plantas nativas de Chihuahua</p>
                </div>

                <div class="form-content">
                    <form method="POST" action="/login">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}"/>
                        <div class="form-group">
                            <label for="login_username">Nombre de usuario</label>
                            <input type="text" id="login_username" name="username" required>
                            <span class="material-icons">account_circle</span>
                        </div>

                        <div class="form-group">
                            <label for="login_password">Contraseña</label>
                            <input type="password" id="login_password" name="password" required>
                            <span class="material-icons">lock</span>
                        </div>

                        <button type="submit" class="auth-btn">
                            Iniciar Sesión
                        </button>
                    </form>

                    <div class="divider">
                        <span>o</span>
                    </div>

                    <div class="auth-link">
                        <p><a href="#" onclick="toggleForm()">¿No tienes cuenta? Haz una aquí</a></p>
                    </div>
                </div>
            </div>

            <!-- Formulario de Registro (oculto por defecto) -->
            <div class="register-form">
                <div class="auth-header">
                    <div class="logo">
                        <span class="material-icons">local_florist</span>
                        <h1>PlantCare</h1>
                    </div>
                    <p>Únete a la comunidad de cuidadores de plantas</p>
                </div>

                <div class="form-content">
                    <form method="POST" action="/register">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}"/>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="reg_first_name">Nombre</label>
                                <input type="text" id="reg_first_name" name="first_name" required>
                                <span class="material-icons">person</span>
                            </div>
                            <div class="form-group">
                                <label for="reg_last_name">Apellido</label>
                                <input type="text" id="reg_last_name" name="last_name" required>
                                <span class="material-icons">person_outline</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="reg_username">Nombre de usuario</label>
                            <input type="text" id="reg_username" name="username" required>
                            <span class="material-icons">account_circle</span>
                        </div>

                        <div class="form-group">
                            <label for="reg_email">Correo electrónico</label>
                            <input type="email" id="reg_email" name="email" required>
                            <span class="material-icons">email</span>
                        </div>

                        <div class="form-group">
                            <label for="reg_password">Contraseña</label>
                            <input type="password" id="reg_password" name="password" required>
                            <span class="material-icons">lock</span>
                        </div>

                        <div class="form-group">
                            <label for="reg_confirm_password">Confirmar contraseña</label>
                            <input type="password" id="reg_confirm_password" name="confirm_password" required>
                            <span class="material-icons">lock_outline</span>
                        </div>

                        <button type="submit" class="auth-btn">
                            Crear cuenta
                        </button>

                        <div class="terms">
                            Al registrarte, aceptas nuestros
                            <a href="#">Términos de Servicio</a> y
                            <a href="#">Política de Privacidad</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isRegisterMode = false;

        function toggleForm() {
            const container = document.getElementById('form-container');
            const toggleText = document.getElementById('toggle-text');

            isRegisterMode = !isRegisterMode;

            if (isRegisterMode) {
                container.classList.add('flipped');
                toggleText.textContent = '¿Ya tienes cuenta? Iniciar sesión';
            } else {
                container.classList.remove('flipped');
                toggleText.textContent = '¿No tienes cuenta? Registrarse';
            }
        }

        // Efecto de burbujas adicionales
        function createBubble() {
            const bubble = document.createElement('div');
            bubble.classList.add('bubble');

            const size = Math.random() * 60 + 20;
            bubble.style.width = size + 'px';
            bubble.style.height = size + 'px';
            bubble.style.left = Math.random() * 100 + '%';
            bubble.style.animationDuration = (Math.random() * 3 + 4) + 's';
            bubble.style.animationDelay = Math.random() * 2 + 's';

            document.querySelector('.bubbles').appendChild(bubble);

            setTimeout(() => {
                bubble.remove();
            }, 8000);
        }

        // Crear burbujas adicionales cada 3 segundos
        setInterval(createBubble, 3000);

        // Animaciones aleatorias de la florecita
        function triggerRandomAnimation() {
            const animations = ['sun', 'rain', 'bees', 'cats'];
            const randomAnimation = animations[Math.floor(Math.random() * animations.length)];

            // Limpiar animaciones anteriores
            const container = document.getElementById('weather-container');
            container.innerHTML = '';

            switch(randomAnimation) {
                case 'sun':
                    createSunAnimation();
                    break;
                case 'rain':
                    createRainAnimation();
                    break;
                case 'bees':
                    createBeesAnimation();
                    break;
                case 'cats':
                    createCatsAnimation();
                    break;
            }
        }

        function createSunAnimation() {
            const container = document.getElementById('weather-container');

            // Crear sol con emoji
            const sun = document.createElement('div');
            sun.className = 'sun-emoji';
            sun.textContent = '☀️';

            container.appendChild(sun);

            // Remover después de 5 segundos
            setTimeout(() => {
                if (sun.parentNode) {
                    sun.remove();
                }
            }, 5000);
        }

        function createRainAnimation() {
            const container = document.getElementById('weather-container');

            for (let i = 0; i < 80; i++) {
                setTimeout(() => {
                    const drop = document.createElement('div');
                    drop.className = 'rain-drop';
                    drop.style.left = Math.random() * 100 + '%';
                    drop.style.animationDuration = (Math.random() * 0.8 + 0.4) + 's';
                    drop.style.animationDelay = Math.random() * 1 + 's';
                    drop.style.opacity = Math.random() * 0.7 + 0.3;
                    container.appendChild(drop);

                    // Remover gota después de la animación
                    setTimeout(() => {
                        if (drop.parentNode) {
                            drop.remove();
                        }
                    }, 2500);
                }, i * 50);
            }
        }

        function createBeesAnimation() {
            const container = document.getElementById('weather-container');
            const beeEmojis = ['🐝', '🐛', '🦋', '🐞', '🦗'];

            for (let i = 0; i < 8; i++) {
                const bee = document.createElement('div');
                bee.className = 'bee';
                bee.textContent = beeEmojis[Math.floor(Math.random() * beeEmojis.length)];
                bee.style.top = Math.random() * 60 + 10 + '%';
                bee.style.left = Math.random() * 30 + '%';
                bee.style.animationDelay = i * 0.3 + 's';
                bee.style.animationDuration = (Math.random() * 2 + 4) + 's';
                container.appendChild(bee);

                // Remover después de 6 segundos
                setTimeout(() => {
                    if (bee.parentNode) {
                        bee.remove();
                    }
                }, 6000);
            }
        }

        function createCatsAnimation() {
            const container = document.getElementById('weather-container');
            const catEmojis = ['🐱', '🐈', '😸', '😺', '🐈‍⬛', '😻', '😽'];

            for (let i = 0; i < 5; i++) {
                setTimeout(() => {
                    const cat = document.createElement('div');
                    cat.className = 'cat';
                    cat.textContent = catEmojis[Math.floor(Math.random() * catEmojis.length)];
                    cat.style.bottom = Math.random() * 40 + 5 + '%';
                    cat.style.animationDelay = i * 0.5 + 's';
                    cat.style.animationDuration = (Math.random() * 2 + 6) + 's';
                    container.appendChild(cat);

                    // Remover después de 8 segundos
                    setTimeout(() => {
                        if (cat.parentNode) {
                            cat.remove();
                        }
                    }, 8000);
                }, i * 800);
            }
        }
    </script>
</body>
</html>