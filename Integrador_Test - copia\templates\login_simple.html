<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Simple - PlantCare</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #2e7d32;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        input[type="text"], input[type="password"], input[type="email"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        input[type="text"]:focus, input[type="password"]:focus, input[type="email"]:focus {
            border-color: #4caf50;
            outline: none;
        }
        
        button {
            width: 100%;
            padding: 12px;
            background-color: #4caf50;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 10px;
        }
        
        button:hover {
            background-color: #45a049;
        }

        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }

        .password-hint {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        .password-match-error {
            font-size: 12px;
            color: #dc3545;
            margin-top: 5px;
        }
        
        .toggle-link {
            text-align: center;
            margin-top: 20px;
        }
        
        .toggle-link a {
            color: #4caf50;
            text-decoration: none;
        }
        
        .toggle-link a:hover {
            text-decoration: underline;
        }
        
        .form-container {
            display: none;
        }
        
        .form-container.active {
            display: block;
        }
        
        .flash-messages {
            margin-bottom: 20px;
        }
        
        .flash-message {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
            color: white;
        }
        
        .flash-message.success {
            background-color: #4caf50;
        }
        
        .flash-message.error {
            background-color: #f44336;
        }
        
        .flash-message.info {
            background-color: #2196f3;
        }
        
        .test-info {
            background-color: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #4caf50;
        }
        
        .test-info h3 {
            margin-top: 0;
            color: #2e7d32;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌱 PlantCare - Login Simple</h1>
        
        <!-- Información de prueba -->
        <div class="test-info">
            <h3>👤 Usuario de Prueba</h3>
            <p><strong>Username:</strong> testuser</p>
            <p><strong>Password:</strong> 123456</p>
        </div>
        
        <!-- Mensajes flash -->
        <div class="flash-messages">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="flash-message {{ category }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
        </div>
        
        <!-- Formulario de Login -->
        <div id="login-form" class="form-container active">
            <h2>Iniciar Sesión</h2>
            <form method="POST" action="/login">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}"/>
                
                <div class="form-group">
                    <label for="username">Nombre de usuario:</label>
                    <input type="text" id="username" name="username" required>
                </div>
                
                <div class="form-group">
                    <label for="password">Contraseña:</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <button type="submit">Iniciar Sesión</button>
            </form>
            
            <div class="toggle-link">
                <a href="#" onclick="toggleForm()">¿No tienes cuenta? Registrarse</a>
            </div>
        </div>
        
        <!-- Formulario de Registro -->
        <div id="register-form" class="form-container">
            <h2>Crear Cuenta</h2>
            <form method="POST" action="/register">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}"/>
                
                <div class="form-group">
                    <label for="reg_first_name">Nombre:</label>
                    <input type="text" id="reg_first_name" name="first_name" required>
                </div>
                
                <div class="form-group">
                    <label for="reg_last_name">Apellido:</label>
                    <input type="text" id="reg_last_name" name="last_name" required>
                </div>
                
                <div class="form-group">
                    <label for="reg_username">Nombre de usuario:</label>
                    <input type="text" id="reg_username" name="username" required>
                </div>
                
                <div class="form-group">
                    <label for="reg_email">Correo electrónico:</label>
                    <input type="email" id="reg_email" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="reg_password">Contraseña:</label>
                    <input type="password" id="reg_password" name="password" required minlength="8">
                    <div class="password-hint">Mínimo 8 caracteres con números y letras</div>
                </div>

                <div class="form-group">
                    <label for="reg_confirm_password">Confirmar contraseña:</label>
                    <input type="password" id="reg_confirm_password" name="confirm_password" required minlength="8">
                    <div class="password-match-error" style="display: none; color: red; font-size: 12px; margin-top: 5px;">Las contraseñas no coinciden</div>
                </div>
                
                <button type="submit">Crear Cuenta</button>
            </form>
            
            <div class="toggle-link">
                <a href="#" onclick="toggleForm()">¿Ya tienes cuenta? Iniciar sesión</a>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="/" style="color: #4caf50; text-decoration: none;">← Volver al inicio</a>
        </div>
    </div>
    
    <script>
        function toggleForm() {
            const loginForm = document.getElementById('login-form');
            const registerForm = document.getElementById('register-form');

            if (loginForm.classList.contains('active')) {
                loginForm.classList.remove('active');
                registerForm.classList.add('active');
            } else {
                registerForm.classList.remove('active');
                loginForm.classList.add('active');
            }
        }

        // Validación de contraseña para registro
        document.addEventListener('DOMContentLoaded', function() {
            const passwordInput = document.getElementById('reg_password');
            const confirmPasswordInput = document.getElementById('reg_confirm_password');
            const submitButton = document.querySelector('#register-form button[type="submit"]');
            const registerForm = document.getElementById('register-form');
            const passwordMatchError = document.querySelector('.password-match-error');

            let passwordValid = false;
            let passwordsMatch = false;

            function validatePassword() {
                const password = passwordInput.value;
                const hasMinLength = password.length >= 8;
                const hasNumbers = /[0-9]/.test(password);
                const hasLetters = /[a-zA-Z]/.test(password);

                passwordValid = hasMinLength && hasNumbers && hasLetters;
                updateSubmitButton();
                return passwordValid;
            }

            function validatePasswordMatch() {
                const password = passwordInput.value;
                const confirmPassword = confirmPasswordInput.value;

                passwordsMatch = password === confirmPassword && password.length > 0;

                if (confirmPassword.length > 0 && !passwordsMatch) {
                    passwordMatchError.style.display = 'block';
                } else {
                    passwordMatchError.style.display = 'none';
                }

                updateSubmitButton();
                return passwordsMatch;
            }

            function updateSubmitButton() {
                const canSubmit = passwordValid && passwordsMatch;
                if (submitButton) {
                    submitButton.disabled = !canSubmit;
                }
            }

            if (passwordInput) {
                passwordInput.addEventListener('input', validatePassword);
            }

            if (confirmPasswordInput) {
                confirmPasswordInput.addEventListener('input', validatePasswordMatch);
            }

            // Prevenir envío si no es válido
            if (registerForm) {
                registerForm.addEventListener('submit', function(e) {
                    if (!passwordValid || !passwordsMatch) {
                        e.preventDefault();

                        if (!passwordValid) {
                            alert('La contraseña debe tener al menos 8 caracteres, incluyendo números y letras.');
                            passwordInput.focus();
                        } else if (!passwordsMatch) {
                            alert('Las contraseñas no coinciden.');
                            confirmPasswordInput.focus();
                        }

                        return false;
                    }
                });
            }
        });

        // Auto-cerrar mensajes flash después de 5 segundos
        setTimeout(function() {
            const flashMessages = document.querySelectorAll('.flash-message');
            flashMessages.forEach(function(message) {
                message.style.opacity = '0';
                setTimeout(function() {
                    message.remove();
                }, 500);
            });
        }, 5000);
    </script>
</body>
</html>
