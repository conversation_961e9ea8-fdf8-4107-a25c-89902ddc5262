<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>Detalles de Planta - PlantCare</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/home.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/planta-detalle.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/user-menu.css') }}">
</head>
<body>
    <header>
        <nav class="main-nav">
            <div class="container">
                <div class="logo">
                    <span class="material-icons">local_florist</span>
                    <h1>PlantCare</h1>
                </div>
                <ul class="nav-links">
                    <li><a href="/">Inicio</a></li>
                    <li><a href="/biblioteca">Biblioteca</a></li>
                    <li><a href="/diagnosis/scanner">Scanner</a></li>
                    <li><a href="/calendario">Calendario</a></li>
                    <li><a href="/foro">Foro</a></li>
                </ul>
                <div class="user-actions">
                    <button id="theme-toggle" class="icon-button">
                        <span class="material-icons">dark_mode</span>
                    </button>
                    <div class="user-menu">
                        <button id="user-menu-button" class="avatar">
                            <img src="/assets/pfp.jpg" alt="Usuario">
                        </button>
                        <div id="user-dropdown" class="dropdown-menu">
                            <a href="/perfil">
                                <span class="material-icons">person</span>
                                Mi Perfil
                            </a>
                            <a href="/ajustes">
                                <span class="material-icons">settings</span>
                                Ajustes
                            </a>
                            <a href="#" id="logout-button">
                                <span class="material-icons">logout</span>
                                Cerrar Sesión
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <main>
        <div class="container">
            <!-- Breadcrumb navigation -->
            <div class="breadcrumb">
                <a href="/">Inicio</a>
                <span class="material-icons">chevron_right</span>
                <a href="/biblioteca">Biblioteca</a>
                <span class="material-icons">chevron_right</span>
                <span id="plant-name-breadcrumb">Detalles de Planta</span>
            </div>

            <!-- Plant details section -->
            <section class="plant-details">
                <div class="plant-details-grid">
                    <!-- Plant images gallery -->
                    <div class="plant-images">
                        <div class="main-image">
                            <img id="main-plant-image" src="/assets/placeholder-plant.jpg" alt="Planta">
                        </div>
                        <div class="thumbnail-gallery">
                            <img class="thumbnail active" src="/assets/placeholder-plant.jpg" alt="Imagen 1" data-src="/assets/placeholder-plant.jpg" onclick="changeMainImage(this)">
                            <img class="thumbnail" src="/assets/placeholder-plant.jpg" alt="Imagen 2" data-src="/assets/placeholder-plant.jpg" onclick="changeMainImage(this)">
                            <img class="thumbnail" src="/assets/placeholder-plant.jpg" alt="Imagen 3" data-src="/assets/placeholder-plant.jpg" onclick="changeMainImage(this)">
                        </div>
                    </div>

                    <!-- Plant basic info -->
                    <div class="plant-info-card">
                        <h1 id="plant-name">Cargando información...</h1>
                        <p id="plant-scientific-name" class="scientific-name">Nombre científico</p>

                        <div class="plant-badges">
                            <span class="badge native-badge">Nativa</span>
                        </div>

                        <div class="plant-care-overview">
                            <h3>Resumen de cuidados</h3>
                            <div class="care-attributes">
                                <div class="attribute">
                                    <span class="material-icons">opacity</span>
                                    <div>
                                        <h4>Riego</h4>
                                        <p id="plant-watering">Escaso</p>
                                    </div>
                                </div>
                                <div class="attribute">
                                    <span class="material-icons">wb_sunny</span>
                                    <div>
                                        <h4>Luz</h4>
                                        <p id="plant-sunlight">Pleno sol</p>
                                    </div>
                                </div>
                                <div class="attribute">
                                    <span class="material-icons">thermostat</span>
                                    <div>
                                        <h4>Clima</h4>
                                        <p id="plant-temperature">Semiárido</p>
                                    </div>
                                </div>
                                <div class="attribute">
                                    <span class="material-icons">terrain</span>
                                    <div>
                                        <h4>Suelo</h4>
                                        <p id="plant-soil">Bien drenado</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <button class="btn btn-primary">
                                <span class="material-icons">calendar_today</span>
                                Añadir a mi calendario
                            </button>
                            <button class="btn btn-outline">
                                <span class="material-icons">favorite_border</span>
                                Guardar
                            </button>
                            {% if current_user.is_authenticated %}
                            <button id="add-to-profile-btn" class="btn btn-success">
                                <span class="material-icons">person_add</span>
                                Añadir a mi perfil
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Plant description and detailed care -->
                <div class="plant-details-tabs">
                    <div class="tabs-header">
                        <button class="tab-button active" data-tab="description">Descripción</button>
                        <button class="tab-button" data-tab="care">Cuidados</button>
                        <button class="tab-button" data-tab="uses">Usos</button>
                        <button class="tab-button" data-tab="problems">Problemas comunes</button>
                    </div>

                    <div class="tabs-content">
                        <div class="tab-content active" id="description">
                            <h2>Descripción</h2>
                            <p id="plant-description">
                                Cargando descripción...
                            </p>

                            <h3>Biografía de la planta</h3>
                            <div id="plant-biography" class="plant-biography">
                                <p>Esta planta tiene una historia fascinante en el estado de Chihuahua. Ha sido parte del ecosistema local durante siglos y ha desarrollado adaptaciones únicas para sobrevivir en este clima.</p>
                                <p>Los habitantes locales la han utilizado tradicionalmente para diversos propósitos, desde medicinales hasta decorativos.</p>
                            </div>

                            <h3>Distribución en Chihuahua</h3>
                            <div id="plant-distribution" class="plant-distribution">
                                <p>Esta planta se encuentra principalmente en las siguientes regiones de Chihuahua:</p>
                                <ul id="plant-regions">
                                    <li>Sierra Tarahumara - Condiciones ideales por su clima templado</li>
                                    <li>Desierto de Chihuahua - Adaptada a condiciones de extrema sequía</li>
                                    <li>Valle de Casas Grandes - Suelos ricos en minerales</li>
                                </ul>
                                <p>Se desarrolla mejor en <span id="plant-best-region">zonas con exposición solar directa y suelos bien drenados</span>.</p>
                            </div>

                            <h3>Características</h3>
                            <ul id="plant-characteristics">
                                <li>Altura: <span id="plant-height">-</span></li>
                                <li>Floración: <span id="plant-bloom">-</span></li>
                                <li>Tipo de hoja: <span id="plant-leaf-type">-</span></li>
                                <li>Tamaño de hoja: <span id="plant-leaf-size">-</span></li>
                            </ul>
                        </div>

                        <div class="tab-content" id="care">
                            <h2>Guía de Cuidados</h2>

                            <h3>Riego</h3>
                            <p id="plant-watering-details">Cargando información...</p>

                            <h3>Luz y ubicación</h3>
                            <p id="plant-light-details">Cargando información...</p>

                            <h3>Temperatura y humedad</h3>
                            <p id="plant-temperature-details">Cargando información...</p>

                            <h3>Suelo y fertilización</h3>
                            <p id="plant-soil-details">Cargando información...</p>

                            <h3>Poda y mantenimiento</h3>
                            <p id="plant-pruning-details">Cargando información...</p>
                        </div>

                        <div class="tab-content" id="uses">
                            <h2>Usos</h2>
                            <div id="plant-uses">Cargando información...</div>
                        </div>

                        <div class="tab-content" id="problems">
                            <h2>Problemas comunes</h2>

                            <div class="problem-card">
                                <h3>Plagas</h3>
                                <p id="plant-pests">Cargando información...</p>
                            </div>

                            <div class="problem-card">
                                <h3>Enfermedades</h3>
                                <p id="plant-diseases">Cargando información...</p>
                            </div>

                            <div class="problem-card">
                                <h3>Otros problemas</h3>
                                <p id="plant-other-issues">Cargando información...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Related plants section -->
            <section class="related-plants">
                <h2>Plantas similares</h2>
                <div class="plants-grid">
                    <!-- This will be dynamically populated -->
                    <div class="plant-card">
                        <div class="plant-image">
                            <img src="/assets/placeholder-plant.jpg" alt="Planta similar">
                            <div class="plant-badges">
                                <span class="badge native-badge">Nativa</span>
                            </div>
                        </div>
                        <div class="plant-info">
                            <h3 class="plant-name">Nombre Planta</h3>
                            <p class="plant-scientific-name">Nombre científico</p>
                            <a href="#" class="btn btn-primary view-details">Ver Detalles</a>
                        </div>
                    </div>
                    <div class="plant-card">
                        <div class="plant-image">
                            <img src="/assets/placeholder-plant.jpg" alt="Planta similar">
                            <div class="plant-badges">
                                <span class="badge native-badge">Nativa</span>
                            </div>
                        </div>
                        <div class="plant-info">
                            <h3 class="plant-name">Nombre Planta</h3>
                            <p class="plant-scientific-name">Nombre científico</p>
                            <a href="#" class="btn btn-primary view-details">Ver Detalles</a>
                        </div>
                    </div>
                    <div class="plant-card">
                        <div class="plant-image">
                            <img src="/assets/placeholder-plant.jpg" alt="Planta similar">
                            <div class="plant-badges">
                                <span class="badge native-badge">Nativa</span>
                            </div>
                        </div>
                        <div class="plant-info">
                            <h3 class="plant-name">Nombre Planta</h3>
                            <p class="plant-scientific-name">Nombre científico</p>
                            <a href="#" class="btn btn-primary view-details">Ver Detalles</a>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Modal para añadir al calendario -->
    <div id="calendar-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Añadir recordatorio de cuidado</h2>
                <button id="close-modal" class="close-button">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="calendar-form">
                    <div class="form-group">
                        <label for="care-type">Tipo de cuidado:</label>
                        <select id="care-type" name="careType" required>
                            <option value="riego">Riego</option>
                            <option value="fertilizacion">Fertilización</option>
                            <option value="poda">Poda</option>
                            <option value="trasplante">Trasplante</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="frequency">Frecuencia:</label>
                        <select id="frequency" name="frequency" required>
                            <option value="daily">Diario</option>
                            <option value="weekly" selected>Semanal</option>
                            <option value="biweekly">Quincenal</option>
                            <option value="monthly">Mensual</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="start-date">Fecha de inicio:</label>
                        <input type="date" id="start-date" name="startDate" required>
                    </div>
                    <div class="form-group">
                        <label for="reminder">Recordatorio:</label>
                        <select id="reminder" name="reminder">
                            <option value="none">Sin recordatorio</option>
                            <option value="1hour" selected>1 hora antes</option>
                            <option value="1day">1 día antes</option>
                            <option value="2days">2 días antes</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="notes">Notas (opcional):</label>
                        <textarea id="notes" name="notes" rows="3"></textarea>
                    </div>
                    <div class="form-buttons">
                        <button type="button" id="cancel-calendar" class="btn btn-outline">Cancelar</button>
                        <button type="submit" id="save-calendar" class="btn btn-primary">Guardar</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <footer class="app-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="logo">
                        <span class="material-icons">local_florist</span>
                        <h2>PlantCare</h2>
                    </div>
                    <p>Tu asistente para el cuidado de plantas nativas de Chihuahua.</p>
                </div>

                <div class="footer-section">
                    <h3>Enlaces</h3>
                    <ul>
                        <li><a href="/">Inicio</a></li>
                        <li><a href="/biblioteca">Biblioteca</a></li>
                        <li><a href="/diagnosis/scanner">Scanner</a></li>
                        <li><a href="/calendario">Calendario</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Comunidad</h3>
                    <ul>
                        <li><a href="/foro">Foro</a></li>
                        <li><a href="blog.html">Blog</a></li>
                        <li><a href="contacto.html">Contacto</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Legal</h3>
                    <ul>
                        <li><a href="terminos.html">Términos de Uso</a></li>
                        <li><a href="privacidad.html">Política de Privacidad</a></li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2025 PlantCare. Todos los derechos reservados.</p>
            </div>
        </div>
    </footer>

    <script>
        // Function to change the main image when clicking on thumbnails
        function changeMainImage(thumbnail) {
            // Get all thumbnails and remove active class
            document.querySelectorAll('.thumbnail').forEach(thumb => {
                thumb.classList.remove('active');
            });

            // Add active class to clicked thumbnail
            thumbnail.classList.add('active');

            // Update main image
            document.getElementById('main-plant-image').src = thumbnail.getAttribute('data-src');
        }

        // Function to load plant images (to be called when loading plant details)
        function loadPlantImages(images) {
            if (images && images.length) {
                // Set main image
                document.getElementById('main-plant-image').src = images[0];

                // Update thumbnails
                const thumbnails = document.querySelectorAll('.thumbnail');
                for (let i = 0; i < Math.min(images.length, thumbnails.length); i++) {
                    thumbnails[i].src = images[i];
                    thumbnails[i].setAttribute('data-src', images[i]);
                }
            }
        }
    </script>
    <script src="{{ url_for('static', filename='js/planta-detalle.js') }}"></script>
</body>
</html>
