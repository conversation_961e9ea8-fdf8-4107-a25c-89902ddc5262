<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recomendaciones - PlantCare</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/recomendaciones.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/user-menu.css') }}">
</head>
<body>
    <header class="app-header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <span class="material-icons">local_florist</span>
                    <h1>PlantCare</h1>
                </div>
                <nav class="main-nav">
                    <ul>
                        <li><a href="/">Inicio</a></li>
                        <li><a href="/biblioteca">Biblioteca</a></li>
                        <li><a href="/scanner">Scanner</a></li>
                        <li><a href="/calendario">Calendario</a></li>
                        <li><a href="/recomendaciones" class="active">Recomendaciones</a></li>
                        <li><a href="/foro">Foro</a></li>
                    </ul>
                </nav>
                <div class="user-actions">
                    <button id="theme-toggle" class="icon-button">
                        <span class="material-icons">dark_mode</span>
                    </button>
                    <div class="user-menu">
                        <button id="user-menu-button" class="avatar">
                            <img src="/static/assets/pfp.jpg" alt="Usuario">
                        </button>
                        <div id="user-dropdown" class="dropdown-menu">
                            <a href="/perfil">
                                <span class="material-icons">person</span>
                                Mi Perfil
                            </a>
                            <a href="/ajustes">
                                <span class="material-icons">settings</span>
                                Ajustes
                            </a>
                            <a href="#" id="logout-button">
                                <span class="material-icons">logout</span>
                                Cerrar Sesión
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <main>
        <div class="container">
            <section class="page-header">
                <h1>Recomendaciones Personalizadas</h1>
                <p class="subtitle">Descubre plantas que se adaptan a tu estilo de vida y entorno</p>
            </section>

            <div class="recommendations-container">
                <div class="recommendations-sidebar">
                    <div class="filter-section">
                        <h3>Personaliza tus Recomendaciones</h3>
                        <form id="recommendations-filter">
                            <div class="form-group">
                                <label for="experience-level">Nivel de Experiencia</label>
                                <select id="experience-level">
                                    <option value="beginner">Principiante</option>
                                    <option value="intermediate">Intermedio</option>
                                    <option value="advanced">Avanzado</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="sunlight">Luz Solar Disponible</label>
                                <select id="sunlight">
                                    <option value="low">Poca luz (sombra)</option>
                                    <option value="medium">Luz media (filtrada)</option>
                                    <option value="high">Luz abundante (directa)</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="watering-frequency">Frecuencia de Riego</label>
                                <select id="watering-frequency">
                                    <option value="low">Poco frecuente</option>
                                    <option value="medium">Moderado</option>
                                    <option value="high">Frecuente</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="space">Espacio Disponible</label>
                                <select id="space">
                                    <option value="small">Pequeño (maceta, mesa)</option>
                                    <option value="medium">Mediano (estante, repisa)</option>
                                    <option value="large">Grande (jardín, patio)</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>Propósito Principal</label>
                                <div class="checkbox-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="purpose-decoration" checked>
                                        Decoración
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="purpose-purification">
                                        Purificación del aire
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="purpose-garden">
                                        Jardín comestible
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="purpose-native">
                                        Conservación nativa
                                    </label>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <span class="material-icons">filter_list</span>
                                Actualizar Recomendaciones
                            </button>
                        </form>
                    </div>
                </div>

                <div class="recommendations-content">
                    <div class="recommendations-header">
                        <h2>Plantas Recomendadas para Ti</h2>
                        <div class="recommendations-actions">
                            <select id="sort-recommendations">
                                <option value="match">Mejor coincidencia</option>
                                <option value="name-asc">Nombre (A-Z)</option>
                                <option value="name-desc">Nombre (Z-A)</option>
                                <option value="difficulty-asc">Dificultad (Menor-Mayor)</option>
                                <option value="difficulty-desc">Dificultad (Mayor-Menor)</option>
                            </select>
                            <div class="view-toggle">
                                <button id="grid-view" class="view-btn active">
                                    <span class="material-icons">grid_view</span>
                                </button>
                                <button id="list-view" class="view-btn">
                                    <span class="material-icons">view_list</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div id="plant-recommendations" class="plants-grid">
                        <!-- Planta 1 -->
                        <div class="plant-card">
                            <div class="match-badge">
                                95% Match
                            </div>
                            <div class="plant-image">
                                <img src="{{ url_for('static', filename='assets/agave.webp') }}" alt="Agave">
                                <div class="plant-badges">
                                    <span class="badge native-badge">Nativa</span>
                                </div>
                            </div>
                            <div class="plant-info">
                                <h3 class="plant-name">Agave</h3>
                                <p class="plant-scientific-name">Agave salmiana</p>
                                <div class="plant-attributes">
                                    <div class="attribute">
                                        <span class="material-icons">opacity</span>
                                        <span>Bajo</span>
                                    </div>
                                    <div class="attribute">
                                        <span class="material-icons">wb_sunny</span>
                                        <span>Alto</span>
                                    </div>
                                    <div class="attribute">
                                        <span class="material-icons">equalizer</span>
                                        <span>Fácil</span>
                                    </div>
                                </div>
                                <div class="plant-actions">
                                    <button class="btn btn-outline btn-sm">
                                        <span class="material-icons">bookmark_border</span>
                                        Guardar
                                    </button>
                                    <a href="biblioteca.html?plant=agave" class="btn btn-primary btn-sm">Ver Detalles</a>
                                </div>
                            </div>
                        </div>

                        <!-- Planta 2 -->
                        <div class="plant-card">
                            <div class="match-badge">
                                90% Match
                            </div>
                            <div class="plant-image">
                                <img src="{{ url_for('static', filename='assets/nopal.jpg') }}" alt="Nopal">
                                <div class="plant-badges">
                                    <span class="badge native-badge">Nativa</span>
                                </div>
                            </div>
                            <div class="plant-info">
                                <h3 class="plant-name">Nopal</h3>
                                <p class="plant-scientific-name">Opuntia ficus-indica</p>
                                <div class="plant-attributes">
                                    <div class="attribute">
                                        <span class="material-icons">opacity</span>
                                        <span>Bajo</span>
                                    </div>
                                    <div class="attribute">
                                        <span class="material-icons">wb_sunny</span>
                                        <span>Alto</span>
                                    </div>
                                    <div class="attribute">
                                        <span class="material-icons">equalizer</span>
                                        <span>Fácil</span>
                                    </div>
                                </div>
                                <div class="plant-actions">
                                    <button class="btn btn-outline btn-sm">
                                        <span class="material-icons">bookmark_border</span>
                                        Guardar
                                    </button>
                                    <a href="biblioteca.html?plant=nopal" class="btn btn-primary btn-sm">Ver Detalles</a>
                                </div>
                            </div>
                        </div>

                        <!-- Planta 3 -->
                        <div class="plant-card">
                            <div class="match-badge">
                                85% Match
                            </div>
                            <div class="plant-image">
                                <img src="{{ url_for('static', filename='assets/biznaga.jpg') }}" alt="Biznaga">
                                <div class="plant-badges">
                                    <span class="badge native-badge">Nativa</span>
                                </div>
                            </div>
                            <div class="plant-info">
                                <h3 class="plant-name">Biznaga</h3>
                                <p class="plant-scientific-name">Echinocactus platyacanthus</p>
                                <div class="plant-attributes">
                                    <div class="attribute">
                                        <span class="material-icons">opacity</span>
                                        <span>Muy Bajo</span>
                                    </div>
                                    <div class="attribute">
                                        <span class="material-icons">wb_sunny</span>
                                        <span>Alto</span>
                                    </div>
                                    <div class="attribute">
                                        <span class="material-icons">equalizer</span>
                                        <span>Fácil</span>
                                    </div>
                                </div>
                                <div class="plant-actions">
                                    <button class="btn btn-outline btn-sm">
                                        <span class="material-icons">bookmark_border</span>
                                        Guardar
                                    </button>
                                    <a href="biblioteca.html?plant=biznaga" class="btn btn-primary btn-sm">Ver Detalles</a>
                                </div>
                            </div>
                        </div>

                        <!-- Planta 4 -->
                        <div class="plant-card">
                            <div class="match-badge">
                                82% Match
                            </div>
                            <div class="plant-image">
                                <img src="{{ url_for('static', filename='assets/yuca.jpg') }}" alt="Yucca">
                                <div class="plant-badges">
                                    <span class="badge native-badge">Nativa</span>
                                </div>
                            </div>
                            <div class="plant-info">
                                <h3 class="plant-name">Yucca</h3>
                                <p class="plant-scientific-name">Yucca elephantipes</p>
                                <div class="plant-attributes">
                                    <div class="attribute">
                                        <span class="material-icons">opacity</span>
                                        <span>Bajo</span>
                                    </div>
                                    <div class="attribute">
                                        <span class="material-icons">wb_sunny</span>
                                        <span>Medio</span>
                                    </div>
                                    <div class="attribute">
                                        <span class="material-icons">equalizer</span>
                                        <span>Medio</span>
                                    </div>
                                </div>
                                <div class="plant-actions">
                                    <button class="btn btn-outline btn-sm">
                                        <span class="material-icons">bookmark_border</span>
                                        Guardar
                                    </button>
                                    <a href="biblioteca.html?plant=yucca" class="btn btn-primary btn-sm">Ver Detalles</a>
                                </div>
                            </div>
                        </div>

                        <!-- Planta 5 -->
                        <div class="plant-card">
                            <div class="match-badge">
                                80% Match
                            </div>
                            <div class="plant-image">
                                <img src="{{ url_for('static', filename='assets/pata_elefante.jpg') }}" alt="Pata de Elefante">
                                <div class="plant-badges">
                                    <span class="badge native-badge">Nativa</span>
                                </div>
                            </div>
                            <div class="plant-info">
                                <h3 class="plant-name">Sotol</h3>
                                <p class="plant-scientific-name">Dasylirion wheeleri</p>
                                <div class="plant-attributes">
                                    <div class="attribute">
                                        <span class="material-icons">opacity</span>
                                        <span>Bajo</span>
                                    </div>
                                    <div class="attribute">
                                        <span class="material-icons">wb_sunny</span>
                                        <span>Alto</span>
                                    </div>
                                    <div class="attribute">
                                        <span class="material-icons">equalizer</span>
                                        <span>Medio</span>
                                    </div>
                                </div>
                                <div class="plant-actions">
                                    <button class="btn btn-outline btn-sm">
                                        <span class="material-icons">bookmark_border</span>
                                        Guardar
                                    </button>
                                    <a href="biblioteca.html?plant=sotol" class="btn btn-primary btn-sm">Ver Detalles</a>
                                </div>
                            </div>
                        </div>

                        <!-- Planta 6 -->
                        <div class="plant-card">
                            <div class="match-badge">
                                78% Match
                            </div>
                            <div class="plant-image">
                                <img src="{{ url_for('static', filename='assets/lechuguilla.jpg') }}" alt="Lechuguilla">
                                <div class="plant-badges">
                                    <span class="badge native-badge">Nativa</span>
                                </div>
                            </div>
                            <div class="plant-info">
                                <h3 class="plant-name">Lechuguilla</h3>
                                <p class="plant-scientific-name">Agave lechuguilla</p>
                                <div class="plant-attributes">
                                    <div class="attribute">
                                        <span class="material-icons">opacity</span>
                                        <span>Muy Bajo</span>
                                    </div>
                                    <div class="attribute">
                                        <span class="material-icons">wb_sunny</span>
                                        <span>Alto</span>
                                    </div>
                                    <div class="attribute">
                                        <span class="material-icons">equalizer</span>
                                        <span>Medio</span>
                                    </div>
                                </div>
                                <div class="plant-actions">
                                    <button class="btn btn-outline btn-sm">
                                        <span class="material-icons">bookmark_border</span>
                                        Guardar
                                    </button>
                                    <a href="biblioteca.html?plant=lechuguilla" class="btn btn-primary btn-sm">Ver Detalles</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Pagination section removed -->
                </div>
            </div>
        </div>
    </main>

    <footer class="app-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="logo">
                        <span class="material-icons">local_florist</span>
                        <h2>PlantCare</h2>
                    </div>
                    <p>Tu asistente para el cuidado de plantas nativas de Chihuahua.</p>
                </div>

                <div class="footer-section">
                    <h3>Enlaces</h3>
                    <ul>
                        <li><a href="/">Inicio</a></li>
                        <li><a href="/biblioteca">Biblioteca</a></li>
                        <li><a href="/scanner">Scanner</a></li>
                        <li><a href="/calendario">Calendario</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Comunidad</h3>
                    <ul>
                        <li><a href="/foro">Foro</a></li>
                        <li><a href="blog.html">Blog</a></li>
                        <li><a href="contacto.html">Contacto</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Legal</h3>
                    <ul>
                        <li><a href="terminos.html">Términos de Uso</a></li>
                        <li><a href="privacidad.html">Política de Privacidad</a></li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2025 PlantCare. Todos los derechos reservados.</p>
            </div>
        </div>
    </footer>

    <script src="{{ url_for('static', filename='js/recomendaciones.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initUserMenu();
            initThemeToggle();

            // Datos de ejemplo para mostrar recomendaciones
            let allPlants = {
                success: true,
                plants: [
                    {
                        id: 1,
                        name: 'Agave',
                        scientificName: 'Agave americana',
                        description: 'Planta suculenta de hojas grandes y carnosas con bordes espinosos. Ideal para jardines de bajo mantenimiento.',
                        difficulty: 'Fácil',
                        waterRequirement: 'Bajo',
                        sunlightRequirement: 'Pleno sol',
                        maintenanceLevel: 'Bajo',
                        matchPercentage: 95,
                        imageUrl: '/static/assets/agave.jpg',
                        isSaved: false,
                        isNative: true,
                        isPurifying: false,
                        isEdible: true
                    },
                    {
                        id: 2,
                        name: 'Nopal',
                        scientificName: 'Opuntia ficus-indica',
                        description: 'Cactus de tallos planos y carnosos. Muy resistente a la sequía y fácil de cultivar.',
                        difficulty: 'Fácil',
                        waterRequirement: 'Bajo',
                        sunlightRequirement: 'Pleno sol',
                        maintenanceLevel: 'Bajo',
                        matchPercentage: 90,
                        imageUrl: '/static/assets/nopal.jpg',
                        isSaved: false,
                        isNative: true,
                        isPurifying: false,
                        isEdible: true
                    },
                    {
                        id: 3,
                        name: 'Yuca',
                        scientificName: 'Yucca elephantipes',
                        description: 'Planta de hojas largas y puntiagudas. Resistente y de bajo mantenimiento.',
                        difficulty: 'Moderado',
                        waterRequirement: 'Medio',
                        sunlightRequirement: 'Sol parcial',
                        maintenanceLevel: 'Medio',
                        matchPercentage: 75,
                        imageUrl: '/static/assets/yuca.jpg',
                        isSaved: false,
                        isNative: true,
                        isPurifying: true,
                        isEdible: false
                    },
                    {
                        id: 4,
                        name: 'Biznaga',
                        scientificName: 'Echinocactus grusonii',
                        description: 'Cactus globoso con costillas y espinas amarillas. Crece lentamente y requiere poco cuidado.',
                        difficulty: 'Fácil',
                        waterRequirement: 'Bajo',
                        sunlightRequirement: 'Pleno sol',
                        maintenanceLevel: 'Bajo',
                        matchPercentage: 85,
                        imageUrl: '/static/assets/agave.jpg',
                        isSaved: false,
                        isNative: true,
                        isPurifying: false,
                        isEdible: false
                    },
                    {
                        id: 5,
                        name: 'Sotol',
                        scientificName: 'Dasylirion wheeleri',
                        description: 'Planta resistente con hojas largas y delgadas. Ideal para jardines xerófilos.',
                        difficulty: 'Moderado',
                        waterRequirement: 'Bajo',
                        sunlightRequirement: 'Pleno sol',
                        maintenanceLevel: 'Medio',
                        matchPercentage: 80,
                        imageUrl: '/static/assets/nopal.jpg',
                        isSaved: false,
                        isNative: true,
                        isPurifying: false,
                        isEdible: false
                    },
                    {
                        id: 6,
                        name: 'Aloe Vera',
                        scientificName: 'Aloe barbadensis miller',
                        description: 'Planta suculenta con propiedades medicinales. Fácil de cuidar y muy útil.',
                        difficulty: 'Fácil',
                        waterRequirement: 'Bajo',
                        sunlightRequirement: 'Sol parcial',
                        maintenanceLevel: 'Bajo',
                        matchPercentage: 92,
                        imageUrl: '/static/assets/yuca.jpg',
                        isSaved: false,
                        isNative: false,
                        isPurifying: true,
                        isEdible: true
                    }
                ],
                userExperienceLevel: 'principiante'
            };

                    // Mostrar el nivel de experiencia del usuario
                    const experienceBadge = document.createElement('div');
                    experienceBadge.className = 'experience-badge';

                    let experienceText = 'Principiante';
                    let experienceClass = 'beginner';

                    if (allPlants.userExperienceLevel === 'intermedio') {
                        experienceText = 'Intermedio';
                        experienceClass = 'intermediate';
                    } else if (allPlants.userExperienceLevel === 'avanzado') {
                        experienceText = 'Avanzado';
                        experienceClass = 'advanced';
                    }

                    experienceBadge.classList.add(experienceClass);
                    experienceBadge.innerHTML = `
                        <span class="material-icons">psychology</span>
                        <span>Nivel: ${experienceText}</span>
                    `;

                    document.querySelector('.page-header').appendChild(experienceBadge);

                    // Inicializar la visualización de plantas
                    displayFilteredPlants(allPlants.plants);

                    // Función para mostrar plantas filtradas
                    function displayFilteredPlants(plants) {
                        const recommendationsContainer = document.querySelector('.plants-grid');
                        recommendationsContainer.innerHTML = '';

                        if (plants.length === 0) {
                            recommendationsContainer.innerHTML = '<p class="no-results">No hay recomendaciones disponibles con los filtros seleccionados.</p>';
                            return;
                        }

                        // Crear tarjetas para cada planta recomendada
                        plants.forEach(plant => {
                            const plantCard = document.createElement('div');
                            plantCard.className = 'plant-card';

                            // Añadir clase basada en el porcentaje de coincidencia
                            if (plant.matchPercentage >= 90) {
                                plantCard.classList.add('high-match');
                            } else if (plant.matchPercentage >= 70) {
                                plantCard.classList.add('medium-match');
                            } else {
                                plantCard.classList.add('low-match');
                            }

                            // Preparar badges
                            let badges = '';
                            if (plant.isNative) {
                                badges += '<span class="badge native-badge">Nativa</span>';
                            }
                            if (plant.isPurifying) {
                                badges += '<span class="badge purifying-badge">Purificadora</span>';
                            }
                            if (plant.isEdible) {
                                badges += '<span class="badge edible-badge">Comestible</span>';
                            }

                            plantCard.innerHTML = `
                                <div class="match-badge">${plant.matchPercentage}% coincidencia</div>
                                <div class="plant-image">
                                    <img src="${plant.imageUrl}" alt="${plant.name}">
                                    <div class="plant-badges">
                                        ${badges}
                                    </div>
                                </div>
                                <div class="plant-info">
                                    <h3 class="plant-name">${plant.name}</h3>
                                    <p class="plant-scientific-name">${plant.scientificName}</p>
                                    <div class="plant-attributes">
                                        <div class="attribute">
                                            <span class="material-icons">opacity</span>
                                            <span>${plant.waterRequirement}</span>
                                        </div>
                                        <div class="attribute">
                                            <span class="material-icons">wb_sunny</span>
                                            <span>${plant.sunlightRequirement}</span>
                                        </div>
                                        <div class="attribute">
                                            <span class="material-icons">equalizer</span>
                                            <span>${plant.difficulty}</span>
                                        </div>
                                    </div>
                                    <div class="plant-actions">
                                        <button class="btn btn-outline btn-sm save-plant" data-id="${plant.id}">
                                            <span class="material-icons">bookmark_border</span>
                                            Guardar
                                        </button>
                                        <a href="/planta/${plant.id}" class="btn btn-primary btn-sm">Ver Detalles</a>
                                    </div>
                                </div>
                            `;

                            recommendationsContainer.appendChild(plantCard);
                        });

                        // Añadir eventos a los botones de guardar
                        document.querySelectorAll('.save-plant').forEach(button => {
                            button.addEventListener('click', function() {
                                const plantId = this.getAttribute('data-id');
                                // Aquí iría la lógica para guardar la planta
                                this.innerHTML = '<span class="material-icons">bookmark</span> Guardada';
                                this.disabled = true;
                            });
                        });
                    }

                    // Función para aplicar filtros a las plantas
                    function applyFilters() {
                        const experienceLevel = document.getElementById('experience-level').value;
                        const sunlight = document.getElementById('sunlight').value;
                        const wateringFrequency = document.getElementById('watering-frequency').value;
                        const space = document.getElementById('space').value;
                        const purposeDecoration = document.getElementById('purpose-decoration').checked;
                        const purposePurification = document.getElementById('purpose-purification').checked;
                        const purposeGarden = document.getElementById('purpose-garden').checked;
                        const purposeNative = document.getElementById('purpose-native').checked;

                        // Filtrar plantas según los criterios seleccionados
                        let filteredPlants = allPlants.plants.filter(plant => {
                            // Filtrar por nivel de experiencia
                            if (experienceLevel === 'beginner' && plant.difficulty !== 'Fácil') {
                                return false;
                            }
                            if (experienceLevel === 'intermediate' && plant.difficulty === 'Difícil') {
                                return false;
                            }

                            // Filtrar por luz solar
                            if (sunlight === 'low' && plant.sunlightRequirement === 'Pleno sol') {
                                return false;
                            }
                            if (sunlight === 'high' && plant.sunlightRequirement === 'Sombra') {
                                return false;
                            }

                            // Filtrar por frecuencia de riego
                            if (wateringFrequency === 'low' && plant.waterRequirement === 'Alto') {
                                return false;
                            }
                            if (wateringFrequency === 'high' && plant.waterRequirement === 'Bajo') {
                                return false;
                            }

                            // Filtrar por propósito
                            if (purposePurification && !plant.isPurifying) {
                                return false;
                            }
                            if (purposeGarden && !plant.isEdible) {
                                return false;
                            }
                            if (purposeNative && !plant.isNative) {
                                return false;
                            }

                            return true;
                        });

                        // Ordenar plantas según el criterio seleccionado
                        const sortCriteria = document.getElementById('sort-recommendations').value;
                        if (sortCriteria === 'name-asc') {
                            filteredPlants.sort((a, b) => a.name.localeCompare(b.name));
                        } else if (sortCriteria === 'name-desc') {
                            filteredPlants.sort((a, b) => b.name.localeCompare(a.name));
                        } else if (sortCriteria === 'difficulty-asc') {
                            const difficultyOrder = { 'Fácil': 1, 'Moderado': 2, 'Difícil': 3 };
                            filteredPlants.sort((a, b) => difficultyOrder[a.difficulty] - difficultyOrder[b.difficulty]);
                        } else if (sortCriteria === 'difficulty-desc') {
                            const difficultyOrder = { 'Fácil': 1, 'Moderado': 2, 'Difícil': 3 };
                            filteredPlants.sort((a, b) => difficultyOrder[b.difficulty] - difficultyOrder[a.difficulty]);
                        } else {
                            // Por defecto, ordenar por porcentaje de coincidencia
                            filteredPlants.sort((a, b) => b.matchPercentage - a.matchPercentage);
                        }

                        // Mostrar plantas filtradas
                        displayFilteredPlants(filteredPlants);
                    }

                    // Manejar el envío del formulario de filtros
                    document.getElementById('recommendations-filter').addEventListener('submit', function(e) {
                        e.preventDefault();
                        applyFilters();
                    });

                    // Manejar cambios en el criterio de ordenación
                    document.getElementById('sort-recommendations').addEventListener('change', applyFilters);

                    // Manejar cambios en la vista (cuadrícula o lista)
                    document.getElementById('grid-view').addEventListener('click', function() {
                        document.querySelector('.plants-grid').classList.remove('list-view');
                        document.getElementById('list-view').classList.remove('active');
                        this.classList.add('active');
                    });

                    document.getElementById('list-view').addEventListener('click', function() {
                        document.querySelector('.plants-grid').classList.add('list-view');
                        document.getElementById('grid-view').classList.remove('active');
                        this.classList.add('active');
                    });
        });
    </script>
</body>
</html>

