<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registrarse - PlantCare</title>
    <script>
        // Redirigir al sistema de login/registro unificado
        window.location.href = '/login';
    </script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .register-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 450px;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .register-header {
            background: linear-gradient(135deg, #4CAF50, #2E7D32);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }

        .register-header .logo {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
        }

        .register-header .logo .material-icons {
            font-size: 32px;
            margin-right: 8px;
        }

        .register-header h1 {
            font-size: 24px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .register-header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .register-form {
            padding: 40px 30px;
        }

        .form-row {
            display: flex;
            gap: 16px;
            margin-bottom: 24px;
        }

        .form-group {
            margin-bottom: 24px;
            position: relative;
            flex: 1;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
            font-size: 14px;
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
            background: #fafafa;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4CAF50;
            background: white;
        }

        .form-group .material-icons {
            position: absolute;
            right: 12px;
            top: 38px;
            color: #999;
            font-size: 20px;
        }

        .register-btn {
            width: 100%;
            background: linear-gradient(135deg, #4CAF50, #2E7D32);
            color: white;
            border: none;
            padding: 14px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            margin-bottom: 20px;
        }

        .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3);
        }

        .register-btn:active {
            transform: translateY(0);
        }

        .register-btn:disabled {
            background: #cccccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .register-btn:disabled:hover {
            background: #cccccc;
            transform: none;
            box-shadow: none;
        }

        /* Estilos para validación de contraseña */
        .password-requirements {
            margin-top: 8px;
            padding: 12px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            font-size: 14px;
            display: none;
        }

        .password-requirements.show {
            display: block;
        }

        .requirement-title {
            margin: 0 0 8px 0;
            font-weight: 500;
            color: #856404;
        }

        .password-requirements ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .requirement {
            display: flex;
            align-items: center;
            margin: 4px 0;
            color: #dc3545;
            transition: color 0.3s ease;
        }

        .requirement.valid {
            color: #28a745;
        }

        .requirement .material-icons {
            font-size: 16px;
            margin-right: 8px;
        }

        .requirement.valid .material-icons:before {
            content: 'check';
        }

        .password-match-message {
            margin-top: 8px;
            padding: 8px 12px;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            color: #721c24;
            font-size: 14px;
            display: none;
            align-items: center;
        }

        .password-match-message.show {
            display: flex;
        }

        .password-match-message .material-icons {
            font-size: 16px;
            margin-right: 8px;
        }

        .divider {
            text-align: center;
            margin: 20px 0;
            position: relative;
            color: #999;
            font-size: 14px;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e0e0e0;
            z-index: 1;
        }

        .divider span {
            background: white;
            padding: 0 16px;
            position: relative;
            z-index: 2;
        }

        .login-link {
            text-align: center;
            margin-top: 20px;
        }

        .login-link a {
            color: #4CAF50;
            text-decoration: none;
            font-weight: 500;
        }

        .login-link a:hover {
            text-decoration: underline;
        }

        .back-home {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
            font-weight: 500;
            transition: opacity 0.3s;
        }

        .back-home:hover {
            opacity: 0.8;
        }

        .back-home .material-icons {
            margin-right: 8px;
        }

        .terms {
            font-size: 12px;
            color: #666;
            text-align: center;
            margin-top: 16px;
            line-height: 1.4;
        }

        .terms a {
            color: #4CAF50;
            text-decoration: none;
        }

        .terms a:hover {
            text-decoration: underline;
        }

        @media (max-width: 480px) {
            .register-container {
                margin: 10px;
            }

            .register-header {
                padding: 30px 20px;
            }

            .register-form {
                padding: 30px 20px;
            }

            .form-row {
                flex-direction: column;
                gap: 0;
            }
        }
    </style>
</head>
<body>
    <a href="/" class="back-home">
        <span class="material-icons">arrow_back</span>
        Volver al inicio
    </a>

    <div class="register-container">
        <div class="register-header">
            <div class="logo">
                <span class="material-icons">local_florist</span>
                <h1>PlantCare</h1>
            </div>
            <p>Únete a la comunidad de cuidadores de plantas</p>
        </div>

        <div class="register-form">
            <form method="POST" action="/register">
                <div class="form-row">
                    <div class="form-group">
                        <label for="first_name">Nombre</label>
                        <input type="text" id="first_name" name="first_name" required>
                        <span class="material-icons">person</span>
                    </div>
                    <div class="form-group">
                        <label for="last_name">Apellido</label>
                        <input type="text" id="last_name" name="last_name" required>
                        <span class="material-icons">person_outline</span>
                    </div>
                </div>

                <div class="form-group">
                    <label for="email">Correo electrónico</label>
                    <input type="email" id="email" name="email" required>
                    <span class="material-icons">email</span>
                </div>

                <div class="form-group">
                    <label for="password">Contraseña</label>
                    <input type="password" id="password" name="password" required minlength="8">
                    <span class="material-icons">lock</span>
                    <div class="password-requirements" id="password-requirements">
                        <p class="requirement-title">La contraseña debe tener:</p>
                        <ul>
                            <li id="length-req" class="requirement">
                                <span class="material-icons">close</span>
                                Mínimo 8 caracteres
                            </li>
                            <li id="number-req" class="requirement">
                                <span class="material-icons">close</span>
                                Al menos un número
                            </li>
                            <li id="letter-req" class="requirement">
                                <span class="material-icons">close</span>
                                Al menos una letra
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="form-group">
                    <label for="confirm_password">Confirmar contraseña</label>
                    <input type="password" id="confirm_password" name="confirm_password" required minlength="8">
                    <span class="material-icons">lock_outline</span>
                    <div class="password-match-message" id="password-match-message">
                        <span class="material-icons">close</span>
                        Las contraseñas no coinciden
                    </div>
                </div>

                <button type="submit" class="register-btn">
                    Crear cuenta
                </button>

                <div class="terms">
                    Al registrarte, aceptas nuestros
                    <a href="#">Términos de Servicio</a> y
                    <a href="#">Política de Privacidad</a>
                </div>
            </form>

            <div class="divider">
                <span>o</span>
            </div>

            <div class="login-link">
                <p>¿Ya tienes cuenta? <a href="/login">Inicia sesión aquí</a></p>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const passwordInput = document.getElementById('password');
            const confirmPasswordInput = document.getElementById('confirm_password');
            const submitButton = document.querySelector('.register-btn');
            const form = document.querySelector('form');

            const passwordRequirements = document.getElementById('password-requirements');
            const passwordMatchMessage = document.getElementById('password-match-message');

            const lengthReq = document.getElementById('length-req');
            const numberReq = document.getElementById('number-req');
            const letterReq = document.getElementById('letter-req');

            let passwordValid = false;
            let passwordsMatch = false;

            // Función para validar contraseña
            function validatePassword() {
                const password = passwordInput.value;

                // Verificar requisitos
                const hasMinLength = password.length >= 8;
                const hasNumbers = /[0-9]/.test(password);
                const hasLetters = /[a-zA-Z]/.test(password);

                // Actualizar indicadores visuales
                updateRequirement(lengthReq, hasMinLength);
                updateRequirement(numberReq, hasNumbers);
                updateRequirement(letterReq, hasLetters);

                // Mostrar/ocultar panel de requisitos
                if (password.length > 0) {
                    passwordRequirements.classList.add('show');
                } else {
                    passwordRequirements.classList.remove('show');
                }

                // Determinar si la contraseña es válida
                passwordValid = hasMinLength && hasNumbers && hasLetters;

                // Validar coincidencia si hay confirmación
                if (confirmPasswordInput.value.length > 0) {
                    validatePasswordMatch();
                }

                updateSubmitButton();
                return passwordValid;
            }

            // Función para actualizar indicador de requisito
            function updateRequirement(element, isValid) {
                if (isValid) {
                    element.classList.add('valid');
                    element.querySelector('.material-icons').textContent = 'check';
                } else {
                    element.classList.remove('valid');
                    element.querySelector('.material-icons').textContent = 'close';
                }
            }

            // Función para validar coincidencia de contraseñas
            function validatePasswordMatch() {
                const password = passwordInput.value;
                const confirmPassword = confirmPasswordInput.value;

                passwordsMatch = password === confirmPassword && password.length > 0;

                if (confirmPassword.length > 0 && !passwordsMatch) {
                    passwordMatchMessage.classList.add('show');
                } else {
                    passwordMatchMessage.classList.remove('show');
                }

                updateSubmitButton();
                return passwordsMatch;
            }

            // Función para actualizar estado del botón
            function updateSubmitButton() {
                const canSubmit = passwordValid && passwordsMatch;
                submitButton.disabled = !canSubmit;

                if (canSubmit) {
                    submitButton.textContent = 'Crear cuenta';
                } else {
                    if (!passwordValid) {
                        submitButton.textContent = 'Contraseña debe cumplir requisitos';
                    } else if (!passwordsMatch) {
                        submitButton.textContent = 'Las contraseñas no coinciden';
                    }
                }
            }

            // Event listeners
            passwordInput.addEventListener('input', validatePassword);
            confirmPasswordInput.addEventListener('input', validatePasswordMatch);

            // Prevenir envío del formulario si no es válido
            form.addEventListener('submit', function(e) {
                if (!passwordValid || !passwordsMatch) {
                    e.preventDefault();

                    // Mostrar mensaje de error
                    if (!passwordValid) {
                        alert('La contraseña debe tener al menos 8 caracteres, incluyendo números y letras.');
                        passwordInput.focus();
                    } else if (!passwordsMatch) {
                        alert('Las contraseñas no coinciden.');
                        confirmPasswordInput.focus();
                    }

                    return false;
                }
            });

            // Inicializar estado del botón
            updateSubmitButton();
        });
    </script>
</body>
</html>