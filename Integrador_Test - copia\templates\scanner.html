<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>Scanner IA de Plantas - PlantCare</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/scanner.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/user-menu.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/navbar.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/home-navbar.css') }}">
    <style>
        /* Footer responsive */
        @media (max-width: 1024px) {
            footer .container > div:first-child {
                grid-template-columns: 1fr 1fr !important;
                gap: 32px !important;
            }
        }

        @media (max-width: 768px) {
            footer .container > div:first-child {
                grid-template-columns: 1fr !important;
                gap: 24px !important;
                text-align: center !important;
            }

            footer .container > div:last-child {
                flex-direction: column !important;
                text-align: center !important;
            }
        }
    </style>
</head>
<body>
    <!-- Navegación atractiva -->
    <nav class="home-navbar">
        <div class="home-navbar-container">
            <!-- Logo atractivo -->
            <a href="/" class="home-logo">
                <span class="material-icons home-logo-icon">local_florist</span>
                <h1 class="home-logo-text">PlantCare</h1>
            </a>

            <!-- Navegación central con iconos -->
            <ul class="home-nav-links">
                <li class="home-nav-item">
                    <a href="/" class="home-nav-link">
                        <span class="material-icons home-nav-icon">home</span>
                        Inicio
                    </a>
                </li>
                <li class="home-nav-item">
                    <a href="/biblioteca" class="home-nav-link">
                        <span class="material-icons home-nav-icon">library_books</span>
                        Biblioteca
                    </a>
                </li>
                <li class="home-nav-item">
                    <a href="/scanner" class="home-nav-link active">
                        <span class="material-icons home-nav-icon">photo_camera</span>
                        Scanner
                    </a>
                </li>
                <li class="home-nav-item">
                    <a href="/calendario" class="home-nav-link">
                        <span class="material-icons home-nav-icon">event</span>
                        Calendario
                    </a>
                </li>
                <li class="home-nav-item">
                    <a href="/clubes" class="home-nav-link">
                        <span class="material-icons home-nav-icon">groups</span>
                        Clubes
                    </a>
                </li>
            </ul>

            <!-- Botones de acción atractivos -->
            <div class="home-auth-buttons">
                {% if current_user.is_authenticated %}
                    <div class="user-menu">
                        <button id="user-menu-button" class="avatar">
                            <img src="{{ url_for('static', filename='assets/pfp.jpg') }}" alt="Usuario">
                        </button>
                        <div id="user-dropdown" class="dropdown-menu">
                            <a href="/perfil">
                                <span class="material-icons">person</span>
                                Mi Perfil
                            </a>
                            <a href="/ajustes">
                                <span class="material-icons">settings</span>
                                Ajustes
                            </a>
                            <a href="/login" id="logout-button">
                                <span class="material-icons">logout</span>
                                Cerrar Sesión
                            </a>
                        </div>
                    </div>
                {% else %}
                    <a href="/login" class="home-btn home-btn-outline">
                        <span class="material-icons home-btn-icon">login</span>
                        Iniciar Sesión
                    </a>
                    <a href="/register" class="home-btn home-btn-primary">
                        <span class="material-icons home-btn-icon">person_add</span>
                        Registrarse
                    </a>
                {% endif %}
            </div>
        </div>
    </nav>

    <main>
        <div class="container">
            <!-- Encabezado de página -->
            <section class="page-header">
                <h1>Scanner IA de Plantas</h1>
                <p class="subtitle">Identifica plantas y obtén consejos personalizados de cuidado con nuestra inteligencia artificial</p>
            </section>

            <!-- Contenedor principal del scanner -->
            <div class="scanner-container">
                <!-- Estado inicial -->
                <div id="initial-state" class="scanner-state active">
                    <div class="scanner-card">
                        <div class="scanner-icon">
                            <span class="material-icons">photo_camera</span>
                        </div>
                        <h2>Analiza tu planta con IA</h2>
                        <p>Toma una foto clara de la planta que deseas identificar. Nuestra IA analizará la imagen y te proporcionará información detallada sobre cuidados específicos para tu planta.</p>
                        <div class="scanner-actions">
                            <button id="take-photo-btn" class="btn btn-primary">
                                <span class="material-icons">add_a_photo</span>
                                Tomar Foto
                            </button>
                            <button id="upload-photo-btn" class="btn btn-outline">
                                <span class="material-icons">upload</span>
                                Subir Foto
                            </button>
                            <input type="file" id="photo-upload" accept="image/*" style="display: none;">
                        </div>
                    </div>

                    <div class="scanner-tips">
                        <h3>Consejos para mejores resultados con IA</h3>
                        <ul>
                            <li>
                                <span class="material-icons">wb_sunny</span>
                                <div>
                                    <h4>Buena iluminación</h4>
                                    <p>Asegúrate de que la planta esté bien iluminada, preferiblemente con luz natural.</p>
                                </div>
                            </li>
                            <li>
                                <span class="material-icons">center_focus_strong</span>
                                <div>
                                    <h4>Enfoque claro</h4>
                                    <p>Mantén la cámara estable y asegúrate de que la planta esté enfocada.</p>
                                </div>
                            </li>
                            <li>
                                <span class="material-icons">crop_free</span>
                                <div>
                                    <h4>Encuadre adecuado</h4>
                                    <p>La planta debe ocupar la mayor parte de la imagen, pero sin cortar partes importantes.</p>
                                </div>
                            </li>
                            <li>
                                <span class="material-icons">view_in_ar</span>
                                <div>
                                    <h4>Múltiples ángulos</h4>
                                    <p>Para mayor precisión, intenta capturar tanto las hojas como las flores si están disponibles.</p>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Estado de cámara -->
                <div id="camera-state" class="scanner-state">
                    <div class="camera-container">
                        <video id="camera-feed" autoplay playsinline></video>
                        <canvas id="photo-canvas" style="display: none;"></canvas>
                        <div class="camera-overlay">
                            <div class="camera-frame"></div>
                        </div>
                        <div class="camera-controls">
                            <button id="cancel-camera-btn" class="btn btn-outline">
                                <span class="material-icons">close</span>
                                Cancelar
                            </button>
                            <button id="capture-btn" class="btn btn-primary">
                                <span class="material-icons">photo_camera</span>
                            </button>
                            <button id="switch-camera-btn" class="btn btn-outline">
                                <span class="material-icons">flip_camera_android</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Estado de previsualización -->
                <div id="preview-state" class="scanner-state">
                    <div class="preview-container">
                        <div class="preview-header">
                            <h2>Previsualización</h2>
                            <p>¿Estás satisfecho con esta imagen?</p>
                        </div>
                        <div class="preview-image-container">
                            <img id="preview-image" src="" alt="Previsualización de la foto">
                        </div>
                        <div class="preview-actions">
                            <button id="retake-btn" class="btn btn-outline">
                                <span class="material-icons">replay</span>
                                Volver a tomar
                            </button>
                            <button id="analyze-btn" class="btn btn-primary">
                                <span class="material-icons">search</span>
                                Identificar planta
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Estado de carga -->
                <div id="loading-state" class="scanner-state">
                    <div class="loading-container">
                        <div class="spinner"></div>
                        <h2>Analizando con IA...</h2>
                        <p>Nuestra inteligencia artificial está procesando la imagen para identificar la planta y determinar sus necesidades específicas de cuidado.</p>
                        <div class="ai-processing-indicators">
                            <div class="processing-step active">Identificando especie</div>
                            <div class="processing-step">Analizando características</div>
                            <div class="processing-step">Generando recomendaciones</div>
                        </div>
                    </div>
                </div>

                <!-- Estado de resultado -->
                <div id="result-state" class="scanner-state">
                    <div class="result-container">
                        <div class="result-header">
                            <h2>Resultado del análisis IA</h2>
                            <p>Hemos identificado tu planta y sus necesidades específicas</p>
                        </div>

                        <div class="result-card">
                            <div class="result-image">
                                <img id="result-plant-image" src="" alt="Planta identificada">
                                <div class="ai-badge">Identificado con IA</div>
                            </div>
                            <div class="result-info">
                                <div class="result-confidence">
                                    <div class="confidence-bar">
                                        <div id="confidence-level" class="confidence-level" style="width: 85%;"></div>
                                    </div>
                                    <span id="confidence-percentage">85% de coincidencia</span>
                                </div>

                                <h3 id="result-plant-name">Agave</h3>
                                <p id="result-plant-scientific" class="scientific-name">Agave americana</p>

                                <div class="care-instructions">
                                    <h4>Guía de cuidados</h4>
                                    <div class="plant-attributes">
                                        <div class="attribute">
                                            <span class="material-icons">opacity</span>
                                            <div>
                                                <strong>Riego</strong>
                                                <span id="result-water-needs">Riego escaso, una vez cada 2-3 semanas</span>
                                            </div>
                                        </div>
                                        <div class="attribute">
                                            <span class="material-icons">wb_sunny</span>
                                            <div>
                                                <strong>Luz</strong>
                                                <span id="result-sun-needs">Pleno sol, mínimo 6 horas diarias</span>
                                            </div>
                                        </div>
                                        <div class="attribute">
                                            <span class="material-icons">thermostat</span>
                                            <div>
                                                <strong>Temperatura</strong>
                                                <span id="result-temp-needs">10°C - 35°C</span>
                                            </div>
                                        </div>
                                        <div class="attribute">
                                            <span class="material-icons">compost</span>
                                            <div>
                                                <strong>Suelo</strong>
                                                <span id="result-soil-needs">Bien drenado, arenoso</span>
                                            </div>
                                        </div>
                                        <div class="attribute">
                                            <span class="material-icons">eco</span>
                                            <div>
                                                <strong>Origen</strong>
                                                <span id="result-native">Nativa de Chihuahua</span>
                                            </div>
                                        </div>
                                    </div>

                                    <h4>Recomendaciones personalizadas</h4>
                                    <p id="result-description" class="plant-description">
                                        El Agave americana es una planta suculenta grande que forma una roseta de hojas gruesas y carnosas, de color verde azulado o verde grisáceo. Es resistente a la sequía y requiere muy poco mantenimiento.
                                    </p>

                                    <div class="care-tips">
                                        <div class="care-tip" id="watering-tip">
                                            <span class="material-icons">tips_and_updates</span>
                                            <p>En verano, aumenta ligeramente la frecuencia de riego pero evita el encharcamiento.</p>
                                        </div>
                                        <div class="care-tip" id="seasonal-tip">
                                            <span class="material-icons">event</span>
                                            <p>Protege del frío extremo durante el invierno, especialmente si las temperaturas bajan de 0°C.</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="result-actions">
                                    <button id="new-scan-btn" class="btn btn-outline">
                                        <span class="material-icons">refresh</span>
                                        Nuevo análisis
                                    </button>
                                    <a id="view-details-btn" href="/biblioteca" class="btn btn-primary">
                                        <span class="material-icons">info</span>
                                        Ver guía completa
                                    </a>
                                    <button id="add-to-my-plants" class="btn btn-accent">
                                        <span class="material-icons">bookmark</span>
                                        Añadir a mis plantas
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="similar-plants">
                            <h3>Plantas similares identificadas</h3>
                            <p>Nuestra IA ha encontrado estas alternativas que podrían coincidir con tu planta</p>
                            <div class="similar-plants-grid" id="similar-plants-container">
                                <!-- Similar plants will be added dynamically -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Estado de error -->
                <div id="error-state" class="scanner-state">
                    <div class="error-container">
                        <span class="material-icons error-icon">error_outline</span>
                        <h2>La IA no pudo identificar la planta</h2>
                        <p id="error-message">La imagen no es clara o no contiene una planta reconocible por nuestro sistema. Intenta tomar otra foto con mejor iluminación y enfoque.</p>
                        <div class="error-tips">
                            <h4>Sugerencias:</h4>
                            <ul>
                                <li>Asegúrate que la planta sea el objeto principal en la foto</li>
                                <li>Evita sombras fuertes o reflejos</li>
                                <li>Intenta capturar características distintivas como hojas o flores</li>
                            </ul>
                        </div>
                        <button id="try-again-btn" class="btn btn-primary">
                            <span class="material-icons">refresh</span>
                            Intentar de nuevo
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="app-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="logo">
                        <span class="material-icons">local_florist</span>
                        <h2>PlantCare</h2>
                    </div>
                    <p>Tu asistente para el cuidado de plantas nativas de Chihuahua.</p>
                </div>

                <div class="footer-section">
                    <h3>Enlaces</h3>
                    <ul>
                        <li><a href="/">Inicio</a></li>
                        <li><a href="/biblioteca">Biblioteca</a></li>
                        <li><a href="/diagnosis/scanner">Scanner</a></li>
                        <li><a href="/calendario">Calendario</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Comunidad</h3>
                    <ul>
                        <li><a href="/foro">Foro</a></li>
                        <li><a href="/">Blog</a></li>
                        <li><a href="/">Contacto</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Legal</h3>
                    <ul>
                        <li><a href="/register">Términos de Uso</a></li>
                        <li><a href="/ajustes">Política de Privacidad</a></li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2025 PlantCare. Todos los derechos reservados.</p>
            </div>
        </div>
    </footer>

    <!-- Template for similar plant items -->
    <template id="similar-plant-template">
        <div class="similar-plant-item">
            <img src="" alt="" class="similar-plant-image">
            <div class="similar-plant-info">
                <h4 class="similar-plant-name"></h4>
                <p class="similar-plant-scientific"></p>
                <span class="similar-plant-match"></span>
                <a href="#" class="similar-view-btn">Ver detalles</a>
            </div>
        </div>
    </template>

    <!-- Footer Premium -->
    <footer style="background: linear-gradient(135deg, #1f2937 0%, #111827 50%, #0f172a 100%); color: white; margin-top: 80px; position: relative; overflow: hidden;">
        <!-- Patrón de fondo -->
        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: url('data:image/svg+xml,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 100 100&quot;><defs><pattern id=&quot;plant-pattern&quot; width=&quot;50&quot; height=&quot;50&quot; patternUnits=&quot;userSpaceOnUse&quot;><circle cx=&quot;25&quot; cy=&quot;25&quot; r=&quot;1&quot; fill=&quot;rgba(5,150,105,0.1)&quot;/><circle cx=&quot;10&quot; cy=&quot;10&quot; r=&quot;0.5&quot; fill=&quot;rgba(16,185,129,0.05)&quot;/><circle cx=&quot;40&quot; cy=&quot;15&quot; r=&quot;0.5&quot; fill=&quot;rgba(52,211,153,0.05)&quot;/></pattern></defs><rect width=&quot;100&quot; height=&quot;100&quot; fill=&quot;url(%23plant-pattern)&quot;/></svg>'); opacity: 0.3;"></div>

        <div class="container" style="position: relative; z-index: 1;">
            <!-- Contenido principal del footer -->
            <div style="display: grid; grid-template-columns: 2fr 1fr 1fr 1fr; gap: 48px; padding: 64px 0 48px 0;">

                <!-- Sección del logo y descripción -->
                <div style="max-width: 400px;">
                    <div style="display: flex; align-items: center; gap: 16px; margin-bottom: 24px;">
                        <div style="background: linear-gradient(135deg, #059669, #10b981); padding: 16px; border-radius: 20px; box-shadow: 0 8px 32px rgba(5, 150, 105, 0.3);">
                            <span class="material-icons" style="font-size: 32px; color: white;">local_florist</span>
                        </div>
                        <h2 style="margin: 0; font-size: 32px; font-weight: 800; background: linear-gradient(135deg, #10b981, #34d399); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">PlantCare</h2>
                    </div>
                    <p style="margin: 0 0 24px 0; font-size: 16px; line-height: 1.6; color: #d1d5db;">Tu asistente inteligente para el cuidado de plantas nativas de Chihuahua. Descubre, identifica y aprende con tecnología de vanguardia.</p>

                    <!-- Estadísticas del footer -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 24px;">
                        <div style="background: rgba(5, 150, 105, 0.1); padding: 16px; border-radius: 12px; text-align: center; border: 1px solid rgba(5, 150, 105, 0.2);">
                            <div style="font-size: 24px; font-weight: 700; color: #10b981; margin-bottom: 4px;">5.2k+</div>
                            <div style="font-size: 12px; color: #9ca3af; text-transform: uppercase; letter-spacing: 0.5px;">Usuarios Activos</div>
                        </div>
                        <div style="background: rgba(5, 150, 105, 0.1); padding: 16px; border-radius: 12px; text-align: center; border: 1px solid rgba(5, 150, 105, 0.2);">
                            <div style="font-size: 24px; font-weight: 700; color: #10b981; margin-bottom: 4px;">150+</div>
                            <div style="font-size: 12px; color: #9ca3af; text-transform: uppercase; letter-spacing: 0.5px;">Especies Nativas</div>
                        </div>
                    </div>

                    <!-- Redes sociales -->
                    <div style="display: flex; gap: 12px;">
                        <a href="#" style="background: rgba(255, 255, 255, 0.1); padding: 12px; border-radius: 12px; color: #d1d5db; text-decoration: none; transition: all 0.3s ease; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.1);" onmouseover="this.style.background='rgba(5, 150, 105, 0.2)'; this.style.color='#10b981'; this.style.transform='translateY(-2px)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'; this.style.color='#d1d5db'; this.style.transform='translateY(0)'">
                            <span class="material-icons" style="font-size: 20px;">facebook</span>
                        </a>
                        <a href="#" style="background: rgba(255, 255, 255, 0.1); padding: 12px; border-radius: 12px; color: #d1d5db; text-decoration: none; transition: all 0.3s ease; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.1);" onmouseover="this.style.background='rgba(5, 150, 105, 0.2)'; this.style.color='#10b981'; this.style.transform='translateY(-2px)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'; this.style.color='#d1d5db'; this.style.transform='translateY(0)'">
                            <span class="material-icons" style="font-size: 20px;">alternate_email</span>
                        </a>
                        <a href="#" style="background: rgba(255, 255, 255, 0.1); padding: 12px; border-radius: 12px; color: #d1d5db; text-decoration: none; transition: all 0.3s ease; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.1);" onmouseover="this.style.background='rgba(5, 150, 105, 0.2)'; this.style.color='#10b981'; this.style.transform='translateY(-2px)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'; this.style.color='#d1d5db'; this.style.transform='translateY(0)'">
                            <span class="material-icons" style="font-size: 20px;">share</span>
                        </a>
                    </div>
                </div>

                <!-- Enlaces principales -->
                <div>
                    <h3 style="margin: 0 0 24px 0; font-size: 18px; font-weight: 700; color: white; display: flex; align-items: center; gap: 8px;">
                        <span class="material-icons" style="color: #10b981;">navigation</span>
                        Enlaces
                    </h3>
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li style="margin-bottom: 12px;">
                            <a href="/" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">home</span>
                                Inicio
                            </a>
                        </li>
                        <li style="margin-bottom: 12px;">
                            <a href="/biblioteca" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">library_books</span>
                                Biblioteca
                            </a>
                        </li>
                        <li style="margin-bottom: 12px;">
                            <a href="/scanner" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">photo_camera</span>
                                Scanner
                            </a>
                        </li>
                        <li style="margin-bottom: 12px;">
                            <a href="/calendario" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">event</span>
                                Calendario
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Comunidad -->
                <div>
                    <h3 style="margin: 0 0 24px 0; font-size: 18px; font-weight: 700; color: white; display: flex; align-items: center; gap: 8px;">
                        <span class="material-icons" style="color: #10b981;">groups</span>
                        Comunidad
                    </h3>
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li style="margin-bottom: 12px;">
                            <a href="/clubes" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">diversity_3</span>
                                Clubes
                            </a>
                        </li>
                        <li style="margin-bottom: 12px;">
                            <a href="#" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">article</span>
                                Blog
                            </a>
                        </li>
                        <li style="margin-bottom: 12px;">
                            <a href="#" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">contact_support</span>
                                Contacto
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Legal -->
                <div>
                    <h3 style="margin: 0 0 24px 0; font-size: 18px; font-weight: 700; color: white; display: flex; align-items: center; gap: 8px;">
                        <span class="material-icons" style="color: #10b981;">gavel</span>
                        Legal
                    </h3>
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li style="margin-bottom: 12px;">
                            <a href="/register" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">description</span>
                                Términos de Uso
                            </a>
                        </li>
                        <li style="margin-bottom: 12px;">
                            <a href="/ajustes" style="color: #d1d5db; text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 8px 0; transition: all 0.3s ease;" onmouseover="this.style.color='#10b981'; this.style.paddingLeft='8px'" onmouseout="this.style.color='#d1d5db'; this.style.paddingLeft='0'">
                                <span class="material-icons" style="font-size: 16px;">privacy_tip</span>
                                Política de Privacidad
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Línea divisoria -->
            <div style="height: 1px; background: linear-gradient(90deg, transparent 0%, rgba(5, 150, 105, 0.3) 50%, transparent 100%); margin: 32px 0;"></div>

            <!-- Footer bottom -->
            <div style="display: flex; justify-content: space-between; align-items: center; padding-bottom: 32px; flex-wrap: wrap; gap: 16px;">
                <p style="margin: 0; color: #9ca3af; font-size: 14px;">
                    © 2025 <span style="color: #10b981; font-weight: 600;">PlantCare</span>. Todos los derechos reservados.
                </p>
                <div style="display: flex; align-items: center; gap: 16px; color: #9ca3af; font-size: 14px;">
                    <span style="display: flex; align-items: center; gap: 4px;">
                        <span class="material-icons" style="font-size: 16px; color: #10b981;">eco</span>
                        Hecho con amor para las plantas
                    </span>
                    <span style="display: flex; align-items: center; gap: 4px;">
                        <span class="material-icons" style="font-size: 16px; color: #10b981;">location_on</span>
                        Chihuahua, México
                    </span>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="{{ url_for('static', filename='js/scanner.js') }}"></script>
    <script src="{{ url_for('static', filename='js/home-navbar.js') }}"></script>
</body>
</html>



