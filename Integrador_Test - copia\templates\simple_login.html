<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Simple - PlantCare</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #4CAF50, #2E7D32);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            color: white;
        }
        
        .login-container {
            background: white;
            color: #333;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            width: 100%;
            max-width: 450px;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-header h1 {
            color: #4CAF50;
            margin-bottom: 10px;
        }
        
        .credentials-info {
            background: #e8f5e9;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border-left: 4px solid #4CAF50;
        }
        
        .credentials-info h3 {
            margin-top: 0;
            color: #2E7D32;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        
        .password-container {
            position: relative;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #4CAF50;
        }
        
        .password-toggle {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #999;
            font-size: 18px;
            user-select: none;
        }
        
        .password-toggle:hover {
            color: #4CAF50;
        }
        
        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #4CAF50, #2E7D32);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .debug-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .links {
            text-align: center;
            margin-top: 20px;
        }
        
        .links a {
            color: #4CAF50;
            text-decoration: none;
            margin: 0 10px;
        }
        
        .links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>🔧 Login Simple (Sin CSRF)</h1>
            <p>Página de prueba para login de administrador</p>
        </div>
        
        <div class="credentials-info">
            <h3>🔑 Credenciales Correctas</h3>
            <p><strong>Usuario:</strong> admin</p>
            <p><strong>Contraseña:</strong> PlantCare2025!</p>
            <p><small>Estas credenciales están pre-cargadas en el formulario</small></p>
        </div>
        
        <div class="debug-info">
            <strong>🔍 Información de Depuración:</strong><br>
            • Esta página NO requiere token CSRF<br>
            • Hash esperado: fb9f21c1985d85c574df59663a034de741666e2da48db7425610691ca03bbf7a<br>
            • Método de hash: SHA256<br>
            • Archivo de usuarios: data/users.json
        </div>
        
        <!-- Mensajes Flash -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'success' if category == 'success' else 'error' }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <form method="POST" action="/login-simple-post">
            <div class="form-group">
                <label for="username">Usuario:</label>
                <input type="text" id="username" name="username" value="admin" required>
            </div>
            
            <div class="form-group">
                <label for="password">Contraseña:</label>
                <div class="password-container">
                    <input type="password" id="password" name="password" value="PlantCare2025!" required>
                    <span class="password-toggle" onclick="togglePassword()">👁️</span>
                </div>
            </div>
            
            <button type="submit" class="login-btn">
                🚀 Login Sin CSRF
            </button>
        </form>
        
        <div class="links">
            <a href="/">🏠 Volver al Inicio</a>
            <a href="/login">🔑 Login Principal</a>
            <a href="/admin">🛠️ Panel Admin (directo)</a>
        </div>
    </div>
    
    <script>
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.querySelector('.password-toggle');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.textContent = '🙈';
                toggleIcon.style.color = '#4CAF50';
            } else {
                passwordInput.type = 'password';
                toggleIcon.textContent = '👁️';
                toggleIcon.style.color = '#999';
            }
        }
        
        // Auto-focus en el campo de usuario
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username').focus();
            
            // Mostrar información en consola
            console.log('🔍 Login Simple (Sin CSRF):');
            console.log('Usuario: admin');
            console.log('Contraseña: PlantCare2025!');
            console.log('Esta página NO requiere token CSRF');
        });
    </script>
</body>
</html>
