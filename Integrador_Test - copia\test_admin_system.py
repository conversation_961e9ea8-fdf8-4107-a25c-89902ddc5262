#!/usr/bin/env python3
"""
Script para probar el sistema de administración
"""

import requests
import json

def test_admin_system():
    """Probar el sistema de administración"""
    
    base_url = "http://127.0.0.1:5000"
    
    print("🧪 Probando sistema de administración...")
    
    # 1. Probar acceso al panel de administración (sin login)
    print("\n1. 🔒 Probando acceso sin autenticación...")
    try:
        response = requests.get(f"{base_url}/admin", timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 302:
            print(f"   ✅ Redirección correcta (requiere login)")
        elif response.status_code == 200:
            print(f"   ⚠️  Acceso permitido sin login (revisar seguridad)")
        else:
            print(f"   ❌ Error inesperado")
    except Exception as e:
        print(f"   💥 Error: {e}")
    
    # 2. Probar página de login
    print("\n2. 🔑 Probando página de login...")
    try:
        response = requests.get(f"{base_url}/login", timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print(f"   ✅ Página de login accesible")
        else:
            print(f"   ❌ Error: {response.status_code}")
    except Exception as e:
        print(f"   💥 Error: {e}")
    
    # 3. Verificar archivo de usuarios
    print("\n3. 👥 Verificando archivo de usuarios...")
    try:
        with open('data/json/users.json', 'r', encoding='utf-8') as f:
            users = json.load(f)
        
        admin_users = [u for u in users if u.get('IsAdmin', False)]
        print(f"   ✅ Archivo de usuarios cargado")
        print(f"   📊 Total usuarios: {len(users)}")
        print(f"   👑 Administradores: {len(admin_users)}")
        
        if admin_users:
            admin = admin_users[0]
            print(f"   🔑 Usuario admin: {admin.get('Username')}")
            print(f"   📧 Email admin: {admin.get('Email')}")
        
    except Exception as e:
        print(f"   💥 Error: {e}")
    
    # 4. Verificar archivo de plantas
    print("\n4. 🌱 Verificando archivo de plantas...")
    try:
        with open('data/json/plants.json', 'r', encoding='utf-8') as f:
            plants = json.load(f)
        
        active_plants = [p for p in plants if p.get('Activo', True)]
        print(f"   ✅ Archivo de plantas cargado")
        print(f"   📊 Total plantas: {len(plants)}")
        print(f"   🌿 Plantas activas: {len(active_plants)}")
        
        if plants:
            plant = plants[0]
            print(f"   🌱 Ejemplo: {plant.get('Nombre')} ({plant.get('NombreCientifico')})")
        
    except Exception as e:
        print(f"   💥 Error: {e}")
    
    # 5. Probar rutas de administración (sin autenticación)
    admin_routes = [
        '/admin/plants',
        '/admin/users',
        '/admin/plants/add'
    ]
    
    print("\n5. 🛣️  Probando rutas de administración...")
    for route in admin_routes:
        try:
            response = requests.get(f"{base_url}{route}", timeout=10)
            print(f"   {route}: Status {response.status_code}")
            if response.status_code == 302:
                print(f"      ✅ Protegida correctamente")
            elif response.status_code == 200:
                print(f"      ⚠️  Accesible sin login")
            else:
                print(f"      ❌ Error inesperado")
        except Exception as e:
            print(f"      💥 Error: {e}")
    
    print("\n🎉 Pruebas del sistema de administración completadas!")
    print("\n📋 Credenciales de administrador:")
    print("   Usuario: admin")
    print("   Contraseña: PlantCare2025!")
    print("   Email: <EMAIL>")
    print("\n🔗 URLs importantes:")
    print(f"   Panel de admin: {base_url}/admin")
    print(f"   Login: {base_url}/login")
    print(f"   Agregar planta: {base_url}/admin/plants/add")

if __name__ == "__main__":
    test_admin_system()
