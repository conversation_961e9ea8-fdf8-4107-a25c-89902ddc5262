"""
Script para probar el servicio de IA actualizado
"""

import os
from ai_service import call_ai_model, check_model_availability

def main():
    print("🧪 PROBANDO SERVICIO DE IA ACTUALIZADO")
    print("=" * 50)
    
    # Verificar disponibilidad del modelo
    model_available, message = check_model_availability()
    print(f"📊 Estado del modelo: {message}")
    
    if not model_available:
        print("❌ Modelo no disponible")
        return
    
    # Buscar imágenes de prueba
    test_dir = "data/processed_plant_images_completo_final/val"
    
    if not os.path.exists(test_dir):
        print(f"❌ No se encontró directorio de prueba: {test_dir}")
        return
    
    # Probar con algunas imágenes
    plantas_a_probar = ['agave', 'bugambilia', 'nopal', 'rosal']
    
    for planta in plantas_a_probar:
        planta_dir = os.path.join(test_dir, planta)
        
        if os.path.exists(planta_dir):
            imagenes = [f for f in os.listdir(planta_dir) 
                       if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
            
            if imagenes:
                imagen_path = os.path.join(planta_dir, imagenes[0])
                
                print(f"\n🌱 PROBANDO: {planta.upper()}")
                print(f"📸 Imagen: {imagenes[0]}")
                print("🔄 Analizando...")
                
                # Llamar al servicio de IA
                resultado = call_ai_model(imagen_path)
                
                print(f"🎯 Resultado: {resultado['disease_name']}")
                print(f"📊 Confianza: {resultado['confidence']:.2f}%")
                print(f"💡 Recomendaciones: {resultado['recommendations'][:100]}...")
                
                # Verificar si la predicción es correcta
                if planta in resultado['disease_name'].lower():
                    print("✅ ¡PREDICCIÓN CORRECTA!")
                else:
                    print("⚠️  Predicción diferente")
                
                print("-" * 50)
    
    print("\n🎉 ¡PRUEBA DEL SERVICIO DE IA COMPLETADA!")

if __name__ == '__main__':
    main()
