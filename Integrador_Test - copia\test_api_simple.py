#!/usr/bin/env python3
"""
Prueba simple de la API de plantas
"""

import requests
import json
import time

def test_api():
    """Probar la API de plantas"""
    
    print("🧪 PRUEBA SIMPLE DE LA API DE PLANTAS")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    # Probar que el servidor esté ejecutándose
    print("1. 🌐 Probando conexión al servidor...")
    try:
        response = requests.get(base_url, timeout=5)
        print(f"   ✅ Servidor responde: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error conectando al servidor: {e}")
        return
    
    # Probar endpoint de recomendaciones con GET
    print("\n2. 📋 Probando GET /api/plants/recommendations...")
    try:
        response = requests.get(f"{base_url}/api/plants/recommendations", timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            plants = data.get('plants', [])
            print(f"   ✅ Plantas obtenidas: {len(plants)}")
            if plants:
                print(f"   Primera planta: {plants[0].get('name', 'Sin nombre')}")
        else:
            print(f"   ❌ Error: {response.text}")
    except Exception as e:
        print(f"   💥 Error: {e}")
    
    # Probar endpoint de recomendaciones con POST
    print("\n3. 📋 Probando POST /api/plants/recommendations...")
    try:
        payload = {
            'experience_level': 'beginner',
            'sunlight': 'medium',
            'watering_frequency': 'low'
        }
        response = requests.post(f"{base_url}/api/plants/recommendations", 
                               json=payload, timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            plants = data.get('plants', [])
            print(f"   ✅ Plantas filtradas: {len(plants)}")
            if plants:
                print(f"   Primera planta: {plants[0].get('name', 'Sin nombre')}")
        else:
            print(f"   ❌ Error: {response.text}")
    except Exception as e:
        print(f"   💥 Error: {e}")
    
    # Probar endpoint de detalle de planta
    print("\n4. 🌱 Probando GET /api/plants/1...")
    try:
        response = requests.get(f"{base_url}/api/plants/1", timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            plant = data.get('plant', {})
            print(f"   ✅ Planta obtenida: {plant.get('name', 'Sin nombre')}")
        else:
            print(f"   ❌ Error: {response.text}")
    except Exception as e:
        print(f"   💥 Error: {e}")
    
    print(f"\n🎯 Prueba completada")

if __name__ == "__main__":
    test_api()
