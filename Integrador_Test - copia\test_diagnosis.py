#!/usr/bin/env python3
"""
Script de prueba para el diagnóstico de IA
"""

import requests
import json
import os
import time
from io import BytesIO

def test_diagnosis_api():
    """Prueba la API de diagnóstico"""
    
    print("🧪 Iniciando prueba del diagnóstico de IA")
    print("=" * 50)
    
    # URL base del servidor
    base_url = "http://127.0.0.1:5000"
    
    # 1. Verificar que el servidor esté funcionando
    try:
        response = requests.get(f"{base_url}/diagnosis/scanner", timeout=5)
        if response.status_code == 200:
            print("✅ Servidor funcionando correctamente")
        else:
            print(f"❌ Error del servidor: {response.status_code}")
            return
    except requests.exceptions.RequestException as e:
        print(f"❌ No se puede conectar al servidor: {e}")
        print("💡 Asegúrate de que el servidor esté ejecutándose en http://127.0.0.1:5000")
        return
    
    # 2. Crear una imagen de prueba
    print("\n📸 Creando imagen de prueba...")
    
    # Crear una imagen simple usando PIL
    try:
        from PIL import Image, ImageDraw
        
        # Crear una imagen de 300x300 píxeles
        img = Image.new('RGB', (300, 300), color='green')
        draw = ImageDraw.Draw(img)
        
        # Dibujar una "planta" simple
        draw.ellipse([50, 50, 250, 250], fill='darkgreen')
        draw.ellipse([100, 100, 200, 200], fill='lightgreen')
        draw.rectangle([140, 200, 160, 280], fill='brown')  # Tallo
        
        # Guardar en memoria
        img_buffer = BytesIO()
        img.save(img_buffer, format='JPEG')
        img_buffer.seek(0)
        
        print("✅ Imagen de prueba creada")
        
    except ImportError:
        print("⚠️ PIL no disponible, usando archivo de prueba existente")
        # Buscar una imagen existente
        test_image_path = None
        for ext in ['jpg', 'jpeg', 'png']:
            for folder in ['uploads/temp', 'uploads/diagnosis', '.']:
                for filename in os.listdir(folder) if os.path.exists(folder) else []:
                    if filename.lower().endswith(ext):
                        test_image_path = os.path.join(folder, filename)
                        break
                if test_image_path:
                    break
            if test_image_path:
                break
        
        if test_image_path and os.path.exists(test_image_path):
            with open(test_image_path, 'rb') as f:
                img_buffer = BytesIO(f.read())
            print(f"✅ Usando imagen existente: {test_image_path}")
        else:
            print("❌ No se encontró ninguna imagen de prueba")
            return
    
    # 3. Obtener token CSRF
    print("\n🔐 Obteniendo token CSRF...")
    try:
        scanner_response = requests.get(f"{base_url}/diagnosis/scanner")
        # Buscar el token CSRF en el HTML
        csrf_token = None
        if 'csrf-token' in scanner_response.text:
            import re
            match = re.search(r'content="([^"]+)"[^>]*name="csrf-token"', scanner_response.text)
            if match:
                csrf_token = match.group(1)
        
        if csrf_token:
            print("✅ Token CSRF obtenido")
        else:
            print("⚠️ No se pudo obtener token CSRF, continuando sin él")
    except Exception as e:
        print(f"⚠️ Error obteniendo CSRF: {e}")
        csrf_token = None
    
    # 4. Subir la imagen
    print("\n📤 Subiendo imagen para análisis...")
    
    try:
        files = {'file': ('test_plant.jpg', img_buffer, 'image/jpeg')}
        headers = {}
        
        if csrf_token:
            headers['X-CSRFToken'] = csrf_token
        
        upload_response = requests.post(
            f"{base_url}/diagnosis/upload",
            files=files,
            headers=headers,
            timeout=30
        )
        
        if upload_response.status_code == 200:
            result = upload_response.json()
            if result.get('success'):
                filename = result.get('filename')
                print(f"✅ Imagen subida exitosamente: {filename}")
                
                # 5. Obtener el resultado
                print("\n📊 Obteniendo resultado del análisis...")
                
                result_response = requests.get(f"{base_url}/diagnosis/result/{filename}")
                
                if result_response.status_code == 200:
                    print("✅ Resultado obtenido exitosamente")
                    print("\n🌱 RESULTADO DEL DIAGNÓSTICO:")
                    print("=" * 30)
                    
                    # Extraer información del HTML (método simple)
                    html = result_response.text
                    
                    # Buscar información en el HTML
                    if 'disease_name' in html:
                        print("✅ Página de resultado cargada correctamente")
                        print("🤖 La IA ha procesado la imagen")
                        print("📋 Se han generado recomendaciones personalizadas")
                        
                        # Abrir el resultado en el navegador
                        print(f"\n🌐 Abriendo resultado en navegador...")
                        print(f"URL: {base_url}/diagnosis/result/{filename}")
                        
                        return True
                    else:
                        print("⚠️ Página de resultado no contiene datos esperados")
                        
                else:
                    print(f"❌ Error obteniendo resultado: {result_response.status_code}")
                    
            else:
                print(f"❌ Error en la respuesta: {result.get('error', 'Error desconocido')}")
        else:
            print(f"❌ Error subiendo imagen: {upload_response.status_code}")
            try:
                error_data = upload_response.json()
                print(f"Detalles: {error_data.get('error', 'Error desconocido')}")
            except:
                print(f"Respuesta: {upload_response.text[:200]}...")
                
    except requests.exceptions.Timeout:
        print("⏰ Timeout - El análisis está tomando más tiempo del esperado")
    except Exception as e:
        print(f"❌ Error durante la prueba: {e}")
    
    return False

if __name__ == "__main__":
    success = test_diagnosis_api()
    
    if success:
        print("\n🎉 ¡PRUEBA EXITOSA!")
        print("✅ El diagnóstico de IA está funcionando correctamente")
        print("✅ Las recomendaciones se generan automáticamente")
        print("✅ La interfaz web está operativa")
    else:
        print("\n❌ La prueba falló")
        print("💡 Verifica que el servidor esté ejecutándose")
        print("💡 Revisa los logs del servidor para más detalles")
    
    print("\n" + "=" * 50)
