#!/usr/bin/env python3
"""
Script de prueba final para verificar que todo funciona correctamente
"""

import requests
import json

def test_all_endpoints():
    """Probar todos los endpoints relacionados con plantas"""
    
    base_url = "http://127.0.0.1:5000"
    
    print("🧪 Probando todos los endpoints de plantas...")
    
    # 1. Probar endpoint de recomendaciones (GET)
    print("\n1. 📡 Probando GET /api/plants/recommendations...")
    try:
        response = requests.get(f"{base_url}/api/plants/recommendations?page=1&sort=match", timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success: {data.get('success')}")
            print(f"   📊 Plants: {len(data.get('plants', []))}")
        else:
            print(f"   ❌ Error: {response.text}")
    except Exception as e:
        print(f"   💥 Error: {e}")
    
    # 2. Probar endpoint de recomendaciones (POST)
    print("\n2. 📡 Probando POST /api/plants/recommendations...")
    try:
        filters = {
            "experienceLevel": "beginner",
            "sunlight": "high",
            "wateringFrequency": "low"
        }
        response = requests.post(
            f"{base_url}/api/plants/recommendations?page=1&sort=match",
            json=filters,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success: {data.get('success')}")
            print(f"   📊 Plants: {len(data.get('plants', []))}")
        else:
            print(f"   ❌ Error: {response.text}")
    except Exception as e:
        print(f"   💥 Error: {e}")
    
    # 3. Probar endpoint de detalles de planta
    print("\n3. 📡 Probando GET /api/plants/1...")
    try:
        response = requests.get(f"{base_url}/api/plants/1", timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success: {data.get('success')}")
            plant = data.get('plant', {})
            print(f"   🌱 Plant: {plant.get('name')} ({plant.get('scientificName')})")
            print(f"   🏷️  Badges: Native={plant.get('isNative')}, Purifying={plant.get('isPurifying')}, Edible={plant.get('isEdible')}")
        else:
            print(f"   ❌ Error: {response.text}")
    except Exception as e:
        print(f"   💥 Error: {e}")
    
    # 4. Probar páginas web
    print("\n4. 🌐 Probando páginas web...")
    
    # Página de recomendaciones
    try:
        response = requests.get(f"{base_url}/recomendaciones", timeout=10)
        print(f"   📄 /recomendaciones - Status: {response.status_code}")
    except Exception as e:
        print(f"   💥 Error en /recomendaciones: {e}")
    
    # Página de detalle de planta
    try:
        response = requests.get(f"{base_url}/planta/1", timeout=10)
        print(f"   📄 /planta/1 - Status: {response.status_code}")
    except Exception as e:
        print(f"   💥 Error en /planta/1: {e}")
    
    # 5. Verificar imágenes
    print("\n5. 🖼️  Verificando imágenes...")
    
    # Cargar datos de plantas para verificar imágenes
    try:
        with open('data/json/plant_recommendations.json', 'r', encoding='utf-8') as f:
            plants_data = json.load(f)
        
        image_errors = []
        for plant in plants_data[:3]:  # Solo verificar las primeras 3
            image_url = plant.get('imageUrl', '')
            if image_url.startswith('/static/'):
                try:
                    response = requests.get(f"{base_url}{image_url}", timeout=5)
                    if response.status_code != 200:
                        image_errors.append(f"{plant['Nombre']}: {image_url}")
                except:
                    image_errors.append(f"{plant['Nombre']}: {image_url}")
        
        if image_errors:
            print(f"   ⚠️  Imágenes con problemas: {len(image_errors)}")
            for error in image_errors:
                print(f"      - {error}")
        else:
            print(f"   ✅ Todas las imágenes verificadas están disponibles")
            
    except Exception as e:
        print(f"   💥 Error verificando imágenes: {e}")
    
    print("\n🎉 Pruebas completadas!")

if __name__ == "__main__":
    test_all_endpoints()
