#!/usr/bin/env python3
"""
Script para simular exactamente lo que hace el frontend
"""

import requests
import json

def test_frontend_simulation():
    """Simular exactamente las peticiones del frontend"""
    
    base_url = "http://127.0.0.1:5000"
    endpoint = f"{base_url}/api/plants/recommendations"
    
    print("🧪 Simulando peticiones del frontend...")
    
    # Simular los filtros que envía getActiveFilters()
    filters = {
        "experienceLevel": "beginner",  # valor del select experience-level
        "sunlight": "high",             # valor del select sunlight
        "wateringFrequency": "low",     # valor del select watering-frequency
        "space": "small",               # valor del select space
        "purposes": {
            "decoration": True,         # checkbox purpose-decoration
            "purification": False,      # checkbox purpose-purification
            "garden": False,           # checkbox purpose-garden
            "native": True             # checkbox purpose-native
        }
    }
    
    print(f"Filtros a enviar: {json.dumps(filters, indent=2)}")
    
    # Probar POST como lo hace loadPlantPage()
    print("\n📡 Enviando POST request...")
    try:
        response = requests.post(
            f"{endpoint}?page=1&sort=match",
            json=filters,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success: {data.get('success')}")
            print(f"📊 Plants count: {len(data.get('plants', []))}")
            print(f"👤 User experience level: {data.get('userExperienceLevel')}")
            
            # Mostrar información de paginación
            pagination = data.get('pagination', {})
            print(f"📄 Pagination: Page {pagination.get('currentPage')} of {pagination.get('totalPages')}")
            print(f"📄 Total items: {pagination.get('totalItems')}")
            
            # Mostrar las primeras 3 plantas
            plants = data.get('plants', [])
            if plants:
                print(f"\n🌱 Primeras plantas:")
                for i, plant in enumerate(plants[:3]):
                    print(f"  {i+1}. {plant['name']} ({plant['scientificName']})")
                    print(f"     Match: {plant['matchPercentage']}% | Dificultad: {plant['difficulty']}")
                    print(f"     Agua: {plant['waterRequirement']} | Luz: {plant['sunlightRequirement']}")
                    badges = []
                    if plant.get('isNative'): badges.append('Nativa')
                    if plant.get('isPurifying'): badges.append('Purificadora')
                    if plant.get('isEdible'): badges.append('Comestible')
                    if badges:
                        print(f"     Badges: {', '.join(badges)}")
                    print()
            else:
                print("❌ No se encontraron plantas")
        else:
            print(f"❌ Error: {response.text}")
            
    except Exception as e:
        print(f"💥 Error en POST: {e}")

if __name__ == "__main__":
    test_frontend_simulation()
