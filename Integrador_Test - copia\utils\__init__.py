import os
from werkzeug.utils import secure_filename
import uuid

def save_image(file, folder):
    """
    Save an uploaded image file to the specified folder.
    
    Args:
        file: The uploaded file object from request.files
        folder: The subfolder to save the image in (e.g., 'diagnoses')
    
    Returns:
        str: The full path to the saved image file
    """
    # Create a secure filename to prevent path traversal attacks
    filename = secure_filename(file.filename)
    
    # Add a unique identifier to prevent filename collisions
    unique_filename = f"{uuid.uuid4()}_{filename}"
    
    # Define the upload directory
    upload_dir = os.path.join('static', 'uploads', folder)
    
    # Create the directory if it doesn't exist
    os.makedirs(upload_dir, exist_ok=True)
    
    # Build the full path
    filepath = os.path.join(upload_dir, unique_filename)
    
    # Save the file
    file.save(filepath)
    
    # Return the path
    return filepath

def get_image_path(filename, folder):
    """
    Get the path to an image file.
    
    Args:
        filename: The filename of the image
        folder: The subfolder where the image is stored
    
    Returns:
        str: The full path to the image file
    """
    return os.path.join('static', 'uploads', folder, filename)