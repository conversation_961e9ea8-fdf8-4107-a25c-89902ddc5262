"""
Utilidades para funciones de administrador
"""

from functools import wraps
from flask import flash, redirect, url_for, abort
from flask_login import current_user
import json
import os

def load_users():
    """Cargar usuarios desde el archivo JSON"""
    try:
        users_file = os.path.join('data', 'json', 'users.json')
        if os.path.exists(users_file):
            with open(users_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return []
    except Exception as e:
        print(f"Error cargando usuarios: {e}")
        return []

def save_users(users_data):
    """Guardar usuarios en el archivo JSON"""
    try:
        users_file = os.path.join('data', 'json', 'users.json')
        os.makedirs(os.path.dirname(users_file), exist_ok=True)
        with open(users_file, 'w', encoding='utf-8') as f:
            json.dump(users_data, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"Error guardando usuarios: {e}")
        return False

def is_admin(user_id=None):
    """Verificar si un usuario es administrador"""
    if user_id is None and current_user.is_authenticated:
        user_id = current_user.id
    
    if not user_id:
        return False
    
    users = load_users()
    for user in users:
        if str(user.get('UsuarioID')) == str(user_id):
            return user.get('IsAdmin', False) or user.get('RolID') == 1
    
    return False

def admin_required(f):
    """Decorador para requerir permisos de administrador"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('Debes iniciar sesión para acceder a esta página.', 'warning')
            return redirect(url_for('login'))
        
        if not is_admin():
            flash('No tienes permisos de administrador para acceder a esta página.', 'danger')
            return redirect(url_for('index'))
        
        return f(*args, **kwargs)
    return decorated_function

def get_user_role(user_id):
    """Obtener el rol de un usuario"""
    users = load_users()
    for user in users:
        if str(user.get('UsuarioID')) == str(user_id):
            if user.get('IsAdmin', False) or user.get('RolID') == 1:
                return 'admin'
            else:
                return 'user'
    return 'guest'

def load_plants():
    """Cargar plantas desde el archivo JSON"""
    try:
        plants_file = os.path.join('data', 'json', 'plants.json')
        if os.path.exists(plants_file):
            with open(plants_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return []
    except Exception as e:
        print(f"Error cargando plantas: {e}")
        return []

def save_plants(plants_data):
    """Guardar plantas en el archivo JSON"""
    try:
        plants_file = os.path.join('data', 'json', 'plants.json')
        os.makedirs(os.path.dirname(plants_file), exist_ok=True)
        with open(plants_file, 'w', encoding='utf-8') as f:
            json.dump(plants_data, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"Error guardando plantas: {e}")
        return False

def get_next_plant_id():
    """Obtener el siguiente ID disponible para una planta"""
    plants = load_plants()
    if not plants:
        return 1
    
    max_id = max(plant.get('PlantaID', 0) for plant in plants)
    return max_id + 1

def add_plant(plant_data):
    """Agregar una nueva planta"""
    try:
        plants = load_plants()
        
        # Asignar ID automáticamente
        plant_data['PlantaID'] = get_next_plant_id()
        
        # Agregar metadatos
        from datetime import datetime
        plant_data['FechaCreacion'] = datetime.now().isoformat()
        plant_data['CreadoPor'] = current_user.id if current_user.is_authenticated else 1
        plant_data['Activo'] = True
        
        # Agregar a la lista
        plants.append(plant_data)
        
        # Guardar
        if save_plants(plants):
            return True, plant_data['PlantaID']
        else:
            return False, "Error al guardar la planta"
            
    except Exception as e:
        return False, str(e)

def update_plant(plant_id, plant_data):
    """Actualizar una planta existente"""
    try:
        plants = load_plants()
        
        for i, plant in enumerate(plants):
            if plant.get('PlantaID') == plant_id:
                # Mantener metadatos originales
                plant_data['PlantaID'] = plant_id
                plant_data['FechaCreacion'] = plant.get('FechaCreacion')
                plant_data['CreadoPor'] = plant.get('CreadoPor')
                
                # Actualizar fecha de modificación
                from datetime import datetime
                plant_data['FechaModificacion'] = datetime.now().isoformat()
                plant_data['ModificadoPor'] = current_user.id if current_user.is_authenticated else 1
                
                # Reemplazar planta
                plants[i] = plant_data
                
                # Guardar
                if save_plants(plants):
                    return True, "Planta actualizada exitosamente"
                else:
                    return False, "Error al guardar los cambios"
        
        return False, "Planta no encontrada"
        
    except Exception as e:
        return False, str(e)

def delete_plant(plant_id):
    """Eliminar una planta (marcar como inactiva)"""
    try:
        plants = load_plants()
        
        for plant in plants:
            if plant.get('PlantaID') == plant_id:
                plant['Activo'] = False
                
                # Agregar metadatos de eliminación
                from datetime import datetime
                plant['FechaEliminacion'] = datetime.now().isoformat()
                plant['EliminadoPor'] = current_user.id if current_user.is_authenticated else 1
                
                # Guardar
                if save_plants(plants):
                    return True, "Planta eliminada exitosamente"
                else:
                    return False, "Error al guardar los cambios"
        
        return False, "Planta no encontrada"
        
    except Exception as e:
        return False, str(e)
