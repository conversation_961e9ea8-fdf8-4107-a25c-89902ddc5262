from flask import jsonify
import datetime

def success_response(data=None, message=None):
    """
    Create a standardized success response
    
    Args:
        data: Data to include in the response
        message (str): Message to include in the response
    
    Returns:
        flask.Response: JSON response
    """
    response = {
        'success': True,
        'timestamp': datetime.datetime.utcnow().isoformat()
    }
    
    if data is not None:
        response['data'] = data
        
    if message is not None:
        response['message'] = message
    
    return jsonify(response)

def error_response(message, error_code=400):
    """
    Create a standardized error response
    
    Args:
        message (str): Error message
        error_code (int): HTTP error code
    
    Returns:
        flask.Response: JSON response
    """
    response = {
        'success': False,
        'error': message,
        'timestamp': datetime.datetime.utcnow().isoformat()
    }
    
    return jsonify(response), error_code

def format_model_to_dict(model_instance, exclude_fields=None):
    """
    Convert a SQLAlchemy model instance to a dictionary
    
    Args:
        model_instance: SQLAlchemy model instance
        exclude_fields (list): Fields to exclude from the result
    
    Returns:
        dict: Dictionary representation of the model
    """
    if exclude_fields is None:
        exclude_fields = []
    
    result = {}
    for column in model_instance.__table__.columns:
        if column.name not in exclude_fields:
            value = getattr(model_instance, column.name)
            
            # Handle special types
            if isinstance(value, datetime.datetime):
                value = value.isoformat()
            elif isinstance(value, datetime.date):
                value = value.isoformat()
            elif isinstance(value, bytes):
                continue  # Skip binary data
            
            result[column.name] = value
    
    return result