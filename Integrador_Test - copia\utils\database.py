from database import db
from sqlalchemy.exc import SQLAlchemyError
import logging

logger = logging.getLogger(__name__)

def execute_stored_procedure(procedure_name, *args):
    """
    Execute a stored procedure in the database
    
    Args:
        procedure_name (str): Name of the stored procedure
        *args: Arguments to pass to the stored procedure
    
    Returns:
        tuple: (success, result or error message)
    """
    try:
        # Construct the SQL query
        params = ', '.join(['?' for _ in args])
        query = f"EXEC {procedure_name} {params}"
        
        # Execute the query
        result = db.engine.execute(query, *args).fetchall()
        
        return True, result
    except SQLAlchemyError as e:
        logger.error(f"Error executing stored procedure {procedure_name}: {str(e)}")
        return False, str(e)

def insert_with_identity(table, **kwargs):
    """
    Insert a record and return the new ID
    
    Args:
        table: SQLAlchemy table model
        **kwargs: Column values to insert
    
    Returns:
        tuple: (success, new_id or error message)
    """
    try:
        new_record = table(**kwargs)
        db.session.add(new_record)
        db.session.flush()  # Flush to get the ID without committing
        new_id = getattr(new_record, table.__table__.primary_key.columns.values()[0].name)
        db.session.commit()
        return True, new_id
    except SQLAlchemyError as e:
        db.session.rollback()
        logger.error(f"Error inserting into {table.__tablename__}: {str(e)}")
        return False, str(e)
