import os
import uuid
from werkzeug.utils import secure_filename

ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def save_image(file, subfolder='general'):
    if file and allowed_file(file.filename):
        # Generate unique filename
        original_filename = secure_filename(file.filename)
        extension = original_filename.rsplit('.', 1)[1].lower()
        new_filename = f"{uuid.uuid4().hex}.{extension}"
        
        # Get the upload folder from current app config
        from flask import current_app
        upload_folder = current_app.config['UPLOAD_FOLDER']
        
        # Create subfolder if it doesn't exist
        upload_path = os.path.join(upload_folder, subfolder)
        os.makedirs(upload_path, exist_ok=True)
        
        # Save the file
        file_path = os.path.join(upload_path, new_filename)
        file.save(file_path)
        
        return file_path
    
    return None

def get_image_path(filename, subfolder='general'):
    from flask import current_app
    return os.path.join(current_app.config['UPLOAD_FOLDER'], subfolder, filename)
